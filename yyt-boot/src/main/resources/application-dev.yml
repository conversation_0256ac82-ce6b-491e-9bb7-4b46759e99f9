spring:
  data:
    redis:
      host: yyt-redis
  # 数据库相关配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      username: root
      password: root
      url: ***********************************************************************************************************************************************************************************************************************************************************************************


# 文件上传配置
file:
  local:
    enable: true
    base-path: /Users/<USER>/Downloads/img

# 验证码配置
aj:
  captcha:
    water-mark: pig4cloud

# 登录报文加密根密钥 ，必须是16位
security:
  encodeKey: pigxpigxpigxpigx

# 配置文件加密根密码
jasypt:
  encryptor:
    password: yyt

# swagger token url 配置
swagger:
  token-url: ${swagger.gateway}/admin/oauth2/token

