package com.yts.yyt.ticket.api.dto;

import com.yts.yyt.common.core.validation.UrlValidation;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 管理端编辑门票请求参数
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "管理端编辑门票请求参数")
public class TicketAdminEditDTO {

	/**
	 * 主键ID
	 */
	@Schema(description="主键ID")
	@NotNull(message = "id不能为空")
	private Long id;

	/**
	 * 时段（多个）
	 */
	@Schema(description="时段（多个）")
	@NotNull(message = "门票明细不能为空")
	private List<TicketAdminTimeSlotDTO> details;

    /**
     * 最少购票数
     */
    @Schema(description = "最少购票数", example = "1")
    private Integer minNums = 1;

    /**
     * 最多购票数
     */
    @Schema(description = "最多购票数", example = "10")
    private Integer maxNums = 10;

	/**
	 * 价格
	 */
	@PositiveOrZero(message = "价格必须大于等于0")
	@Schema(description = "价格", example = "100.00")
	private BigDecimal unitPrice;

	/**
	 * 门票样式图片URL
	 */
	@Schema(description="门票样式图片URL")
	@UrlValidation
	private String ticketImage;

    /**
     * 状态
     */
    @Schema(description = "状态：0-启用 1-禁用", example = "0")
    private Integer status = 0;
} 