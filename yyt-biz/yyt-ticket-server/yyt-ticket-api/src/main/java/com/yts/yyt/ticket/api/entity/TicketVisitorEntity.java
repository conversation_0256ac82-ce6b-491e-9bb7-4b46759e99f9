package com.yts.yyt.ticket.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@TableName("ticket_visitor")
@Schema(description = "游客信息表")
public class TicketVisitorEntity {

    /**
     * 主键ID
     */
    @Schema(description="主键ID")
	@TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 用户id
     */
    @Schema(description="用户id")
    private Long userId;
    /**
     * 游客姓名
     */
    @Schema(description="游客姓名")
    private String visitorName;
    /**
     * 字典配置 证件类型
     */
    @Schema(description="字典配置 证件类型")
    private String idType;
    /**
     * 证件号码
     */
    @Schema(description="证件号码")
    private String idNumber;
    /**
     * 创建时间
     */
    @Schema(description="创建时间")
	@TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

	/**
	 * 删除标志 0-正常、1-删除
	 */
	@TableLogic
	@TableField(fill = FieldFill.INSERT)
	@Schema(description = "删除标志 0-正常、1-删除")
	private Integer delFlag;

}

