package com.yts.yyt.ticket.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class TicketVerificationExportDTO {

	@Schema(description = "订单状态(1-待支付 2-待使用 3-已使用 4-已过期 5-已关闭 6-退款中 7-退票/退款")
//	@NotNull(message = "订单状态必填")
	private Integer status;

	@Schema(description = "参观日期开始时间", example = "2023-06-01 00:00:00")
//	@NotBlank(message = "开始时间-结束时间必填")
	private LocalDateTime startTime;

	@Schema(description = "参观日期结束时间", example = "2023-06-30 00:00:00")
//	@NotBlank(message = "开始时间-结束时间必填")
	private LocalDateTime endTime;
}