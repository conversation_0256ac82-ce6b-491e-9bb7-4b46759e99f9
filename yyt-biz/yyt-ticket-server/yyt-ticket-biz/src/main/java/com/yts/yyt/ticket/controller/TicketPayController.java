package com.yts.yyt.ticket.controller;

import com.google.common.net.HttpHeaders;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.log.annotation.SysLog;
import com.yts.yyt.common.security.annotation.Inner;
import com.yts.yyt.common.swagger.base.BaseController;
import com.yts.yyt.ticket.api.dto.TicketOrderPayCallbackDTO;
import com.yts.yyt.ticket.api.dto.TicketPayParameterDTO;
import com.yts.yyt.ticket.api.vo.TicketPayParameterVO;
import com.yts.yyt.ticket.service.TicketOrderPayCallbackService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
@RequestMapping("/pay")
@Tag(description = "TicketPayCallbackController", name = "票务支付控制层")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TicketPayController extends BaseController {

	private final TicketOrderPayCallbackService ticketOrderPayCallbackService;

	/**
	 * 支付回调
	 *
	 * @param dto 支付回调参数
	 * @return 支付回调结果
	 */
	@PostMapping("/callback")
	@Operation(summary = "支付回调", description = "")
	@SysLog("支付回调")
	@Inner
	public R<Boolean> payCallback(@RequestBody TicketOrderPayCallbackDTO dto) {
		return R.ok(this.ticketOrderPayCallbackService.payCallback(dto));
	}

	/**
	 * 获取支付参数
	 *
	 * @param dto dto
	 * @return 支付参数
	 */
	@PostMapping("/params")
	@Operation(summary = "获取支付参数")
	@SysLog("获取支付参数")
	@Inner
	public R<TicketPayParameterVO> getOrderPayParams(@RequestBody TicketPayParameterDTO dto) {
		return R.ok(this.ticketOrderPayCallbackService.getPayParams(dto.getOrderId(), dto.getOpenId()));
	}
}

