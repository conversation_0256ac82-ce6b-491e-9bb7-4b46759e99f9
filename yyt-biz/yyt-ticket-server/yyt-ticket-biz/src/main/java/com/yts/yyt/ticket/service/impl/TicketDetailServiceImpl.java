package com.yts.yyt.ticket.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yts.yyt.common.core.constant.enums.DeletedEnum;
import com.yts.yyt.common.core.dto.IdDTO;
import com.yts.yyt.ticket.api.entity.TicketDetailEntity;
import com.yts.yyt.ticket.api.entity.TicketInfoEntity;
import com.yts.yyt.ticket.api.exception.TicketErrorEnum;
import com.yts.yyt.ticket.api.exception.TicketException;
import com.yts.yyt.ticket.api.vo.TicketDetailVOForPurchase;
import com.yts.yyt.ticket.mapper.TicketDetailMapper;
import com.yts.yyt.ticket.service.TicketDetailService;
import com.yts.yyt.ticket.service.TicketInfoService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service("ticketDetailService")
public class TicketDetailServiceImpl extends ServiceImpl<TicketDetailMapper, TicketDetailEntity>
 implements TicketDetailService {
	@Autowired
	@Lazy
	private TicketInfoService ticketInfoService;

	@Override
	public TicketDetailEntity queryById(Long id){
		if (ObjUtil.isNull(id)) {
			throw TicketException.build(TicketErrorEnum.PARAM_ERROR);
		}
		TicketDetailEntity entity = this.getById(id);
		if (entity == null){
			throw TicketException.build(TicketErrorEnum.TICKET_NOT_EXIST);
		}
		return entity;
	}

	@Override
	public List<TicketDetailEntity> listByTicketId(Long id) {
		List<TicketDetailEntity> list = this.list(
				new LambdaQueryWrapper<TicketDetailEntity>()
						.eq(TicketDetailEntity::getTicketId, id)
						.eq(TicketDetailEntity::getDelFlag, DeletedEnum.UN_DELETE.getType())
						.orderByAsc(TicketDetailEntity::getStartTime)
		);
		if (CollUtil.isEmpty(list)) {
			return Collections.emptyList();
		}
		return list;
	}

	@Override
	public TicketDetailVOForPurchase infoDetailForPurchase(IdDTO dto) {
		if(StrUtil.isBlank(dto.getId())){
			throw TicketException.build(TicketErrorEnum.PARAM_ERROR);
		}
		TicketDetailEntity detail = this.queryById(Long.parseLong(dto.getId()));
		if (ObjUtil.isNull(detail)) {
			throw TicketException.build(TicketErrorEnum.TICKET_NOT_EXIST);
		}
		TicketDetailVOForPurchase vo = new TicketDetailVOForPurchase();
		BeanUtils.copyProperties(detail, vo);
		vo.setTimeSlot(detail.getStartTime() + "-" + detail.getEndTime());
		TicketInfoEntity infoEntity = ticketInfoService.getById(detail.getTicketId());
		if  (infoEntity != null) {
			vo.setTicketImage(infoEntity.getTicketImage());
			vo.setUnitPrice(infoEntity.getUnitPrice().toString());
		}
		return vo;
	}
}
