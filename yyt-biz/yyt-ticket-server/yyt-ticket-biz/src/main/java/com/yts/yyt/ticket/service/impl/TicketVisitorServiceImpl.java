package com.yts.yyt.ticket.service.impl;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tencentcloudapi.faceid.v20180301.models.IdCardVerificationResponse;
import com.yts.yyt.common.core.constant.enums.DeletedEnum;
import com.yts.yyt.common.core.dto.IdDTO;
import com.yts.yyt.common.core.exception.GlobalBizException;
import com.yts.yyt.common.eid.EIDService;
import com.yts.yyt.common.security.util.SecurityUtils;
import com.yts.yyt.ticket.api.dto.TicketVisitorDTO;
import com.yts.yyt.ticket.api.dto.TicketVisitorEditDTO;
import com.yts.yyt.ticket.api.entity.TicketVisitorEntity;
import com.yts.yyt.ticket.api.enums.TicketVisitorIdTypeEnum;
import com.yts.yyt.ticket.api.exception.TicketErrorEnum;
import com.yts.yyt.ticket.api.exception.TicketException;
import com.yts.yyt.ticket.api.vo.TicketVisitorVO;
import com.yts.yyt.ticket.mapper.VisitorInfoMapper;
import com.yts.yyt.ticket.service.TicketVisitorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service("visitorInfoService")
public class TicketVisitorServiceImpl extends ServiceImpl<VisitorInfoMapper, TicketVisitorEntity>
		implements TicketVisitorService {

	@Autowired
	private EIDService eidService;

	@Override
	public Boolean addVisitor(TicketVisitorDTO dto) {

		String idType = dto.getIdType();
		if(!TicketVisitorIdTypeEnum.contains(idType)) {
			throw TicketException.build(TicketErrorEnum.PARAM_ERROR);
		}

		// 身份证格式校验
		checkIdCard(dto.getIdType(), dto.getVisitorName(), dto.getIdNumber());

		Long userId = SecurityUtils.getUser().getId();

		// 判断是否存在
		LambdaQueryWrapper<TicketVisitorEntity> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(TicketVisitorEntity::getUserId, userId);
		queryWrapper.eq(TicketVisitorEntity::getIdType, dto.getIdType());
		queryWrapper.eq(TicketVisitorEntity::getVisitorName, dto.getVisitorName());
		queryWrapper.eq(TicketVisitorEntity::getIdNumber, dto.getIdNumber());
		if (this.count(queryWrapper) > 0) {
			// 游客信息已存在
			throw TicketException.build(TicketErrorEnum.VISITOR_EXIST);
		}
		TicketVisitorEntity entity = new TicketVisitorEntity();
		entity.setUserId(userId);
		entity.setVisitorName(dto.getVisitorName());
		entity.setIdNumber(dto.getIdNumber());
		entity.setIdType(dto.getIdType());
		entity.setCreateTime(LocalDateTime.now());
		entity.setDelFlag(DeletedEnum.UN_DELETE.getType());
		return this.save(entity);
	}

	@Override
	public List<TicketVisitorVO> queryVisitor() {
		Long userId = SecurityUtils.getUser().getId();
		LambdaQueryWrapper<TicketVisitorEntity> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(TicketVisitorEntity::getUserId, userId);
		List<TicketVisitorEntity> list = this.list(queryWrapper);
		return list.stream().map(i -> {
			TicketVisitorVO vo = new TicketVisitorVO();
			vo.setId(i.getId());
			vo.setVisitorName(i.getVisitorName());
			vo.setIdNumber(i.getIdNumber());
			vo.setIdType(i.getIdType());
			vo.setIdTypeName(TicketVisitorIdTypeEnum.getByCode((i.getIdType())).getDesc());
			return vo;
		}).collect(Collectors.toList());
	}

	@Override
	public Boolean editVisitor(TicketVisitorEditDTO dto) {
		Long userId = SecurityUtils.getUser().getId();
		// 判断是否存在
		LambdaQueryWrapper<TicketVisitorEntity> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(TicketVisitorEntity::getUserId, userId);
		queryWrapper.eq(TicketVisitorEntity::getId, dto.getId());
		if (this.count(queryWrapper) == 0) {
			throw TicketException.build(TicketErrorEnum.PARAM_ERROR);
		}

		// 身份证格式校验
		checkIdCard(dto.getIdType(), dto.getVisitorName(), dto.getIdNumber());

		LambdaUpdateWrapper<TicketVisitorEntity> updateWrapper = new LambdaUpdateWrapper<>();
		updateWrapper.eq(TicketVisitorEntity::getUserId, userId);
		updateWrapper.eq(TicketVisitorEntity::getId, dto.getId());
		updateWrapper.set(StringUtils.isNotBlank(dto.getVisitorName()), TicketVisitorEntity::getVisitorName, dto.getVisitorName());
		updateWrapper.set(StringUtils.isNotBlank(dto.getIdNumber()), TicketVisitorEntity::getIdNumber, dto.getIdNumber());
		updateWrapper.set(StringUtils.isNotBlank(dto.getIdType()), TicketVisitorEntity::getIdType, dto.getIdType());
		return this.update(updateWrapper);
	}

	private void checkIdCard(String idType, String visitorName, String idNumber2) {
		if (idType.equals(TicketVisitorIdTypeEnum.ID_CARD.getCode())) {
			String name = visitorName;
			String idNumber = idNumber2;
			IdCardVerificationResponse check = eidService.IdCardVerification(name, idNumber);
			if (!check.getResult().equals("0")) {
				throw new GlobalBizException(check.getDescription());
			}
		}
	}

	@Override
	public Boolean deleteVisitor(IdDTO dto) {
		return this.removeById(dto.getId());
	}
}
