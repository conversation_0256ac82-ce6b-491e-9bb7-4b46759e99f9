package com.yts.yyt.ticket.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yts.yyt.common.core.dto.IdDTO;
import com.yts.yyt.ticket.api.entity.TicketDetailEntity;
import com.yts.yyt.ticket.api.vo.TicketDetailVOForPurchase;

import java.util.List;

public interface TicketDetailService extends IService<TicketDetailEntity> {

	TicketDetailEntity queryById(Long id);

	List<TicketDetailEntity> listByTicketId(Long id);

	TicketDetailVOForPurchase infoDetailForPurchase(IdDTO dto);
}

