package com.yts.yyt.ticket.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yts.yyt.common.data.datascope.YytBaseMapper;
import com.yts.yyt.ticket.api.dto.TicketAdminStatisticsDTO;
import com.yts.yyt.ticket.api.entity.TicketInfoEntity;
import com.yts.yyt.ticket.api.vo.TicketAdminStatisticsVO;
import com.yts.yyt.ticket.api.vo.TicketDetailInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TicketInfoMapper extends YytBaseMapper<TicketInfoEntity> {

	Page<TicketAdminStatisticsVO> pageStatistics(Page pageParam, @Param("p") TicketAdminStatisticsDTO queryDto);

	/**
	 * 使用悲观锁查询门票信息
	 *
	 * @param id 门票ID
	 * @return 门票信息实体
	 */
	TicketDetailInfoVO selectByIdForUpdate(@Param("id") Long id);

	/**
	 * 增加门票库存
	 *
	 * @param id       门票ID
	 * @param quantity 要增加的数量
	 * @return 是否更新成功
	 */
	boolean incrementRemainingQuantity(@Param("id") Long id, @Param("quantity") Integer quantity);
	
	/**
	 * 减少门票库存
	 *
	 * @param id       门票ID
	 * @param quantity 要减少的数量
	 * @return 是否更新成功
	 */
	boolean decrementRemainingQuantity(@Param("id") Long id, @Param("quantity") Integer quantity);
	
	/**
	 * 查询从今天开始到未来7天内的所有有效门票信息（包含时段详情）
	 * 
	 * @return 门票详情信息列表
	 */
	List<TicketDetailInfoVO> listTicketDetailInfoFromTodayToNext7Days();

}

