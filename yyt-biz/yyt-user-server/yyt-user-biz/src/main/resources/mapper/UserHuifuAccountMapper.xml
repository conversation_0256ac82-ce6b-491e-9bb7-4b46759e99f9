<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yts.yyt.user.mapper.UserHuifuAccountMapper">

    <resultMap type="com.yts.yyt.user.api.entity.UserHuifuAccount" id="UserHuifuAccountMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="sysUserId" column="sys_user_id" jdbcType="INTEGER"/>
        <result property="merchantId" column="merchant_id" jdbcType="INTEGER"/>
        <result property="huifuId" column="huifu_id" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="errorInfo" column="error_info" jdbcType="VARCHAR"/>
        <result property="retryCount" column="retry_count" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
    </resultMap>

</mapper>