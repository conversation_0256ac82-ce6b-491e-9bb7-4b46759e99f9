<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yts.yyt.user.mapper.UserInfoMapper">

    <select id="checkExistRegisterRank" resultType="java.lang.Boolean">
        SELECT COUNT(id) FROM (
          SELECT id FROM `user_info` WHERE is_delete = 0 ORDER BY reg_time ASC, reg_num ASC LIMIT #{rank}
      ) AS t WHERE id = #{userId}
    </select>

    <!--    分页获取用户会员列表信息-->
    <select id="pageUserVipInfoList" resultType="com.yts.yyt.user.api.vo.UserVipInfoVO">
        SELECT 
            ui.id,ui.nickname,ui.mobile,
               ui.username,ui.head_img,ui.reg_time,ui.login_type_name,ui.login_time,ui.status,
            COALESCE(acc.total_balance, '0') AS accountBalance
        FROM user_info ui
        LEFT JOIN account acc ON ui.id = acc.user_id AND acc.account_type = 'BALANCE_ACCOUNT' AND acc.del_flag = '0'
        ${ew.getCustomSqlSegment}
    </select>
    
    <!-- 获取用户邀请列表及认证状态 -->
    <select id="getUserInviteList" resultType="com.yts.yyt.user.api.vo.UserInviteListVO$UserInviteVO">
        SELECT 
            ui.id AS userId,
            ui.nickname AS nickName,
            ui.username AS userName,
            ui.head_img AS headImg,
            CASE WHEN ua.id IS NOT NULL THEN true ELSE false END AS isIdAuth
        FROM 
            user_info ui
        LEFT JOIN 
            user_authentication ua ON ui.id = ua.user_id AND ua.auth_type = 'id_auth'
        WHERE
            ui.invite_id = #{inviteId}
            AND ui.is_delete = 0
        ORDER BY 
            ui.reg_time DESC
    </select>
</mapper>