package com.yts.yyt.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yts.yyt.common.tencent.vo.AccountCheckResultVO;
import com.yts.yyt.common.tencent.dto.IMAccountImportDTO;
import com.yts.yyt.user.api.entity.UserImInfoEntity;
import com.yts.yyt.user.api.vo.MultiAccountImportResultVO;

import java.util.List;

public interface UserImInfoService extends IService<UserImInfoEntity> {

	/**
	 * 根据用户ID获取用户IM信息
	 * @param userId 用户ID
	 * @return 用户IM信息
	 */
	UserImInfoEntity getUserImInfoByUserId(Long userId);

	/**
	 * 获取用户IM签名
	 * @param userId 用户ID
	 * @return 用户签名
	 */
	String getUserSign(Long userId);

	/**
	 * 查询用户IM状态
	 *
	 * @param userIds 用户ID列表
	 * @return 用户IM状态列表
	 */
	List<AccountCheckResultVO> checkUserStatus(String... userIds);

	/**
	 * 设置用户IM账号失效
	 * @param userId 用户ID
	 * @return 设置结果
	 */
	Boolean setUserImInvalid(Long userId);

	/**
	 *  创建IM用户
	 * @param userId	用户ID - 唯一
	 * @param username	用户名
	 * @param headImg	头像
	 */
	void importUser(Long userId,String username,String headImg);

	/**
	 * 批量导入IM账号
	 *
	 * @param accountList 账号列表
	 * @return 导入结果，包含失败的账号列表
	 */
	MultiAccountImportResultVO multiAccountImport(List<IMAccountImportDTO.AccountInfo> accountList);

}
