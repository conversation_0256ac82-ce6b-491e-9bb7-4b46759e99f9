package com.yts.yyt.user.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yts.yyt.user.api.dto.UserMsgTemplateInfoDto;
import com.yts.yyt.user.api.entity.UserMsgTemplateEntity;


public interface UserMsgTemplateService extends IService<UserMsgTemplateEntity> {

    /**
     * 用户消息模板 分页查询
     * @param page
     * @param wrapper
     * @param userMsgTemplate
     * @return
     */
    Page<UserMsgTemplateEntity> getUserMsgTemplatePage(Page page,
                                                       LambdaQueryWrapper<UserMsgTemplateEntity> wrapper,
                                                       UserMsgTemplateEntity userMsgTemplate);

    UserMsgTemplateEntity saveUserMsgTemplateInfo(UserMsgTemplateInfoDto dto);
}
