package com.yts.yyt.user.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.log.annotation.SysLog;
import com.yts.yyt.common.security.util.SecurityUtils;
import com.yts.yyt.common.swagger.base.BaseController;
import com.yts.yyt.user.api.dto.UserMsgTemplateInfoDto;
import com.yts.yyt.user.api.entity.UserMsgTemplateEntity;
import com.yts.yyt.user.service.UserMsgTemplateService;
import jakarta.validation.Valid;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import com.yts.yyt.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 用户消息模板
 *
 * <AUTHOR>
 * @date 2025-03-03 18:01:41
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/userMsgTemplate" )
@Tag(description = "userMsgTemplate" , name = "用户消息模板管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class UserMsgTemplateController extends BaseController  {

    private final UserMsgTemplateService userMsgTemplateService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param userMsgTemplate 用户消息模板
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
//    @PreAuthorize("@pms.hasPermission('admin_userMsgTemplate_view')" )
    public R<Page<UserMsgTemplateEntity>> getUserMsgTemplatePage(@ParameterObject Page page, @ParameterObject UserMsgTemplateEntity userMsgTemplate) {
        LambdaQueryWrapper<UserMsgTemplateEntity> wrapper = Wrappers.lambdaQuery();

        return R.ok(userMsgTemplateService.getUserMsgTemplatePage(page, wrapper,userMsgTemplate));
    }


    /**
     * 通过id查询用户消息模板
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
//    @PreAuthorize("@pms.hasPermission('admin_userMsgTemplate_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(userMsgTemplateService.getById(id));
    }

    /**
     * 新增用户消息模板
     * @param dto 用户消息模板
     * @return R
     */
    @Operation(summary = "新增用户消息模板" , description = "新增用户消息模板" )
    @SysLog("新增用户消息模板" )
    @PostMapping
//    @PreAuthorize("@pms.hasPermission('admin_userMsgTemplate_add')" )
    public R<UserMsgTemplateEntity> saveUserMsgTemplateInfo(@Valid @RequestBody UserMsgTemplateInfoDto dto) {
        return R.ok(userMsgTemplateService.saveUserMsgTemplateInfo(dto));
    }

    /**
     * 修改用户消息模板
     * @param dto 用户消息模板
     * @return R
     */
    @Operation(summary = "修改用户消息模板" , description = "修改用户消息模板" )
    @SysLog("修改用户消息模板" )
    @PutMapping
//    @PreAuthorize("@pms.hasPermission('admin_userMsgTemplate_edit')" )
    public R updateById(@Valid @RequestBody UserMsgTemplateInfoDto dto) {
        UserMsgTemplateEntity add = new UserMsgTemplateEntity();
        BeanUtils.copyProperties(dto,add);
        add.setUpdateBy(String.valueOf(SecurityUtils.getUser().getId()));
        add.setUpdateTime(LocalDateTime.now());
        return R.ok(userMsgTemplateService.updateById(add));
    }

    /**
     * 通过id删除用户消息模板
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除用户消息模板" , description = "通过id删除用户消息模板" )
    @SysLog("通过id删除用户消息模板" )
    @DeleteMapping
//    @PreAuthorize("@pms.hasPermission('admin_userMsgTemplate_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(userMsgTemplateService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param userMsgTemplate 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
//    @PreAuthorize("@pms.hasPermission('admin_userMsgTemplate_export')" )
    public List<UserMsgTemplateEntity> export(UserMsgTemplateEntity userMsgTemplate,Long[] ids) {
        return userMsgTemplateService.list(Wrappers.lambdaQuery(userMsgTemplate).in(ArrayUtil.isNotEmpty(ids), UserMsgTemplateEntity::getId, ids));
    }
}
