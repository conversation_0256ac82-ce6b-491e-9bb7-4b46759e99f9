package com.yts.yyt.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yts.yyt.admin.api.constant.SmsBizCodeEnum;
import com.yts.yyt.admin.api.dto.MessageSmsDTO;
import com.yts.yyt.admin.api.feign.RemoteMessageService;
import com.yts.yyt.common.core.constant.CacheConstants;
import com.yts.yyt.common.core.constant.CommonConstants;
import com.yts.yyt.common.core.constant.SecurityConstants;
import com.yts.yyt.common.core.dto.IdDTO;
import com.yts.yyt.common.core.exception.ErrorCodes;
import com.yts.yyt.common.core.util.*;
import com.yts.yyt.common.data.resolver.ParamResolver;
import com.yts.yyt.common.security.service.YytRedisOAuth2AuthorizationService;
import com.yts.yyt.distribution.api.vo.DistUserTeamInfoVO;
import com.yts.yyt.merchant.api.dto.MerchantDTO;
import com.yts.yyt.merchant.api.dto.UserRegisterSetUserIdDTO;
import com.yts.yyt.order.api.enums.CouponEnum;
import com.yts.yyt.order.api.enums.OrderEnum;
import com.yts.yyt.order.api.feign.RemoteFirstLaunchOrderService;
import com.yts.yyt.order.api.vo.OrderStatVO;
import com.yts.yyt.user.api.constant.BizUserConstants;
import com.yts.yyt.user.api.constant.enums.AccountEnum;
import com.yts.yyt.user.api.constant.enums.UserAuthenticationEnum;
import com.yts.yyt.user.api.constant.enums.UserIdCardStatusEnum;
import com.yts.yyt.user.api.constant.enums.UserInfoStatusEnum;
import com.yts.yyt.user.api.dto.AppRegisterDTO;
import com.yts.yyt.user.api.dto.UserInfoUpdateDTO;
import com.yts.yyt.user.api.dto.UserSaveOpenIdDTO;
import com.yts.yyt.user.api.dto.UserVipInfoDTO;
import com.yts.yyt.user.api.entity.AccountEntity;
import com.yts.yyt.user.api.entity.UserAuthenticationEntity;
import com.yts.yyt.user.api.entity.UserBankCardEntity;
import com.yts.yyt.user.api.entity.UserInfoEntity;
import com.yts.yyt.user.api.exception.UserBizErrorCodeEnum;
import com.yts.yyt.user.api.service.UserRegisterLocationService;
import com.yts.yyt.user.api.vo.*;
import com.yts.yyt.user.handler.MobileSmsLoginHandler;
import com.yts.yyt.user.mapper.UserInfoMapper;
import com.yts.yyt.user.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.seata.spring.annotation.GlobalTransactional;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 用户信息表
 *
 * <AUTHOR>
 * @date 2025-01-06 20:45:45
 */
@Slf4j
@Service
public class UserInfoServiceImpl extends ServiceImpl<UserInfoMapper, UserInfoEntity> implements UserInfoService {

    @Autowired
    private UserAuthenticationService userAuthenticationService;
    @Autowired
    private RemoteMessageService remoteMessageService;
    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    private RemoteFirstLaunchOrderService remoteFirstLaunchOrderService;
    @Autowired
    private UserInfoMapper baseMapper;
	@Autowired
	private UserVipConfigService userVipConfigService;
	@Autowired
	private BaseRemoteService baseRemoteService;
	@Autowired
	private YytRedisOAuth2AuthorizationService yytRedisOAuth2AuthorizationService;
	@Autowired
	private UserImInfoService userImInfoService;
	@Autowired
	private UserBankCardService userBankCardService;
	@Autowired
	private AccountService accountService;
	@Autowired
	@Lazy
	private UserIdCardService userIdCardService;
	@Autowired
	private UserRegisterLocationService userRegisterLocationService;

	@Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disableEnable(Long userId, Integer status) {
        UserInfoEntity userInfo = getById(userId);
        if (ObjectUtil.isEmpty(userInfo)){
            UserBizErrorCodeEnum.USER_NOT_EXIST_ERROR.throwException();
        }
        boolean flag = lambdaUpdate()
                .eq(UserInfoEntity::getId, userId)
                .set(UserInfoEntity::getStatus, status)
                .update();

        if(flag && Objects.equals(status, UserInfoStatusEnum.DISABLE.getStatus())){
            // DC 2025/3/24 18:29 禁用用户，清除用户登录缓存
			yytRedisOAuth2AuthorizationService.removeByUsername(userInfo.getUsername());
        }

        return flag;
    }

    @Override
    public UserInfoVO getByOpenId(String appId) {
        LambdaQueryWrapper<UserInfoEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(UserInfoEntity::getWeappOpenid, appId);
        queryWrapper.eq(UserInfoEntity::getIsDelete, 0);
        queryWrapper.orderByAsc(UserInfoEntity::getRegNum);
        queryWrapper.orderByAsc(UserInfoEntity::getRegTime);
        queryWrapper.last("limit 1");
        UserInfoEntity entity = this.getOne(queryWrapper);

        // 转换为VO
        return ObjectUtil.isNotEmpty(entity) ? covertToUserInfoVO(entity) : null;
    }

    @Override
	@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public UserInfoVO getByMobile(String mobile) {
        LambdaQueryWrapper<UserInfoEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(UserInfoEntity::getMobile, mobile);
        queryWrapper.eq(UserInfoEntity::getIsDelete, 0);
        queryWrapper.orderByAsc(UserInfoEntity::getRegNum);
        queryWrapper.orderByAsc(UserInfoEntity::getRegTime);
        queryWrapper.last("limit 1");
        UserInfoEntity entity = this.getOne(queryWrapper);
		log.info("SIGN_CODE: 通过mobile 查询用户信息Sql:{}|{}",mobile,queryWrapper.getSqlSegment());
        // 转换为VO
        return ObjectUtil.isNotEmpty(entity) ? covertToUserInfoVO(entity) : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @GlobalTransactional
    public UserInfoVO addAppUser(AppRegisterDTO request) {
        UserInfoEntity entity = setUserInfo(request);
        // 保存用户信息
        this.save(entity);
        
        // 注册地理位置
        userRegisterLocationService.recordUserRegisterLocation(entity.getId(), WebUtils.getIP());
        
        //注册用户导入im
        userImInfoService.importUser(entity.getId(),entity.getNickname(),entity.getHeadImg());
        // 设置商家或合伙人的移动端用户ID
        UserRegisterSetUserIdDTO bindDto = new UserRegisterSetUserIdDTO();
        bindDto.setUserId(entity.getId());
        bindDto.setMobile(request.getMobile());
        baseRemoteService.bindMobileUserToAccounts(bindDto);

        // 为新注册用户发放优惠券
        baseRemoteService.grantUserCoupon(entity.getId(), CouponEnum.activityType.REGISTER);

        // 判断是否有邀请人
        if (ObjectUtil.isNotNull(entity.getInviteId())) {
            // 给邀请人发放优惠券
            baseRemoteService.grantUserCoupon(entity.getInviteId(), CouponEnum.activityType.INVITE);
        }
        return covertToUserInfoVO(entity);
    }


    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public Long addAppUserForMerchant(AppRegisterDTO request) {
        UserInfoEntity entity = setUserInfo(request);
        // 保存用户信息
        this.save(entity);
        //注册用户导入im
        userImInfoService.importUser(entity.getId(),entity.getNickname(),entity.getHeadImg());
		return entity.getId();
    }

    @Override
    public Long getUserIdByInviteNo(String inviteNo) {
        if (StrUtil.isNotBlank(inviteNo)) {
            LambdaQueryWrapper<UserInfoEntity> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(UserInfoEntity::getInviteNo, inviteNo);
            queryWrapper.eq(UserInfoEntity::getIsDelete, 0);
            queryWrapper.orderByAsc(UserInfoEntity::getRegNum);
            queryWrapper.orderByAsc(UserInfoEntity::getRegTime);
            queryWrapper.last("limit 1");
            UserInfoEntity user = this.getOne(queryWrapper);
            if (ObjectUtil.isNotEmpty(user)) {
                return user.getId();
            }
        }
        return null;
    }

    @Override
    public UserInfoVO covertToUserInfoVO(UserInfoEntity source) {
        // 转换为VO
        UserInfoVO target = BeanUtil.toBean(source, UserInfoVO.class);
        if (ObjectUtil.isNotEmpty(target)) {
            target.setPassword(StrUtil.EMPTY);

            // 获取认证数据
            List<String> authTypes = userAuthenticationService.queryAuthentication(source.getId());

            // 处理认证相关数据
            target.setIsIdAuth(authTypes.contains(UserAuthenticationEnum.ID_AUTH.getCode()));
            target.setIsBusiness(authTypes.contains(UserAuthenticationEnum.BUSINESS.getCode()));
            target.setIsOpenWallet(authTypes.contains(UserAuthenticationEnum.OPEN_WALLET.getCode()));

			// 获取当前商家信息并设置到VO中
			setMerchantInfo(source.getId(), target);

			// 获取用户分销角色信息
            setDistributionInfo(source.getId(),target);
		}
        return target;
    }

    private void setDistributionInfo(Long id, UserInfoVO target) {
        DistUserTeamInfoVO teamInfo = baseRemoteService.getUserDistributionTeamInfo(id);
        if (null != teamInfo) {
            target.setDistributionRole(teamInfo.getRole());
            target.setTeamStatus(teamInfo.getTeamStatus());
        }
    }

    /**
     * 创建前端用户 同步导入帐号到IM系统
     * @param request
     * @return
     */
    private UserInfoEntity setUserInfo(AppRegisterDTO request){
        UserInfoEntity entity = new UserInfoEntity();
        entity.setId(IDS.uniqueID());
        entity.setUsername(generateUserName());
        entity.setNickname(StrUtil.blankToDefault(request.getNickname(), request.getMobile()));
        entity.setMobile(request.getMobile());
        entity.setWeappOpenid(request.getOpenId());
        entity.setHeadImg(request.getHeadImg());
        entity.setLoginType(request.getAppType());
        entity.setLoginTypeName(request.getAppTypeName());
        entity.setLoginTime(System.currentTimeMillis());
        entity.setRegTime(System.currentTimeMillis());
        // 查询邀请人ID
        entity.setInviteId(getUserIdByInviteNo(request.getInviteNo()));
        // 获取邀请码
        entity.setInviteNo(generateInviteNo());
        // 获取注册顺序
        entity.setRegNum(((Long) (this.count(Wrappers.lambdaQuery()) + 1L)).intValue());
        entity.setDeleteTime(0L);
        entity.setIsDelete(0);
        entity.setStatus(1);
        
        return entity;
    }
    
    /**
     * 获取商家信息并设置到用户VO中
     * 
     * @param userId 用户ID
     * @param userInfoVO 用户信息VO
     */
    private void setMerchantInfo(Long userId, UserInfoVO userInfoVO) {
		// 获取当前商家信息 是否有店铺 禁用状态 审核状态
		IdDTO idDTO = new IdDTO();
		idDTO.setId(userId + "");
		MerchantDTO merchantDTO = baseRemoteService.getMerchantByUserId(idDTO);
		if (ObjectUtil.isNotNull(merchantDTO)) {
			userInfoVO.setMerchantId(merchantDTO.getMerchantId());
			userInfoVO.setHasShop(merchantDTO.getHasShop());
			userInfoVO.setState(merchantDTO.getState());
			userInfoVO.setAuditState(merchantDTO.getAuditState());
			userInfoVO.setShopType(merchantDTO.getShopType());
			userInfoVO.setBenefitLevel(merchantDTO.getBenefitLevel());
			userInfoVO.setBenefitLevelName(merchantDTO.getBenefitLevelName());
			userInfoVO.setBenefitLevelLogo(merchantDTO.getBenefitLevelLogo());
		}

    }

    @Override
    @CacheEvict(value = CacheConstants.YYT_USER_INFO, key = "#userId")
    public void unsubscribe(Long userId) {
        UserInfoEntity userInfoEntity = getById(userId);
        if (userInfoEntity.getIsDelete() == 1) {
            UserBizErrorCodeEnum.USER_UNSUBSCRIBE_ERROR.throwException();
        }

        lambdaUpdate()
                .set(UserInfoEntity::getDeleteTime, LocalDateTime.now().plusDays(7))
                .eq(UserInfoEntity::getId, userId)
                .update();
        // TODO发送注销短信

    }

    @Override
    public void cancelUnsubscribe(Long userId) {
        lambdaUpdate()
                .set(UserInfoEntity::getDeleteTime, null)
                .eq(UserInfoEntity::getId, userId)
                .update();
    }

    @Override
    public void changeUnsubscribeStatus() {
        lambdaUpdate()
                .set(UserInfoEntity::getIsDelete, 1)
                .set(UserInfoEntity::getDeleteTime, LocalDateTime.now())
                .set(UserInfoEntity::getUnsubscribeExpTime, null)
                .isNotNull(UserInfoEntity::getUnsubscribeExpTime)
                .lt(UserInfoEntity::getUnsubscribeExpTime, LocalDateTime.now())
                .update();
    }

    /**
     * 生成邀请码
     *
     * @return 邀请码
     */
    private String generateUserName() {
        String userName = "u_" + RandomUtil.randomString(RandomUtil.BASE_NUMBER, 10);
        // 验证用户名是否重复
        LambdaQueryWrapper<UserInfoEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(UserInfoEntity::getUsername, userName);
        return this.count(queryWrapper) > 0 ? generateInviteNo() : userName;
    }

    /**
     * 生成邀请码
     *
     * @return 邀请码
     */
    private String generateInviteNo() {
//        String firstStr = String.valueOf(RandomUtil.randomInt(1, 9));
//        String secondStr = RandomUtil.randomString(RandomUtil.BASE_NUMBER, 11);
//        String inviteNo = firstStr + secondStr;
        String inviteNo = RandomUtil.randomString(5);

        // 验证邀请码是否重复
        LambdaQueryWrapper<UserInfoEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(UserInfoEntity::getInviteNo, inviteNo);
        return this.count(queryWrapper) > 0 ? generateInviteNo() : inviteNo;
    }

    @Override
    public AppUserVO handleAppUserDTO(UserInfoVO userInfo) {
        // 封装返回数据
        AppUserVO result = new AppUserVO();
        // 转换为VO的用户信息
        result.setUserInfo(userInfo);
        // 默认为空
        result.setRoles(new Long[] {});
        // 默认为空
        result.setPermissions(new String[] {});
		log.info("SIGN_CODE: 封装返回结果数据:{}", JSONUtil.toJsonStr(result));
		log.info("SIGN_CODE: 结束获取App用户信息:{}", userInfo.getMobile());
        // 返回数据
        return result;
    }

    @Override
    public void changeHeadImg(Long userId, String headImg) {
        // 查询用户信息
        UserInfoEntity userInfo = getById(userId);
        if (ObjectUtil.isEmpty(userInfo)) {
            UserBizErrorCodeEnum.USER_NOT_EXIST_ERROR.throwException();
        }

        lambdaUpdate()
                .set(UserInfoEntity::getHeadImg, headImg)
                .eq(UserInfoEntity::getId, userId)
                .update();

        // 调用IM导入用户方法
        userImInfoService.importUser(userId, userInfo.getNickname(), headImg);
    }

    @Override
    public void changeNickname(Long userId, String nickname) {
        // 查询用户信息
        UserInfoEntity userInfo = getById(userId);
        if (ObjectUtil.isEmpty(userInfo)) {
            UserBizErrorCodeEnum.USER_NOT_EXIST_ERROR.throwException();
        }

        lambdaUpdate()
                .set(UserInfoEntity::getNickname, nickname)
                .eq(UserInfoEntity::getId, userId)
                .update();

        // 调用IM导入用户方法
        userImInfoService.importUser(userId, nickname, userInfo.getHeadImg());
    }

    @Override
    public UserInfoVO getUserInfoVOById(Long id) {
        // 获取基本用户信息
        return covertToUserInfoVO(getById(id));
    }

    @Override
    public Boolean checkExistRegisterRank(Long userId, Integer registerRank) {
        return baseMapper.checkExistRegisterRank(userId, registerRank);
    }

    @Override
    public Boolean checkIsUnsubscribeByMobile(String mobile) {
        return lambdaQuery().eq(UserInfoEntity::getMobile, mobile).eq(UserInfoEntity::getIsDelete, 1).exists();
    }

    @Override
    public void sendLoginMobileMsg(String mobile) {
        if (!Validator.isMobile(mobile)) {
            UserBizErrorCodeEnum.MOBILE_ERROR.throwException();
        }

        // 缓存KEY - 解决AOP代理导致的注解获取问题
        MobileSmsLoginHandler mobileSmsLoginHandler = SpringContextHolder.getBean(MobileSmsLoginHandler.class);
        // 获取目标类而不是代理类，避免AOP代理导致注解丢失
        Class<?> targetClass = AopUtils.getTargetClass(mobileSmsLoginHandler);
        String componentValue = targetClass.getAnnotation(Component.class).value();
        String cacheKey = CacheConstants.DEFAULT_CODE_KEY + componentValue + StringPool.AT + mobile;

        // 检查手机号验证码未过期
        String cacheVal = redisTemplate.opsForValue().get(cacheKey);
        if (StrUtil.isNotBlank(cacheVal)) {
            log.info("[发送手机短信] 验证码未过期:{}，{}", mobile, cacheKey);
            UserBizErrorCodeEnum.throwException(CommonConstants.FAIL,
                    MsgUtils.getMessage(ErrorCodes.SYS_APP_SMS_OFTEN));
        }

        // 校验手机号是否注销(注销后不能在使用)
        if (checkIsUnsubscribeByMobile(mobile)) {
            log.info("[发送手机短信] 手机号已注销:{}", mobile);
            UserBizErrorCodeEnum.USER_UNSUBSCRIBE_ERROR.throwException();
        }

        // 生成验证码
        String codeSizeStr = ParamResolver.getStr(BizUserConstants.CODE_SIZE);
        int codeSize = Integer.parseInt(NumberUtil.isInteger(codeSizeStr) ? codeSizeStr : SecurityConstants.CODE_SIZE);
        cacheVal = RandomUtil.randomNumbers(Math.max(codeSize, 1));

        // 缓存验证码
        String codeTimeStr = ParamResolver.getStr(BizUserConstants.CODE_TIME);
        int codeTime = NumberUtil.isInteger(codeTimeStr) ? Integer.parseInt(codeTimeStr) : SecurityConstants.CODE_TIME;
        redisTemplate.opsForValue().set(cacheKey, cacheVal, Math.max(codeTime, 10), TimeUnit.SECONDS);

        log.info("[发送手机短信] 手机号({})生成验证码成功:{}", mobile, cacheVal);

        // 组装短信参数
        MessageSmsDTO messageSmsDTO = MessageSmsDTO.builder().param(cacheVal).build();
        messageSmsDTO.setBizCode(SmsBizCodeEnum.SMS_NORMAL_CODE.getCode());
        messageSmsDTO.setMobiles(List.of(mobile));

        // 发送短信
        R result = remoteMessageService.sendInnerSms(messageSmsDTO);
        if (!result.isOk()) {
            log.info("[发送手机短信] 手机号({})发送短信失败:{}", mobile, JSONUtil.toJsonStr(result));
            redisTemplate.delete(cacheKey);
            UserBizErrorCodeEnum.SEND_MOBILE_MESSAGE_ERROR.throwException();
        }
    }


	@Override
	public void cancel(Long id) {
		if (Objects.isNull(id)) {
			UserBizErrorCodeEnum.PARAMS_ERROR.throwException();
		}
		UserInfoEntity userInfoEntity = getById(id);
		if (null == userInfoEntity || userInfoEntity.getIsDelete() == 1) {
			UserBizErrorCodeEnum.USER_UNSUBSCRIBE_ERROR.throwException();
		}
		lambdaUpdate()
				.set(UserInfoEntity::getDeleteTime, System.currentTimeMillis())
				.set(UserInfoEntity::getIsDelete, 1)
				.eq(UserInfoEntity::getId, id)
				.update();

        // 注销提交成功 发送短信
        MessageSmsDTO messageSmsDTO = new MessageSmsDTO();
        messageSmsDTO.setBizCode(SmsBizCodeEnum.CANCEL_NORMAL_CODE.getCode());
        messageSmsDTO.setMobiles(List.of(userInfoEntity.getMobile()));
        // 发送短信
        R result = remoteMessageService.sendInnerSms(messageSmsDTO);
        if (!result.isOk()) {
            log.info("[发送手机短信] 手机号({})发送短信失败:{}", userInfoEntity.getMobile(), JSONUtil.toJsonStr(result));
            UserBizErrorCodeEnum.SEND_MOBILE_MESSAGE_ERROR.throwException();
        }
		userImInfoService.setUserImInvalid(id);
	}

	/**
     * 功能 根据用户ID获取用户信息
     * 创建于 2025/2/23 下午4:51
     * <AUTHOR>
     * @param userId 用户ID
     * @return UserInfoEntity
     */
    private UserInfoEntity getUserById(Long userId) {
        UserInfoEntity userInfo = getById(userId);
        if (ObjectUtil.isEmpty(userInfo)) {
            UserBizErrorCodeEnum.USER_NOT_EXIST_ERROR.throwException();
        }
        return userInfo;
    }

    @Override
    public IPage<UserVipInfoVO> pageUserVipList(Page page, UserVipInfoDTO dto) {
        IPage<UserVipInfoVO> pageResult = baseMapper.pageUserVipInfoList(page, buildQueryWrapper(dto));
        if (pageResult.getTotal() < 1) {
            return pageResult;
        }

        // DC 2025/3/5 15:21 获取用户ID列表
        List<Long> userIds = pageResult.getRecords().stream().map(UserInfoEntity::getId).toList();

        // DC 2025/3/6 17:40 获取用户订单统计信息（远程feign调用）
        Map<String,Map<Long,String>> orderStatMap = getOrderStatByUserIds(userIds);

        // 处理订单统计信息
        if (orderStatMap != null) {
            for (UserVipInfoVO userInfo : pageResult.getRecords()) {
                // DC 2025/3/6 17:47 刷入订单统计
                if(orderStatMap.containsKey("count") && orderStatMap.get("count").containsKey(userInfo.getId())){
                    userInfo.setCompletedMallOrderCount(Long.parseLong(orderStatMap.get("count").get(userInfo.getId())));
                }
                if(orderStatMap.containsKey("amount") && orderStatMap.get("amount").containsKey(userInfo.getId())){
                    userInfo.setCompletedMallOrderAmount(orderStatMap.get("amount").get(userInfo.getId()));
                }
            }
        }

        return pageResult;
    }



    @Override
    public UserVipInfoDetailVO userVipDetail(Long userId) {
        UserVipInfoDetailVO vo = new UserVipInfoDetailVO();

        // DC 2025/3/7 14:04 获取用户信息
        vo.setUserInfo(getUserById(userId));

        // 获取用户真实姓名 没有则为空
		vo.getUserInfo().setNickname("");
		QueryUserIdCardVO query = userIdCardService.query(userId);
		if(query.getAuthStatus().equals(UserIdCardStatusEnum.REVIEW_PASS.getStatus())){
			vo.getUserInfo().setNickname(query.getName());
		}

		// DC 2025/3/7 14:07 获取用户订单统计信息
        Map<String,Map<Long,String>> orderStatMap = getOrderStatByUserIds(List.of(userId));

        // DC 2025/3/6 17:47 刷入订单统计
        if(orderStatMap != null){
            if(orderStatMap.containsKey("count") && orderStatMap.get("count").containsKey(vo.getUserInfo().getId())){
                vo.setCompletedMallOrderCount(Long.parseLong(orderStatMap.get("count").get(vo.getUserInfo().getId())));
            }
            if(orderStatMap.containsKey("amount") && orderStatMap.get("amount").containsKey(vo.getUserInfo().getId())){
                vo.setCompletedMallOrderAmount(orderStatMap.get("amount").get(vo.getUserInfo().getId()));
            }
        }

        return vo;

    }

	@Override
	public UserAccountDetailVO accountDetail(Long userId) {
		UserAccountDetailVO vo = new UserAccountDetailVO();
		// 初始化总冻结金额
		BigDecimal totalFrozen = BigDecimal.ZERO;
		// 获取主账户信息
		AccountEntity mainAccount = accountService.getByUserIdAndType(userId, AccountEnum.AccountTypeEnum.BALANCE_ACCOUNT.getType());
		if (mainAccount != null) {
			// 设置账户可用余额
			vo.setAccountBalance(mainAccount.getAvailableBalance().toString());
			// 累加到总冻结金额
			totalFrozen = totalFrozen.add(mainAccount.getFrozenBalance());
		}
		// 获取消保金账户信息
		AccountEntity protectionAccount = accountService.getByUserIdAndType(userId, AccountEnum.AccountTypeEnum.DEPOSIT_ACCOUNT.getType());
		if (protectionAccount != null) {
			// 设置消保金可用余额
			vo.setProtectionBalance(protectionAccount.getAvailableBalance().toString());
			// 累加消保金冻结金额
			totalFrozen = totalFrozen.add(protectionAccount.getFrozenBalance());
		}
		// 设置总冻结金额（消保金冻结+账户冻结）
		vo.setFrozenAmount(totalFrozen.toString());
		// 获取消费总额和成交订单数（调用订单服务）
		R<List<OrderStatVO>> orderStat = remoteFirstLaunchOrderService.getOrderStat(Collections.singletonList(userId),
				String.valueOf(OrderEnum.OrderStatus.FIRST_LAUNCH_COMPLETED));
		if (orderStat.isOk() && CollUtil.isNotEmpty(orderStat.getData())) {
			OrderStatVO statVO = orderStat.getData().get(0);
			vo.setTotalConsumption(statVO.getOrderAmount());
			vo.setCompletedOrders(String.valueOf(statVO.getOrderCount()));
			vo.setActualAmount(statVO.getActualAmount());
		} else {
			// 设置默认值为0
			vo.setTotalConsumption("0");
			vo.setCompletedOrders("0");
			vo.setActualAmount("0");
		}
		// 获取用户银行卡列表
		vo.setBankCardList(getBankCardList(userId));
		return vo;
	}

	/**
	 * 获取用户银行卡列表
	 * @param userId 用户ID
	 * @return 银行卡信息列表
	 */
	private List<UserAccountDetailVO.BankCardInfo> getBankCardList(Long userId) {
		List<UserBankCardEntity> bankCardList = userBankCardService.queryByUserId(userId);
		if (CollUtil.isNotEmpty(bankCardList)) {
			return bankCardList.stream().map(card -> {
				UserAccountDetailVO.BankCardInfo bankCardInfo = new UserAccountDetailVO.BankCardInfo();
				// 账户类型
				bankCardInfo.setAccountType("0".equals(card.getCardType()) ? "商家(对公)" : ("1".equals(card.getCardType()) ? "个人(对私)" : "个人(对私非法人)"));
				// 所在省市
				bankCardInfo.setLocation(card.getProvName() + "/" + card.getAreaName());
				// 银行卡号
				bankCardInfo.setCardNumber(card.getBankNo());
				// 银行名称（总行-支行）
				bankCardInfo.setBankName(card.getBankName());
				// 银行号
				bankCardInfo.setBankCode(card.getBankCode());
				// 支付联号
				bankCardInfo.setPaymentCode(card.getBranchCode());
				// 预留手机号
				bankCardInfo.setReservedPhone(card.getMobile());
				// 持卡人
				bankCardInfo.setCardHolder(card.getAccountName());
				// 绑定时间
				bankCardInfo.setBindTime(DateUtil.format(card.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
				return bankCardInfo;
			}).collect(Collectors.toList());
		} else {
			// 银行卡列表为空时，设置为空列表
			return Collections.emptyList();
		}
	}

    @Override
    public Boolean updateUserInfo(UserInfoUpdateDTO dto) {
        return lambdaUpdate()
                    .set(StrUtil.isNotEmpty(dto.getNickname()), UserInfoEntity::getNickname, dto.getNickname())
                    .set(StrUtil.isNotEmpty(dto.getMobile()),UserInfoEntity::getMobile, dto.getMobile())
                    .set(StrUtil.isNotEmpty(dto.getUsername()),UserInfoEntity::getUsername, dto.getUsername())
                    .set(StrUtil.isNotEmpty(dto.getUserAddress()),UserInfoEntity::getUserAddress, dto.getUserAddress())
                    .eq(UserInfoEntity::getId, dto.getUserId())
                    .update();
    }

	@Override
	public void saveOpenIdById(UserSaveOpenIdDTO dto) {
		UserInfoEntity user = this.getUserById(dto.getId());
		if (StrUtil.isNotBlank(user.getWeappOpenid())) {
			return;
		}
		// 保存openId
		user.setWeappOpenid(dto.getOpenId());
		this.updateById(user);
	}

	@Override
	public UserVipLevelPageVO getVipLevelPage(Long id) {
		// 转换为VO
		UserVipLevelPageVO result = new UserVipLevelPageVO();

		// 获取认证数据
		List<String> authTypes = userAuthenticationService.queryAuthentication(id);

		// 处理认证相关数据
		result.setIsIdAuth(authTypes.contains(UserAuthenticationEnum.ID_AUTH.getCode()));

		// 获取会员配置信息
		result.setUserVipConfigEntityList(userVipConfigService.list());

		return result;
	}

	@Override
	public UserInviteListVO getUserInviteList(Long userId, Page<UserInfoEntity> page) {
		// 验证用户是否存在
		UserInfoEntity userInfo = getById(userId);
		if (ObjectUtil.isEmpty(userInfo)) {
			UserBizErrorCodeEnum.USER_NOT_EXIST_ERROR.throwException();
		}

		// 使用一次SQL查询获取邀请用户列表及其认证状态
		IPage<UserInviteListVO.UserInviteVO> userInvitePage = baseMapper.getUserInviteList(page, userId);

		// 构建返回VO对象
		UserInviteListVO userInviteListVO = new UserInviteListVO();
		userInviteListVO.setCurrent(userInvitePage.getCurrent())
				.setSize(userInvitePage.getSize())
				.setPages(userInvitePage.getPages())
				.setTotal(userInvitePage.getTotal())
				.setUserInviteVOList(userInvitePage.getRecords());

		return userInviteListVO;
	}

	@Override
	public AdminUserInviteListVO getAdminUserInviteList(Page<UserInfoEntity> page, String keyword) {

		// 构建查询语句
		LambdaQueryWrapper<UserInfoEntity> queryWrapper = Wrappers.lambdaQuery();
		queryWrapper.ne(UserInfoEntity::getInviteId,0)
				.like(StrUtil.isNotBlank(keyword),UserInfoEntity::getNickname,keyword)
				.or()
				.ne(UserInfoEntity::getInviteId,0)
				.like(StrUtil.isNotBlank(keyword),UserInfoEntity::getMobile,keyword)
				.orderByDesc(UserInfoEntity::getId);

		Page<UserInfoEntity> userInfoEntityPage = page(page, queryWrapper);

		// 构建返回对象
		AdminUserInviteListVO result = new AdminUserInviteListVO();
		result.setCurrent(userInfoEntityPage.getCurrent())
				.setSize(userInfoEntityPage.getSize())
				.setPages(userInfoEntityPage.getPages())
				.setTotal(userInfoEntityPage.getTotal());

		// 获取邀请人id
		List<Long> inviteUserIds = userInfoEntityPage.getRecords().stream().map(UserInfoEntity::getInviteId).collect(Collectors.toList());
		// 获取被邀请人id
		List<Long> inviteeUserIds = userInfoEntityPage.getRecords().stream().map(UserInfoEntity::getId).collect(Collectors.toList());
		if(!inviteUserIds.isEmpty()){
			// 获取邀请用户列表
			List<UserInfoEntity> inviteUserInfoList = listByIds(inviteUserIds);
			// 获取idAuth验证信息
			List<UserAuthenticationEntity> userAuthenticationEntityList = userAuthenticationService.getAuthList(inviteeUserIds,UserAuthenticationEnum.ID_AUTH.getCode());
			// 转换对象
			List<AdminUserInviteListVO.AdminUserInviteVO> userInviteVOS = userInfoEntityPage.getRecords().stream().map(inviteeUserInfo -> {
				Optional<UserInfoEntity> inviteUserInfoOpt = inviteUserInfoList.stream()
						.filter(u -> u.getId().equals(inviteeUserInfo.getInviteId())).findFirst();
				Optional<UserAuthenticationEntity> authOpt = userAuthenticationEntityList.stream()
						.filter(auth -> auth.getUserId().equals(inviteeUserInfo.getId())).findFirst();
				// 构建返回对象
				return new AdminUserInviteListVO.AdminUserInviteVO()
						.setInviteUserId(inviteUserInfoOpt.map(UserInfoEntity::getId).orElse(null))
						.setInviteNickName(inviteUserInfoOpt.map(UserInfoEntity::getNickname).orElse(null))
						.setInviteMobile(inviteUserInfoOpt.map(UserInfoEntity::getMobile).orElse(null))
						.setInviteeUserId(inviteeUserInfo.getId())
						.setInviteeNickName(inviteeUserInfo.getNickname())
						.setInviteeMobile(inviteeUserInfo.getMobile())
						.setIdAuthTime(authOpt.map(UserAuthenticationEntity::getCreateTime).orElse(null));
			}).collect(Collectors.toList());
			result.setUserInviteVOList(userInviteVOS);
		}

		return result;
	}


	/**
     * 功能 构建查询条件
     * 创建于 2025/3/6 18:22
     * <AUTHOR>
     * @param dto
     * @return
     */
    private LambdaQueryWrapper<UserInfoEntity> buildQueryWrapper(UserVipInfoDTO dto){
        LambdaQueryWrapper<UserInfoEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StrUtil.isNotEmpty(dto.getMobile()), UserInfoEntity::getMobile, dto.getMobile());
        wrapper.like(StrUtil.isNotEmpty(dto.getNickName()), UserInfoEntity::getNickname, dto.getNickName());
        if(StrUtil.isNotEmpty(dto.getStartTime())){
            wrapper.ge(UserInfoEntity::getRegTime, DateUtil.parse(dto.getStartTime()).getTime()); 
        }
        if(StrUtil.isNotEmpty(dto.getEndTime())) {
            wrapper.le(UserInfoEntity::getRegTime, DateUtil.parse(dto.getEndTime()).getTime());
        }
        wrapper.eq(StrUtil.isNotEmpty(dto.getLoginType()), UserInfoEntity::getLoginType, dto.getLoginType());
        wrapper.orderByDesc(UserInfoEntity::getRegTime);
        return wrapper;
    }



    /**
     * 功能 获取用户订单统计信息
     * 创建于 2025/3/6 18:23
     * <AUTHOR>
     * @param userIds 用户ID列表
     * @return Map
     */
    private Map<String,Map<Long,String>> getOrderStatByUserIds(List<Long> userIds){
        R<List<OrderStatVO>> orderStat = remoteFirstLaunchOrderService.getOrderStat(userIds, String.valueOf(OrderEnum.OrderStatus.FIRST_LAUNCH_COMPLETED));
        if (!orderStat.isOk()){
            UserBizErrorCodeEnum.VIP_USER_GET_ORDER_STAT_FAILED.throwException();
        }
        if (ObjectUtil.isEmpty(orderStat.getData())){
            return null;
        }

        // DC 2025/3/5 17:31 获取用户VIP等级
        Map<Long, String> buyerOrderCount = new HashMap<>();
        Map<Long,String> buyerOrderAmount = new HashMap<>();
        for (OrderStatVO orderStatVO : orderStat.getData()){
            buyerOrderCount.put(orderStatVO.getUserId(), String.valueOf(orderStatVO.getOrderCount()));
            buyerOrderAmount.put(orderStatVO.getUserId(), orderStatVO.getOrderAmount());
        }

        return MapUtil.builder(new HashMap<String, Map<Long, String>>())
                .put("count",buyerOrderCount)
                .put("amount",buyerOrderAmount)
                .build();
    }


    
}
