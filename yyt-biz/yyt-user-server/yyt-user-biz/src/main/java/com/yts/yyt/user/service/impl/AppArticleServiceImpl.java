package com.yts.yyt.user.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yts.yyt.admin.api.entity.SysDictItem;
import com.yts.yyt.common.data.resolver.DictResolver;
import com.yts.yyt.user.api.constant.enums.DictEnum;
import com.yts.yyt.user.api.entity.AppArticleCategoryEntity;
import com.yts.yyt.user.api.entity.AppArticleCollectEntity;
import com.yts.yyt.user.api.entity.AppArticleEntity;
import com.yts.yyt.user.mapper.AppArticleCollectMapper;
import com.yts.yyt.user.mapper.AppArticleMapper;
import com.yts.yyt.user.service.AppArticleService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 文章资讯
 *
 * <AUTHOR>
 * @date 2023-06-07 16:32:35
 */
@Service
@RequiredArgsConstructor
public class AppArticleServiceImpl extends ServiceImpl<AppArticleMapper, AppArticleEntity> implements AppArticleService {

    private final AppArticleCollectMapper collectMapper;

    /**
     * 获取文章并使阅读数+1
     *
     * @param id id
     * @return
     */
    @Override
    public AppArticleEntity getArticleAndIncrById(Long id, Long userId) {
        AppArticleEntity appArticleEntity = baseMapper.selectById(id);
        // 过滤掉状态为草稿的文章
        if (Objects.isNull(appArticleEntity) || appArticleEntity.getStatus() != 1) {
            return null;
        }

        // 查询是否收藏了
        if (Objects.nonNull(userId)) {
            boolean exists = collectMapper.exists(Wrappers.<AppArticleCollectEntity>lambdaQuery()//
                    .eq(AppArticleCollectEntity::getArticleId, appArticleEntity.getId())//
                    .eq(AppArticleCollectEntity::getUserId, userId));
            appArticleEntity.setCollect(exists);
        }

        // 新增访问量
        baseMapper.articleIncrVisit(appArticleEntity.getId());

        // 返回数据
        return appArticleEntity;
    }

    /**
     * 分页查询文章列表 包含分类名称
     *
     * @param page       分页参数
     * @param appArticle 文章查询条件
     * @return
     */
    @Override
    public Page pageAndCname(Page page, AppArticleEntity appArticle) {
        MPJLambdaWrapper<AppArticleEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(AppArticleEntity.class)
                .selectAs(AppArticleCategoryEntity::getName, AppArticleEntity.Fields.cname)
                .leftJoin(AppArticleCategoryEntity.class, AppArticleCategoryEntity::getId, AppArticleEntity::getCid)
                .like(StrUtil.isNotBlank(appArticle.getAuthor()), AppArticleEntity::getAuthor, appArticle.getAuthor())
                .like(StrUtil.isNotBlank(appArticle.getTitle()), AppArticleEntity::getTitle, appArticle.getTitle())
                .eq(Objects.nonNull(appArticle.getCid()), AppArticleEntity::getCid, appArticle.getCid());
        return this.page(page, wrapper);
    }

    @Override
    public AppArticleEntity getArticleAndByKey(String key) {
        // 文章ID
        Long articleId = null;
        AppArticleEntity article;
        // 查询对应的转换配置
        SysDictItem dictItem = DictResolver.getDictItemByItemValue(DictEnum.YYT_AGREEMENT_TYPE.getCode(), key);
        if (ObjectUtil.isNotEmpty(dictItem) && NumberUtil.isLong(dictItem.getDescription())) {
            articleId = Long.valueOf(dictItem.getDescription());
        }

        // 处理传入默认数据
        articleId = ObjectUtil.defaultIfNull(articleId, NumberUtil.isLong(key) ? Long.valueOf(key) : null);

        // [文章ID为空/不存在/状态不为草稿] 时, 返回null
        if (articleId == null || ObjectUtil.isEmpty(article = baseMapper.selectById(articleId)) || article.getStatus() != 1) {
            return null;
        }

        // 新增访问量
        baseMapper.articleIncrVisit(articleId);

        // 返回数据
        return article;
    }
}
