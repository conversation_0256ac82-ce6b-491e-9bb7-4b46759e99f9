package com.yts.yyt.user.controller;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.qcloud.cos.COSClient;
import com.yts.yyt.admin.api.vo.UploadFileVO;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.security.annotation.Inner;
import com.yts.yyt.goods.api.constants.GoodsConstant;
import com.yts.yyt.user.api.dto.CosPropertiesDTO;
import com.yts.yyt.user.api.dto.GoodsZipInfoSaveDTO;
import com.yts.yyt.user.api.vo.CosTempSecretVO;
import com.yts.yyt.user.service.CosProcessor;
import com.yts.yyt.user.service.SysFileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文件管理
 *
 * <AUTHOR>
 * @date 2019-06-18 17:18:42
 */
@RestController
@AllArgsConstructor
@RequestMapping("/file")
@Tag(description = "file", name = "文件管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@Slf4j
public class SysFileController {

    private final SysFileService sysFileService;
    private final CosProcessor cosProcessor;

	@Operation(summary = "获取cos配置", description = "获取cos配置")
	@GetMapping("/getCosInfo")
	public R<CosPropertiesDTO> getCosInfo(){
		CosPropertiesDTO cosInfo = sysFileService.getCosInfo();
		return R.ok(cosInfo);
	}

	/**
	 * 获取cos临时密钥
	 * @return res
	 */
	@Operation(summary = "获取cos临时密钥", description = "获取cos临时密钥")
	@GetMapping("/getCosTempInfo")
	public R<CosTempSecretVO> getCosTempInfo(){
		CosTempSecretVO tempCredential = cosProcessor.getTempCredential();
		return R.ok(tempCredential);
	}

	/**
	 * zip包名称检查
	 * 不可重复，因为需要根据包名称去cos上解压
	 * @return
	 */
	@Operation(summary = "zip包名称检查", description = "zip包不可重复，因为需要根据包名称去cos上解压")
	@GetMapping("/checkZipFileName")
	public R checkZipFileName(@RequestParam("fileName") String fileName){
		Boolean check = sysFileService.checkZipFileName(fileName);
		if (check) {
			return R.ok(check);
		}
		return R.failed("文件名称不能重复");
	}

	/**
	 * 图片zip上传信息保存
	 * @param goodsZipInfoSaveDTO
	 * @return
	 */
	@Operation(summary = "图片zip上传信息保存", description = "图片zip上传信息保存")
	@PostMapping("/saveGoodsZipInfo")
	public R saveGoodsZipInfo(@Valid @RequestBody GoodsZipInfoSaveDTO goodsZipInfoSaveDTO){
		String message = sysFileService.saveGoodsZipInfo(goodsZipInfoSaveDTO);
		return R.ok(StrUtil.isNotBlank(message) ? message : "图片信息处理中，请稍后上传相应的商品信息");
	}

	/**
	 * 保存图片文件夹信息
	 * @param goodsZipInfoSaveDTO
	 * @return
	 */
	@Operation(summary = "保存图片文件夹信息", description = "保存图片文件夹信息")
	@PostMapping("/saveFolderInfo")
	public R saveFolderInfo(@Valid @RequestBody GoodsZipInfoSaveDTO goodsZipInfoSaveDTO){
		sysFileService.saveFolderInfo(goodsZipInfoSaveDTO);
		return R.ok("图片信息处理中，请稍后上传相应的商品信息");
	}

    /**
     * 上传文件 文件名采用uuid,避免原始文件名中带"-"符号导致下载的时候解析出现异常
     *
     * @param file 资源
     * @param dir  文件夹
     * @return R(/ admin / bucketName / filename)
     */
    @PostMapping(value = "/upload")
    public R upload(@RequestPart("file") MultipartFile file, @RequestParam(value = "dir", required = false) String dir,
                    @RequestParam(value = "groupId", required = false) Long groupId,
                    @RequestParam(value = "type", required = false) String type) {
		if (GoodsConstant.store_goods_dir.equals(dir)) {
			//藏品管理相关图片，目录需要加上日期
			dir = dir +"/"+ DateUtil.format(LocalDateTime.now(), DatePattern.PURE_DATE_PATTERN);
		}
        return sysFileService.uploadFile(file, dir, groupId, type);
    }

	/**
	 * 上传商品图片zip包
	 * 图片名称： 商品编码_头图_1.jpg 、 商品编码_头图_2.jpg   商品编码_详情图_1.jpg 、 商品编码_详情图_2.jpg
	 */
	@Operation(summary = "上传商品图片zip包", description = "上传商品图片zip包")
	@PostMapping("/uploadFirstGoodsZip")
//    @HasPermission("goods_info_upload")
	public R<List<UploadFileVO>> uploadFirstGoodsZip(@RequestParam("file") MultipartFile file,
													 @RequestParam(value = "dir", required = false, defaultValue = "") String dir) {
		log.info("uploadFirstGoodsZip 开始。。。");
		return sysFileService.uploadZipFile(file, dir);
	}

    /**
     * 上传文件
     *
     * @param file 资源
     * @return R(/ admin / bucketName / filename)
     */
    @Operation(summary = "上传文件", description = "上传文件")
    @PostMapping(value = "/upload/file/{uploadType}")
    public R uploadFile(@PathVariable("uploadType") String uploadType, @RequestPart("file") MultipartFile file) {
        return sysFileService.uploadFile(file, uploadType);
    }

    /**
     * 上传头像文件
     *
     * @param file 资源
     * @return R(/ admin / bucketName / filename)
     */
    @Inner(false)
    @PostMapping(value = "/upload/avatar")
    @Operation(summary = "上传头像文件", description = "上传头像文件")
    public R uploadAvatar(@RequestPart("file") MultipartFile file) {
        return sysFileService.uploadFile(file, "avatar");
    }

    /**
     * 获取文件
     *
     * @param fileName 文件空间/名称
     */
    @Inner(false)
    @GetMapping("/show")
    @Operation(summary = "获取文件", description = "获取文件")
    public void file(String fileName, HttpServletResponse response) {
        sysFileService.getFile(fileName, response);
    }
}
