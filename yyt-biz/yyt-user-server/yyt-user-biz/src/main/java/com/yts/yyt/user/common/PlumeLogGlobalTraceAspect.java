package com.yts.yyt.user.common;

import com.plumelog.trace.aspect.AbstractAspect;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

@Aspect
@Component
@Slf4j
public class PlumeLogGlobalTraceAspect extends AbstractAspect {

    @Pointcut("within(com.yts.yyt.user..*)")
    public void allServicePointcut() {
    }

    @Around("allServicePointcut()")
    public Object around(JoinPoint joinPoint) throws Throwable {
        return aroundExecute(joinPoint);
    }
}
