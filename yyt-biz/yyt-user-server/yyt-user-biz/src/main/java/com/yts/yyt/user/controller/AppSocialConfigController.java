package com.yts.yyt.user.controller;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.StrUtil;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.security.annotation.Inner;
import com.yts.yyt.user.api.dto.AppSocialDTO;
import com.yts.yyt.user.api.vo.AppUserVO;
import com.yts.yyt.user.api.exception.UserBizErrorCodeEnum;
import com.yts.yyt.user.service.AppSocialConfigService;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
@Tag(description = "social", name = "三方账号管理模块")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class AppSocialConfigController {

    private final AppSocialConfigService appSocialConfigService;

    /**
     * 通过(社交账号、手机号)  -> inputStr 查询用户、角色信息
     *
     * @param request appid@code
     * @return
     */
    @Inner
    @PostMapping("/app/social/info")
    public R<AppUserVO> getUserInfo(@Valid @RequestBody AppSocialDTO request) {
        if (!request.getInputStr().contains(StrUtil.AT) || request.getInputStr().endsWith(StrUtil.AT)) {
            UserBizErrorCodeEnum.PARAMS_ERROR.throwException();
        }

        // 校验传入手机号格式
        if (StrUtil.isNotBlank(request.getMobile()) && !Validator.isMobile(request.getMobile())) {
            UserBizErrorCodeEnum.MOBILE_ERROR.throwException();
        }

        return R.ok(appSocialConfigService.getUserInfo(request));
    }

    /**
     * 绑定社交账号
     *
     * @param state 类型
     * @param code  code
     * @return
     */
    @PostMapping("/bind")
    public R bindSocial(String state, String code) {
        return R.ok(appSocialConfigService.bindSocial(state, code));
    }
}
