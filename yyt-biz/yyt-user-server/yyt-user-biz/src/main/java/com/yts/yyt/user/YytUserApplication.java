package com.yts.yyt.user;

import com.yts.yyt.common.feign.annotation.EnableYytFeignClients;
import com.yts.yyt.common.security.annotation.EnableYytResourceServer;
import com.yts.yyt.common.swagger.annotation.EnableOpenApi;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;


import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.EnableAspectJAutoProxy;


/**
 * <AUTHOR> archetype
 * <p>
 * 项目启动类
 */
@EnableOpenApi("user")
@EnableYytFeignClients
@EnableDiscoveryClient
@EnableYytResourceServer
@SpringBootApplication
@Slf4j
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
public class YytUserApplication{

	public static void main(String[] args) {
		SpringApplication.run(YytUserApplication.class, args);
	}
}
