package com.yts.yyt.user.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yts.yyt.common.data.datascope.YytBaseMapper;
import com.yts.yyt.user.api.entity.UserInfoEntity;
import com.yts.yyt.user.api.vo.UserInviteListVO;
import com.yts.yyt.user.api.vo.UserVipInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UserInfoMapper extends YytBaseMapper<UserInfoEntity> {

    /**
     * 检查用户是否前(rank)名注册
     * @param userId 用户ID
     * @param rank 排名
     * @return 结果
     */
    Boolean checkExistRegisterRank(@Param("userId") Long userId, @Param("rank") Integer rank);

    /**
     * 功能 分页获取用户会员列表信息
     * 创建于 2025/3/6 18:21
     * <AUTHOR>
     * @param page 分页参数
     * @param wrapper 查询条件
     * @return IPage<UserVipInfoVO>
     */
    IPage<UserVipInfoVO> pageUserVipInfoList(Page page, @Param(Constants.WRAPPER) Wrapper<UserInfoEntity> wrapper);
    
    /**
     * 获取用户邀请列表及认证状态
     * @param page 分页参数
     * @param inviteId 邀请人ID
     * @return 邀请用户列表
     */
    IPage<UserInviteListVO.UserInviteVO> getUserInviteList(Page page, @Param("inviteId") Long inviteId);
}