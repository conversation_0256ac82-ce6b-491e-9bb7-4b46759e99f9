package com.yts.yyt.user.controller;

import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.log.annotation.SysLog;
import com.yts.yyt.user.api.vo.UserVipLevelVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户会员信息表
 *
 * <AUTHOR>
 * @date 2025-02-14 11:14:10
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/userVipInfo")
@Tag(description = "userVipInfo", name = "用户会员信息表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class UserVipInfoController {


    /**
     * 查询用户会员等级信息
     */
    @SysLog("查询用户会员等级信息")
    @GetMapping("/getUserVipLevel")
    @Operation(summary = "查询用户会员等级信息")
    public R<UserVipLevelVO> getUserVipLevel(@RequestParam(value = "vipType", required = true) String vipType) {
    	return R.ok();
    }
    
    
}