package com.yts.yyt.user.controller.admin;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.user.api.dto.UserBankCardDTO;
import com.yts.yyt.user.api.entity.UserBankCardEntity;
import com.yts.yyt.user.service.UserBankCardService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 用户银行卡信息表
 *
 * <AUTHOR>
 * @date 2024-01-08
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/admin/userBankCard")
@Tag(name = "用户银行卡管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class AdminUserBankCardController {

    private final UserBankCardService userBankCardService;

    /**
     * 功能 分页查询用户银行卡
     * 创建于 2025/3/7 15:16
     * <AUTHOR>
     * @param page 分页对象
     * @param dto 查询条件
     * @return R
     */
    @Operation(summary = "后台：分页查询", description = "分页查询用户银行卡列表")
    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('user_BankCard_view')" )
    public R getUserBankCardPage(@ParameterObject Page page, UserBankCardDTO dto) {
        return R.ok(userBankCardService.queryPage(page, dto));
    }

    /**
     * 功能 查询用户的所有银行卡列表
     * 创建于 2025/3/14 09:28
     * <AUTHOR>
     * @param userId 用户ID
     * @return R<List<UserBankCardEntity>>
     */
    @Operation(summary = "后台：查询用户的所有银行卡列表", description = "查询用户的所有银行卡列表")
    @GetMapping("/userList/{userId}")
    @PreAuthorize("@pms.hasPermission('user_BankCard_view')" )
    public R<List<UserBankCardEntity>> getUserBankCardPage(@PathVariable Long userId) {
        return R.ok(userBankCardService.queryByUserId(userId));
    }

}