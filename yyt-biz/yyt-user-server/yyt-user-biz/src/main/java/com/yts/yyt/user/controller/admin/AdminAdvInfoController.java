package com.yts.yyt.user.controller.admin;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.core.validation.UrlValidation;
import com.yts.yyt.common.excel.annotation.ResponseExcel;
import com.yts.yyt.common.log.annotation.SysLog;
import com.yts.yyt.common.swagger.base.BaseController;
import com.yts.yyt.user.api.entity.AdvInfoEntity;
import com.yts.yyt.user.service.AdvInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 广告信息管理
 *
 * <AUTHOR>
 * @date 2025-01-08 13:34:54
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/admin/advInfo")
@Tag(description = "advInfo", name = "广告信息管理(后台)")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class AdminAdvInfoController extends BaseController {

    private final AdvInfoService advInfoService;

    /**
     * 分页查询
     *
     * @param page    分页对象
     * @param advInfo 广告信息管理
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('user_advInfo_view')")
    public R getAdvInfoPage(@ParameterObject Page page, @ParameterObject AdvInfoEntity advInfo) {
        LambdaQueryWrapper<AdvInfoEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(advInfoService.page(page, wrapper));
    }


    /**
     * 通过id查询广告信息管理
     *
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{id}")
    @PreAuthorize("@pms.hasPermission('user_advInfo_view')")
    public R getById(@PathVariable("id") Integer id) {
        return R.ok(advInfoService.getById(id));
    }

    /**
     * 新增广告信息管理
     *
     * @param advInfo 广告信息管理
     * @return R
     */
    @Operation(summary = "新增广告信息管理", description = "新增广告信息管理")
    @SysLog("新增广告信息管理")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('user_advInfo_add')")
    @UrlValidation
    public R save(@RequestBody AdvInfoEntity advInfo) {
        return R.ok(advInfoService.save(advInfo));
    }

    /**
     * 修改广告信息管理
     *
     * @param advInfo 广告信息管理
     * @return R
     */
    @Operation(summary = "修改广告信息管理", description = "修改广告信息管理")
    @SysLog("修改广告信息管理")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('user_advInfo_edit')")
    @UrlValidation
    public R updateById(@RequestBody AdvInfoEntity advInfo) {
        return R.ok(advInfoService.updateById(advInfo));
    }

    /**
     * 通过id删除广告信息管理
     *
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除广告信息管理", description = "通过id删除广告信息管理")
    @SysLog("通过id删除广告信息管理")
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('user_advInfo_del')")
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(advInfoService.removeBatchByIds(CollUtil.toList(ids)));
    }

    /**
     * 导出excel 表格
     *
     * @param advInfo 查询条件
     * @param ids     导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('user_advInfo_export')")
    public List<AdvInfoEntity> export(AdvInfoEntity advInfo, Integer[] ids) {
        return advInfoService.list(Wrappers.lambdaQuery(advInfo).in(ArrayUtil.isNotEmpty(ids), AdvInfoEntity::getId, ids));
    }
}