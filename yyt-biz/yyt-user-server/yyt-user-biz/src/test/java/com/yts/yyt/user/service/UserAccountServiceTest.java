package com.yts.yyt.user.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yts.yyt.user.api.constant.TradeDescConstants;
import com.yts.yyt.user.api.constant.enums.AccountEnum;
import com.yts.yyt.user.api.dto.AccountFundFlowFrontQueryDTO;
import com.yts.yyt.user.api.dto.AccountOperateDTO;
import com.yts.yyt.user.api.dto.AccountTransactionSettleDTO;
import com.yts.yyt.user.api.entity.AccountEntity;
import com.yts.yyt.user.api.vo.AccountFundFlowFrontVO;
import com.yts.yyt.user.api.vo.AccountVO;
import com.yts.yyt.user.api.vo.CosTempSecretVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

@Slf4j
public class UserAccountServiceTest extends BaseSpringTest {


    @Autowired
    private AccountService accountService;
    @Autowired
    private AccountFundFlowService accountFundFlowService;

    @Autowired
    private AccountFundFlowFrontService accountFundFlowFrontService;
    @Autowired
    private CosProcessor cosProcessor;
    @Autowired
    private UserBankCardService  bankCardService;

    @Test
    public void test_modify_fee(){
		log.info("【checkAndUpdateWithdrawalFeeRate】 开始");
		// 交通银行卡：6222620580010864762   招商：****************
		String fixFee = bankCardService.checkAndUpdateWithdrawalFeeRate("****************", "6222620580010864762");
		log.info("【checkAndUpdateWithdrawalFeeRate】 结束，手续费：{}",fixFee);

	}
    @Test
    public void test_temp(){
		CosTempSecretVO temp = cosProcessor.getTempCredential();
		log.info("【getTempCredential】 获取临时凭证：{}", JSONObject.toJSONString(temp));

	}
    @Test
    public void test_flow(){

        AccountFundFlowFrontQueryDTO dto = new AccountFundFlowFrontQueryDTO();
        dto.setUserId(1662215203879985152L);
        dto.setAccountType(AccountEnum.AccountTypeEnum.DEPOSIT_ACCOUNT.getType());
        Page<AccountFundFlowFrontVO> page = accountFundFlowFrontService.page(dto);
        System.out.println(JSON.toJSONString(page));


    }

    @Test
    public void test_add(){
        AccountOperateDTO dto = new AccountOperateDTO();
        dto.setTradeDesc("111");
        dto.setAmount(new BigDecimal("123"));
        dto.setAccountType(AccountEnum.AccountTypeEnum.DEPOSIT_ACCOUNT.getType());
        dto.setBusinessId("1222");
        dto.setUserId(1662215203879985152L);

        dto.setBusinessType(AccountEnum.BusinessTypeEnum.ORDER);
        dto.setBusinessTradeDesc(TradeDescConstants.DEPOSIT_PAY);
        accountService.addBalanceReturnAccountInfo(dto);

    }

    @Test
    public void testConcurrentAddBalance() throws InterruptedException {
        // 创建线程池
        ExecutorService executorService = Executors.newFixedThreadPool(30);
        CountDownLatch latch = new CountDownLatch(30);

        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);

        for (int i = 0; i < 30; i++) {
            executorService.submit(() -> {
                try {
                    AccountOperateDTO dto = new AccountOperateDTO();
                    dto.setUserId(1662215203879985152L);
                    dto.setAccountType(AccountEnum.AccountTypeEnum.DEPOSIT_ACCOUNT.getType());
                    dto.setAmount(new BigDecimal("0.99"));
                    dto.setTradeDesc("并发测试充值");
                    AccountVO result = null;
                    try {
                        System.out.println("操作金额...............................>>>>" + dto.getAmount());
                        result = accountService.addBalanceReturnAccountInfo(dto);
                    } catch (Exception e) {
                        System.out.println("操作失败...............................");
                    }
                    if (result != null) {
                        successCount.incrementAndGet();
                    } else {
                        failCount.incrementAndGet();
                    }
                } catch (Exception e) {
                    failCount.incrementAndGet();
                    System.err.println("操作失败：" + e.getMessage());
                } finally {
                    latch.countDown();
                }
            });
        }

        // 等待所有线程执行完毕
        latch.await();

        // 关闭线程池
        executorService.shutdown();

        // 查询最终账户信息
        AccountEntity finalAccount = accountService.getByUserIdAndType(1662215203879985152L, AccountEnum.AccountTypeEnum.DEPOSIT_ACCOUNT.getType());
        assertNotNull(finalAccount);

        // 打印结果
        System.out.println("成功次数：" + successCount.get());
        System.out.println("失败次数：" + failCount.get());
        System.out.println("最终总余额：" + finalAccount.getTotalBalance());

        // 预期金额为 THREAD_COUNT * AMOUNT
        assertEquals(new BigDecimal(30).multiply(new BigDecimal("0.99")), finalAccount.getTotalBalance());
    }

    @Test
    public void test_reduce(){
//        (userId=, accountType=BALANCE_ACCOUNT, amount=0.87, businessId=1927566599730954240, tradeDesc=订单退款成功，减冻结金额, businessTradeDesc=订单退款, businessType=REFUND)
        AccountOperateDTO dto = new AccountOperateDTO();
        dto.setTradeDesc("订单退款成功，减冻结金额");
        dto.setAmount(new BigDecimal("0.01"));
        dto.setAccountType(AccountEnum.AccountTypeEnum.BALANCE_ACCOUNT.getType());
        dto.setBusinessId("1927566599730954240");
        dto.setUserId(1688008522038939648L);

        dto.setBusinessType(AccountEnum.BusinessTypeEnum.REFUND);
        dto.setBusinessTradeDesc(TradeDescConstants.ORDER_REFUND);
        accountService.reduceFreezeBalance(dto);

    }

    @Test
    public void test_setting(){
        AccountTransactionSettleDTO transactionSettleDTO = new AccountTransactionSettleDTO();
        transactionSettleDTO.setUserId(1688008522038939648L);
        transactionSettleDTO.setAccountType(AccountEnum.AccountTypeEnum.BALANCE_ACCOUNT.getType());
        transactionSettleDTO.setAmount(new BigDecimal("0.10"));
        transactionSettleDTO.setFee(new BigDecimal("0.01"));
        transactionSettleDTO.setBusinessId("********");
        transactionSettleDTO.setTradeDesc(TradeDescConstants.ORDER_PAYMENT_SUCCESS);
        transactionSettleDTO.setFeeTradeDesc(TradeDescConstants.TRANSACTION_FEE);
        accountService.settleTransaction(transactionSettleDTO);
    }

    @Autowired
    private UserInfoService userInfoService;
    @Test
    public void test_send_msg(){
        userInfoService.sendLoginMobileMsg("***********");
    }

}