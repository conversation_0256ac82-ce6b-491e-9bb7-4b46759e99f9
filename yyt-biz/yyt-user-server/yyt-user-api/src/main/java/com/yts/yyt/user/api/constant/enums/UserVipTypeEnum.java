package com.yts.yyt.user.api.constant.enums;

/**
 * 会员类型枚举
 *
 * <AUTHOR>
 */
public interface UserVipTypeEnum {

    /**
     * 买家会员
     */
    interface BUYER {
        /**
         * 类型标识
         */
        String TYPE = "BUYER";
        /**
         * 类型名称
         */
        String NAME = "买家会员";
    }

    /**
     * 卖家会员
     */
    interface SELLER {
        /**
         * 类型标识
         */
        String TYPE = "SELLER";
        /**
         * 类型名称
         */
        String NAME = "卖家会员";
    }
}