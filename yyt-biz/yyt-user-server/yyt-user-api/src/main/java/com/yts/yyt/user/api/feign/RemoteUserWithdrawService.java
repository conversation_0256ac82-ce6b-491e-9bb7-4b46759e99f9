package com.yts.yyt.user.api.feign;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.feign.annotation.NoToken;
import com.yts.yyt.user.api.dto.WithdrawCallbackDTO;
import com.yts.yyt.user.api.vo.WithdrawRecordVO;

/**
 * 提现回调Feign接口
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
@FeignClient(contextId = "remoteUserWithdrawService", value = "yyt-user-biz")
public interface RemoteUserWithdrawService {

    /**
     * 提现成功回调
     *
     * @param dto 回调参数
     * @return 处理结果
     */
    @PostMapping("/withdraw/callback")
	@NoToken
    R<Boolean> withdrawCallback(@RequestBody WithdrawCallbackDTO dto);

    /**
     * 查询处理中且审核时间超过一个工作日的提现记录
     *
     * @return 提现记录列表
     */
    @PostMapping("/withdraw/processing-overdue")
    @NoToken
    R<List<WithdrawRecordVO>> queryProcessingOverdueWithdraws();
} 
