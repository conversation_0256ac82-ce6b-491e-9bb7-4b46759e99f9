package com.yts.yyt.user.api.dto;

import com.yts.yyt.user.api.constant.enums.AccountEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(description = "交易结算组合接口")
public class AccountTransactionSettleDTO {
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * 账户类型
     * @see AccountEnum.AccountTypeEnum
     */
    private String accountType;

    /**
     * 操作金额
     */
    private BigDecimal amount;

    /**
     *
     */
    private BigDecimal fee;

    /**
     * 业务ID
     */
    private String businessId;

    /**
     * 交易描述
     */
    private String tradeDesc;

    /**
     * 手续费交易描述
     */
    private String feeTradeDesc;

    /**
     * 交易前总余额
     */
    private BigDecimal beforeBalance;
}
