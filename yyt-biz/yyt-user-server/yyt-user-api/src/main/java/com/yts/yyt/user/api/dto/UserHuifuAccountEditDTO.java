package com.yts.yyt.user.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class UserHuifuAccountEditDTO {

	@Schema(description="移动用户ID")
	private Long userId;

	@Schema(description="系统用户ID")
	private Long sysUserId;

	@Schema(description="商家ID")
	private Long merchantId;

	@Schema(description="汇付账户ID")
	private String huifuId;

	@Schema(description="激活状态 0:未激活 1:进件激活成功 2:业务入驻失败 3:全部执行成功")
	private Integer status;

	@Schema(description="业务入驻错误信息")
	private String errorInfo;
}
