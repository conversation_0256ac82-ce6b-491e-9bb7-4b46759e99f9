package com.yts.yyt.user.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 意见反馈
 *
 * <AUTHOR>
 * @date 2025-01-09 15:04:01
 */
@Data
@TableName("user_feedback")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "意见反馈")
public class UserFeedbackEntity extends Model<UserFeedbackEntity> {


	/**
	* 主键id
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键id")
    private Long id;

	/**
	* 反馈类型
	*/
    @Schema(description="反馈类型")
    private String type;

	/**
	* 反馈内容
	*/
    @Schema(description="反馈内容")
    private String content;

	/**
	* 联系方式
	*/
    @Schema(description="联系方式")
    private String contact;

	/**
	* 图片数据
	*/
    @Schema(description="图片数据")
    private String imgs;

	/**
	* 处理状态
	*/
    @Schema(description="处理状态")
    private Integer status;

	/**
	* 最终状态
	*/
    @Schema(description="最终状态")
    private Integer finalStatus;

	/**
	* 回复内容
	*/
    @Schema(description="回复内容")
    private String reply;

	/**
	* 回复时间
	*/
    @Schema(description="回复时间")
    private LocalDateTime replyTime;

	/**
	* 反馈用户ID
	*/
    @Schema(description="反馈用户ID")
    private Long userId;

	/**
	* 反馈等级（0初始）
	*/
    @Schema(description="反馈等级（0初始）")
    private Integer level;

	/**
	* 初始ID
	*/
    @Schema(description="初始ID")
    private Long startId;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 删除标记
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标记")
    private String delFlag;
}