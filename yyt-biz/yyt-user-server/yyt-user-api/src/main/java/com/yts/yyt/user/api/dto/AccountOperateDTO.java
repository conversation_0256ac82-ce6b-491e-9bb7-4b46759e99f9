package com.yts.yyt.user.api.dto;

import com.yts.yyt.user.api.constant.enums.AccountEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 账户操作DTO
 *
 * <AUTHOR>
 * @date 2025-01-06 20:45:45
 */
@Data
@Schema(description = "账户操作DTO")
public class AccountOperateDTO {

    @Schema(description = "用户ID")
    private Long userId;

    /**
     * @see AccountEnum.AccountTypeEnum
     */
    @Schema(description = "账户类型")
    private String accountType;

    @Schema(description = "操作金额")
    private BigDecimal amount;

    @Schema(description = "业务ID")
    private String businessId;

    @Schema(description = "交易描述")
    private String tradeDesc;


    @Schema(description = "业务交易描述(前端)")
    private String businessTradeDesc;

    /**
     * @see AccountEnum.BusinessTypeEnum
     */
    @Schema(description = "业务类型")
    private AccountEnum.BusinessTypeEnum businessType;

} 