package com.yts.yyt.user.api.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class WeAppPhoneNumberVO extends WeAppBaseVO implements Serializable {

    /**
     * 用户手机号信息
     */
    private PhoneInfoVO phone_info;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PhoneInfoVO implements Serializable {
        /**
         * 没有区号的手机号
         */
        private String purePhoneNumber;

        /**
         * 用户绑定的手机号（国外手机号会有区号）
         */
        private String phoneNumber;

        /**
         * 	区号
         */
        private String countryCode;
    }
}
