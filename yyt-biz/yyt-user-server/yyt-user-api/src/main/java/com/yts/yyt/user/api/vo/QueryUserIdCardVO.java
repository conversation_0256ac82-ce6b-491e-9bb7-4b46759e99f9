package com.yts.yyt.user.api.vo;

import com.yts.yyt.common.sensitive.annotation.Sensitive;
import com.yts.yyt.common.sensitive.core.SensitiveTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class QueryUserIdCardVO {
	
	/**
     * 姓名（人像面）
     */
    private String name;

    /**
     * 性别（人像面）
     */
    private String Sex;

    /**
     * 民族（人像面）
     */
    private String Nation;

    /**
     * 出生日期（人像面）
     */
    private String Birth;

    /**
     * 地址（人像面）
     */
    private String address;

    /**
     * 身份证号（人像面）
     */
    private String idNum;

    /**
     * 发证机关（国徽面）
     */
    private String Authority;

    /**
     * 证件有效期（国徽面）
     */
    private String ValidDate;
    
    
    /**
     * 审核状态（0待审核 1通过 2拒绝）
     */
    private Integer authStatus;

    /**
     * 身份证正面地址
     */
    @Schema(description = "身份证正面地址")
    private String idFrontUrl;

    /**
     * 身份证反面地址
     */
    @Schema(description = "身份证反面地址")
    private String idBackUrl;
}
