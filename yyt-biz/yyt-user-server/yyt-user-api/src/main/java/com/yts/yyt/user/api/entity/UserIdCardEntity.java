package com.yts.yyt.user.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户身份证信息
 *
 * <AUTHOR>
 * @date 2025-01-06 20:55:13
 */
@Data
@TableName("user_id_card")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "用户身份证信息")
public class UserIdCardEntity extends Model<UserIdCardEntity> {


    /**
     * 主键（用户ID）
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键（用户ID）")
    private Long id;

    /**
     * 真实姓名
     */
    @Schema(description = "真实姓名")
    private String realname;

    /**
     * 身份证编号
     */
    @Schema(description = "身份证编号")
    private String idNo;

    /**
     * 身份证地址
     */
    @Schema(description = "身份证地址")
    private String idAddress;

    /**
     * 身份证正面地址
     */
    @Schema(description = "身份证正面地址")
    private String idFrontUrl;

    /**
     * 身份证反面地址
     */
    @Schema(description = "身份证反面地址")
    private String idBackUrl;

    /**
     * 身份证开始有效期
     */
    @Schema(description = "身份证开始有效期")
    private LocalDate idStartValidDate;

    /**
     * 身份证结束有效期
     */
    @Schema(description = "身份证结束有效期")
    private LocalDate idEndValidDate;
    
    /**
     * 是否长期:1-长期,0-非长期
     */
    @Schema(description = "是否长期:1-长期,0-非长期")
    private Integer isLong;

    /**
     * 审核状态（0待审核 1通过 2拒绝）
     */
    @Schema(description = "审核状态（0待审核 1通过 2拒绝）")
    private Integer authStatus;

    /**
     * 审核备注
     */
    @Schema(description = "审核备注")
    private String authRemark;

    /**
     * 审核时间
     */
    @Schema(description = "审核时间")
    private LocalDateTime authTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;
}