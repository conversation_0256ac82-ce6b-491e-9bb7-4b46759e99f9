package com.yts.yyt.user.api.vo;

import com.yts.yyt.user.api.entity.UserInfoEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;

@Data
public class UserVipInfoDetailVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "用户信息")
    private UserInfoEntity userInfo;

    @Schema(description = "买家会员等级")
    private String buyerVipLevel;

    @Schema(description = "卖家会员等级")
    private String sellerVipLevel;

    @Schema(description = "完成的商城订单量")
    private Long completedMallOrderCount;

    @Schema(description = "完成的商城订单交易额")
    private String completedMallOrderAmount;
}
