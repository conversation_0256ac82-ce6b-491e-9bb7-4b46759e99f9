package com.yts.yyt.user.api.feign;

import com.yts.yyt.common.core.constant.ServiceNameConstants;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.feign.annotation.NoToken;
import com.yts.yyt.user.api.dto.*;
import com.yts.yyt.user.api.entity.UserInterestEntity;
import com.yts.yyt.user.api.vo.AppUserVO;
import com.yts.yyt.user.api.vo.UserInfoVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/6/22
 */
@FeignClient(contextId = "remoteBizUserInfoService", value = ServiceNameConstants.BIZ_USER_SERVER)
public interface RemoteBizUserInfoService {

    /**
     * 通过用户ID查询用户信息
     *
     * @param id 用户ID
     * @return R
     */
    @NoToken
    @GetMapping("/user/info/id/{id}")
    R<UserInfoVO> info(@PathVariable("id") Long id);

    /**
     * 保存用户openId
     *
     * @param dto dto
     * @return R
     */
    @NoToken
	@PostMapping("/user/saveOpenId")
	R<Boolean> saveOpenId(@RequestBody UserSaveOpenIdDTO dto);

    /**
     * 通过用户ID查询用户信息
     *
     * @param id 用户ID
     * @return R
     */
    @NoToken
    @GetMapping("/user/info/detail")
    R<UserInfoVO> getDetailById(@RequestParam Long id);

    /**
     * 通过openId查询用户信息
     *
     * @param openId openId
     * @return R
     */
    @NoToken
    @GetMapping("/user/info/by/open/id")
    R<UserInfoVO> getByOpenId(@RequestParam String openId);

    /**
     * 通过(社交账号、手机号)  -> inputStr 查询用户、角色信息
     *
     * @param request 传入参数
     * @return R
     */
    @NoToken
    @PostMapping("/app/social/info")
    R<AppUserVO> social(@RequestBody AppSocialDTO request);

    /**
     * 检查用户是否是前(registerRank)名注册,(排除注销后的用户)
     *
     * @param userId        用户ID
     * @param registerRank  排行
     * @return R
     */
    @NoToken
    @GetMapping("/check/exist/register/rank")
    R<Boolean> checkExistRegisterRank(@RequestParam Long userId, @RequestParam Integer registerRank);

    /**
     * 通过手机号查询用户信息
     *
     * @param mobile 手机号
     * @return R
     */
    @NoToken
    @GetMapping("/user/getByMobile/{mobile}")
    R<UserInfoVO> getByMobile(@PathVariable("mobile") String mobile);

    /**
     * 发送系统消息
     */
    @NoToken
    @PostMapping("/send/system/message")
    R<Void> sendSystemMessage(@RequestBody SendUserMessageDTO request);

    /**
     * 用户感兴趣列表
     */
    @NoToken
	@GetMapping("/user/userInterestListByUserId")
	R<List<UserInterestEntity>> getUserInterestListByUserId(@RequestParam Long userId);
    /**
     * 用户感兴趣列表
     */
    @NoToken
	@GetMapping("/admin/userInfo/deleteToken")
	R<Boolean> deleteToken(@RequestParam String userName);

	/**
	 * 增加App用户信息
	 */
	@NoToken
	@PostMapping("/admin/userInfo/addAppUser")
	R<Long> addAppUser(AppRegisterDTO params);


    /**
     * 同步用户信息到IM系统
     * @return 同步结果
     */
    @NoToken
    @PostMapping("/user/sync/im")
    R<Boolean> syncUserToIm();

    /**
     * 合伙人后台,禁用启用移动端用户
     */
    @NoToken
    @PostMapping("/disableEnable")
    R<Boolean> disableEnable(@RequestBody UserInfoDisableEnableDTO dto);
}
