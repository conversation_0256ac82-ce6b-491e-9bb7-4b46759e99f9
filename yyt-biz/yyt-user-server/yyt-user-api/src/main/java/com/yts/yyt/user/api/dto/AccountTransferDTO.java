package com.yts.yyt.user.api.dto;

import com.yts.yyt.user.api.constant.enums.AccountEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 账户转账DTO
 *
 * <AUTHOR>
 * @date 2025-01-06 20:45:45
 */
@Data
@Schema(description = "账户转账DTO")
public class AccountTransferDTO {

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "转出账户类型")
    private String fromAccountType;

    @Schema(description = "转入账户类型")
    private String toAccountType;

    @Schema(description = "转账金额")
    private BigDecimal amount;


    @Schema(description = "业务交易描述(前端)")
    private String fromBusinessTradeDesc;

    @Schema(description = "业务交易描述(前端)")
    private String toBusinessTradeDesc;

    @Schema(description = "业务ID")
    private String businessId;
}