package com.yts.yyt.user.api.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 账户流水类型枚举
 *
 * <AUTHOR>
 * @date 2025-01-06 20:45:45
 */
@Getter
@AllArgsConstructor
public enum AccountFlowTypeEnum {

    /**
     * 转账转出
     */
    TRANSFER_OUT("TRANSFER_OUT", "转账转出"),

    /**
     * 转账转入
     */
    TRANSFER_IN("TRANSFER_IN", "转账转入");

    private final String code;
    private final String desc;
} 