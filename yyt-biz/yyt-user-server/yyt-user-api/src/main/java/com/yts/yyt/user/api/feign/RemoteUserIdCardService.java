package com.yts.yyt.user.api.feign;

import com.yts.yyt.common.core.constant.ServiceNameConstants;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.feign.annotation.NoToken;
import com.yts.yyt.user.api.vo.QueryUserAddressVO;
import com.yts.yyt.user.api.vo.QueryUserIdCardVO;
import com.yts.yyt.user.api.vo.QueryUserInfoVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <AUTHOR>
 * @date 2018/6/22
 */
@FeignClient(contextId = "remoteUserIdCardService", value = ServiceNameConstants.BIZ_USER_SERVER,path = "/userIdCard")
public interface RemoteUserIdCardService {

	/**
	 * 通过用户ID查询用户信息
	 *
	 * @param id 用户ID
	 * @return R
	 */
	@NoToken
	@GetMapping("/info/{id}")
	R<QueryUserInfoVO> info(@PathVariable("id") Long id);

	
}
