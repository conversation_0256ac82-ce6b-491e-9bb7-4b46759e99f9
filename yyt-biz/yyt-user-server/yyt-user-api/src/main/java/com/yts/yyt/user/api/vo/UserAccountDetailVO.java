package com.yts.yyt.user.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public class UserAccountDetailVO implements Serializable {
	@Serial
	private static final long serialVersionUID = 1L;

	@Schema(description = "账户可用余额")
	private String accountBalance;

	@Schema(description = "消保金可用余额")
	private String protectionBalance;

	@Schema(description = "冻结总金额")
	private String frozenAmount;

	@Schema(description = "消费总额")
	private String totalConsumption;

    /**
     * 实际付款金额
     */
    @Schema(description="实际付款金额")
    private String actualAmount;

	@Schema(description = "成交订单数")
	private String completedOrders;

	@Schema(description = "银行卡列表")
	private List<BankCardInfo> bankCardList;

	@Data
	public static class BankCardInfo {
		@Schema(description = "账户类型")
		private String accountType;

		@Schema(description = "所在省市")
		private String location;

		@Schema(description = "银行卡号")
		private String cardNumber;

		@Schema(description = "银行名称")
		private String bankName;

		@Schema(description = "银行号")
		private String bankCode;

		@Schema(description = "支付联号")
		private String paymentCode;

		@Schema(description = "预留手机号")
		private String reservedPhone;

		@Schema(description = "持卡人")
		private String cardHolder;

		@Schema(description = "绑定时间")
		private String bindTime;
	}
}
