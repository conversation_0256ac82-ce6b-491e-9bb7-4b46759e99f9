//package com.yts.yyt.live.service;
//
//import com.yts.yyt.common.tencent.service.TLiveService;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//@SpringBootTest
//@RunWith(SpringRunner.class)
//public class TencentLiveServiceTest {
//
//    @Autowired
//    private TLiveService tLiveService;
//
//    @Test
//    public void test_url(){
//        String s1 = tLiveService.getPullUrl("abc123");
//        System.out.println(s1);
//        String s2 = tLiveService.getPushUrl("abc123");
//        System.out.println(s2 );
//        System.out.println("------------------------------");
//        tLiveService.describeLiveTranscodeTemplateRequest();
//    }
//
//}
