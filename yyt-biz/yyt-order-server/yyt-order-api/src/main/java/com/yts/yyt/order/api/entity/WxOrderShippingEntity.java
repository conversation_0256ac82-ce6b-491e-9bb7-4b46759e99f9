package com.yts.yyt.order.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 微信小程序订单发货信息
 *
 * <AUTHOR>
 * @date 2025-01-06 19:28:37
 */
@Data
@TableName("wx_order_shipping")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "微信小程序订单发货信息")
public class WxOrderShippingEntity extends Model<WxOrderShippingEntity> {

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键ID")
    private Long id;
    
    /**
     * 业务id
     */
    @Schema(description="业务id")
    private Long businessId;
    
    /**
     * 订单单号类型：1-商户侧单号形式，2-微信支付单号形式
     */
    @Schema(description="订单单号类型：1-商户侧单号形式，2-微信支付单号形式")
    private Integer orderNumberType;
    
    /**
     * 微信支付交易单号
     */
    @Schema(description="微信支付交易单号")
    private String transactionId;
    
    /**
     * 物流类型：1-实体物流，2-同城配送，3-虚拟商品，4-用户自提
     */
    @Schema(description="物流类型：1-实体物流，2-同城配送，3-虚拟商品，4-用户自提")
    private Integer logisticsType;
    
    /**
     * 发货模式：1-统一发货，2-分拆发货
     */
    @Schema(description="发货模式：1-统一发货，2-分拆发货")
    private Integer deliveryMode;
    
    /**
     * 物流单号（快递发货必填）
     */
    @Schema(description="物流单号（快递发货必填）")
    private String trackingNo;
    
    /**
     * 物流公司编码（快递发货必填）
     */
    @Schema(description="物流公司编码（快递发货必填）")
    private String expressCompany;
    
    /**
     * 商品信息描述，如：微信红包抱枕*1个
     */
    @Schema(description="商品信息描述，如：微信红包抱枕*1个")
    private String itemDesc;
    
    /**
     * 寄件人联系方式（顺丰时必填）
     */
    @Schema(description="寄件人联系方式（顺丰时必填）")
    private String consignorContact;
    
    /**
     * 收件人联系方式（顺丰时必填）
     */
    @Schema(description="收件人联系方式（顺丰时必填）")
    private String receiverContact;
    
    /**
     * 上传时间，RFC 3339格式
     */
    @Schema(description="上传时间，RFC 3339格式")
    private LocalDateTime uploadTime;
    
    /**
     * 用户在小程序下的唯一标识
     */
    @Schema(description="用户在小程序下的唯一标识")
    private String openid;
    
    /**
     * 发货状态：0-未上传，1-已上传成功，2-上传失败，3-草稿待处理
     * @see com.yts.yyt.order.api.enums.WxOrderEnum.ShippingStatus
     */
    @Schema(description="发货状态：0-未上传，1-已上传成功，2-上传失败，3-草稿待处理")
    private Integer status;
    
    /**
     * 错误信息
     */
    @Schema(description="错误信息")
    private String errorMsg;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;
} 
