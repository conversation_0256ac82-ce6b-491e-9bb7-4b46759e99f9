package com.yts.yyt.order.api.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户优惠券DTO
 *
 * <AUTHOR>
 * @date 2024-02-13
 */
@Data
@Schema(description = "用户优惠券DTO")
public class UserCouponDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "用户优惠券ID")
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "优惠券模板ID")
    private Long couponTemplateId;

    @Schema(description = "优惠券名称")
    private String couponName;

    @Schema(description = "优惠金额")
    private BigDecimal faceValue;

    @Schema(description = "使用门槛")
    private BigDecimal threshold;

    @Schema(description = "使用平台：1-APP 2-小程序 3-全平台")
    private String platform;

    @Schema(description = "有效期开始时间")
    private LocalDateTime validStartTime;

    @Schema(description = "有效期结束时间")
    private LocalDateTime validEndTime;

    @Schema(description = "使用状态：0-未使用 1-已使用 2-已过期")
    private Integer status;

    @Schema(description = "使用时间")
    private LocalDateTime useTime;

    @Schema(description = "订单ID")
    private Long orderId;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
} 
