package com.yts.yyt.order.api.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.yts.yyt.order.api.entity.LogisticsCompanyEntity;
import com.yts.yyt.order.api.entity.OrderLogisticsEntity;
import com.yts.yyt.order.api.enums.OrderEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(description = "首发订单详情")
public class AdminFirstLaunchOrderDetailVO implements Serializable {


	/**
	 * 物流信息
	 */
	private OrderLogisticsEntity orderLogistics;
	/**
	 * 订单编号
	 */
	@Schema(description="订单编号")
	private String orderNo;

	/**
	 * 用户ID
	 */
	@Schema(description="用户ID")
	private Long userId;

	/**
	 * 订单状态
	 * @see OrderEnum.OrderStatus
	 */
	@Schema(description="订单状态")
	private String status;

	/**
	 * 订单来源 1-小程序 2-APP
	 */
	@Schema(description="订单来源 1-小程序 2-APP")
	private Integer orderSource;

	/**
	 * 首发价格
	 */
	@Schema(description="首发价格")
	private BigDecimal ipoAmount;

	/**
	 * 金额
	 */
	@Schema(description="金额")
	private BigDecimal amount;

	/**
	 * 1-微信小程序 2-微信 3-支付宝 4-三方支付 5-线下支付
	 * @see OrderEnum.OrderPayType
	 */
	@Schema(description="1-微信小程序 2-微信 3-支付宝 4-三方支付 5-线下支付")
	private Integer payType;

	/**
	 * 支付流水号
	 */
	@Schema(description="支付流水号")
	private String paySerio;

	/**
	 * 支付凭证
	 */
	@Schema(description="支付凭证")
	private String payImages;


	/**
	 * 支付状态 0-未支付，1-支付成功，2-支付失败
	 * @see OrderEnum.OrderPayStatus
	 */
	@Schema(description="支付状态 0-未支付，1-支付成功，2-支付失败")
	private Integer payStatus;


	/**
	 * 备注
	 */
	@Schema(description="备注")
	private String remark;

	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	@Schema(description="创建时间")
	private LocalDateTime createTime;

	@Schema(description = "名称")
	private String goodsName;

	@Schema(description="昵称")
	private String nickName;

    /**
     * 商品ID
     */
    @Schema(description="商品ID")
    private Long goodsId;

    /**
     * 拍品ID
     */
    @Schema(description="拍品ID")
    private Long lotId;
    
	@Schema(description="商品编码")
	private String goodsNo;

	@Schema(description="商品当前价")
	private BigDecimal currentPrice;

	@Schema(description="商品销售价")
	private BigDecimal salePrice;

	@Schema(description="商品库存")
	private Integer stockQuantity;

	@Schema(description = "订单类型 1- 首发")
	private String orderType;

	@Schema(description = "取件码")
	private String pickupCode;

	/**
	 * @see com.yts.yyt.order.api.enums.OrderEnum.ProtectionStatus
	 */
	@Schema(description = "退款订单表状态")
	private Integer protectionTableStatus;

	/**
	 * 退款原因
	 */
	@Schema(description="退款原因")
	private String reason;

    @Schema(description="物流公司id")
    private Long logisticsCompanyId;

    @Schema(description = "物流公司")
    private LogisticsCompanyEntity logisticsCompany;

    @Schema(description = "用户券ID")
    private Long userCouponId;

    @Schema(description = "优惠金额")
    private BigDecimal couponAmount;
}
