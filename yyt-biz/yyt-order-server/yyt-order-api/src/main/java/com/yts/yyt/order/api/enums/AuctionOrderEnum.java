package com.yts.yyt.order.api.enums;

public interface AuctionOrderEnum {

    public enum AuctionOrderStatus {
        OFFER("offer", "已出价"),
        WON("won", "已中拍"),
        LOST("lost", "未中拍"),
        PENDING_PAYMENT("pending_payment", "待付款"),
        PENDING_PROCESS("pending_process", "待处理"),
        CONSIGNMENT_ONLINE("consignment_online", "商城寄售中"),
        CONSIGNMENT_OFFLINE("consignment_offline", "门店寄售中"),

        CONSIGNMENT_SELLING_SUCCESS("consignment_selling_success", "寄售成功"),
        CONSIGNMENT_UNSOLD("consignment_unsold", "寄售未卖出"),
        PENDING_DELIVERY("pending_delivery", "待发货"),
        PENDING_RECEIPT("pending_receipt", "待收货"),
        COMPLETED("completed", "已完成"),
        CLOSED("closed", "已关闭"),
        AFTER_SALE("after_sale", "售后中"),
        REPURCHASE_COMPLETED("repurchase_completed", "回购完成"),
        CANCEL("cancel", "已取消"),
        PENDING_AUDIT("pending_audit", "待审核"),

        ;

        private String status;
        private String desc;

        AuctionOrderStatus(String status, String desc) {
            this.status = status;
            this.desc = desc;
        }

        public String getStatus() {
            return status;
        }

        public String getDesc() {
            return desc;
        }
    }



}
