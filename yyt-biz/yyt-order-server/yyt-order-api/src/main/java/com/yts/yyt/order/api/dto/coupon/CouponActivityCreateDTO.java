package com.yts.yyt.order.api.dto.coupon;

import java.io.Serializable;

import jakarta.validation.constraints.Size;

import com.yts.yyt.order.api.enums.CouponEnum;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 优惠券活动创建DTO
 *
 * <AUTHOR>
 * @date 2024-02-13
 */
@Data
@Schema(description = "优惠券活动创建DTO")
public class CouponActivityCreateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 活动主标题
     */
    @Schema(description = "活动主标题")
	@Size(max = 30, message = "活动主标题长度不能超过30个字符")
    private String mainTitle;

    /**
     * 活动副标题
     */
    @Schema(description = "活动副标题")
	@Size(max = 30, message = "活动副标题长度不能超过30个字符")
    private String subTitle;

    /**
     * 活动名称
     */
    @Schema(description = "活动名称")
    private String name;

    /**
     * 触发方式
     * @see CouponEnum.activityMode
     */
    @Schema(description = "触发方式：1-被动发放 2-主动领取")
    private Integer mode;

    /**
     * 策略类型
     * @see CouponEnum.activityType
     */
    @Schema(description = "策略类型：register/invite/auth")
    private String code;
} 
