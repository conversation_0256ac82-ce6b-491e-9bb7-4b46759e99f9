package com.yts.yyt.order.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "退款转账DTO")
public class OrderProtectionTransferDTO implements Serializable {
	@Schema(description = "当前登录用户userId", hidden = true)
	private Long loginUserId;

	@NotNull(message = "订单ID不能为空")
	@Schema(description = "订单id")
	private Long orderId;

	@Schema(description = "转账退款凭证")
	private List<String> refundVoucherImgs;
}
