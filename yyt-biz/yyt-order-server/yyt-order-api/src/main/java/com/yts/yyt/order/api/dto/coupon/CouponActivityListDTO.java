package com.yts.yyt.order.api.dto.coupon;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 优惠券活动列表查询DTO
 *
 * <AUTHOR>
 * @date 2024-02-13
 */
@Data
@Schema(description = "优惠券活动列表查询DTO")
public class CouponActivityListDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 搜索关键词(活动名称/创建人)
     */
    @Schema(description = "搜索关键词(活动名称/创建人)")
    private String keyword;

    /**
     * 创建时间-开始
     */
    @Schema(description = "创建时间-开始")
    private String createTimeStart;

    /**
     * 创建时间-结束
     */
    @Schema(description = "创建时间-结束")
    private String createTimeEnd;

    /**
     * 当前页码
     */
    @Schema(description = "当前页码")
    private Long current = 1L;

    /**
     * 每页条数
     */
    @Schema(description = "每页条数")
    private Long size = 10L;
} 
