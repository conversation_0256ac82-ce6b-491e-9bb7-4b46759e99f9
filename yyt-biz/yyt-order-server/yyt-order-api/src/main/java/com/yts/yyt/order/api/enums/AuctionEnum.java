package com.yts.yyt.order.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

public interface AuctionEnum {

	/**
 * 订单参数枚举
 */
@AllArgsConstructor
@Getter
public static enum AuctionParamEnum {
    
    AUCTION_CONSIGNMENT_PRICE("AUCTION_CONSIGNMENT_PRICE","竞拍门店寄售价格"),

    /**
     * 再次竞拍冷却期(天)
     */
    AUCTION_RESUBMIT_COOLING_DAYS("AUCTION_RESUBMIT_COOLING_DAYS", "再次竞拍冷却期"),

    /**
     * 拍卖周期(小时)
     */
    AUCTION_PERIOD("AUCTION_PERIOD", "拍卖周期"),

    /**
     * 卖家确认过期时间(小时)
     */
    AUCTION_SELLER_CONFIRM_EXPIRE_TIME("AUCTION_SELLER_CONFIRM_EXPIRE_TIME", "卖家确认过期时间"),

    ;

    private final String key;
    private final String desc;

}


	/**
	 * 竞拍申请状态
	 */
	@AllArgsConstructor
	@Getter
	public static enum ApplyStatus{
		AUCTION_STATUS_PENDING_SEND("auction_status_pending_send", "待寄出"),
		AUCTION_STATUS_SENT("auction_status_sent", "已寄出"),
		AUCTION_STATUS_BIDDING("auction_status_bidding", "竞拍中"),
		AUCTION_STATUS_PENDING_CONFIRM("auction_status_pending_confirm", "待确认"),
		AUCTION_STATUS_CONSIGNMENT("auction_status_consignment", "寄售中"),
		AUCTION_STATUS_PENDING_RECOVERY("auction_status_pending_recovery", "待收回"),	
		AUCTION_STATUS_ALL("auction_status_all", "全部"),
		;

		private final String type;

		private final String desc;


	}

	  /**
     * 竞拍申请子状态
     */
    @AllArgsConstructor
    @Getter
    public static enum ApplySubStatusEnum {
        // 寄售相关状态
        AUCTION_SUB_STORE_CONSIGNMENT("auction_sub_store_consignment", "门店寄售", ApplyStatus.AUCTION_STATUS_CONSIGNMENT),
        AUCTION_SUB_ONLINE_CONSIGNMENT("auction_sub_online_consignment", "线上寄售", ApplyStatus.AUCTION_STATUS_CONSIGNMENT),
        
        // 待寄出
        AUCTION_SUB_PENDING_REVIEW("auction_sub_pending_review", "待审核", ApplyStatus.AUCTION_STATUS_PENDING_SEND),
        
		//已寄出
        AUCTION_SUB_PENDING_PUBLISH("auction_sub_pending_publish", "待发布", ApplyStatus.AUCTION_STATUS_SENT),

		// 竞拍中相关状态
		AUCTION_SUB_BIDDING("auction_sub_bidding", "竞拍中", ApplyStatus.AUCTION_STATUS_BIDDING),
		AUCTION_SUB_PENDING_PAYMENT("auction_sub_pending_payment", "待收款", ApplyStatus.AUCTION_STATUS_BIDDING),

		// 待确认状态
        AUCTION_SUB_PENDING_CONFIRM("auction_sub_pending_confirm", "待确认", ApplyStatus.AUCTION_STATUS_PENDING_CONFIRM),

		        
        // 待收回相关状态
        AUCTION_SUB_PENDING_RECOVERY("auction_sub_pending_recovery", "待收回", ApplyStatus.AUCTION_STATUS_PENDING_RECOVERY),
        AUCTION_SUB_REVIEW_REJECTED("auction_sub_review_rejected", "审核未通过", ApplyStatus.AUCTION_STATUS_PENDING_RECOVERY),
        AUCTION_SUB_PENDING_DELIVERY("auction_sub_pending_delivery", "待发货", ApplyStatus.AUCTION_STATUS_PENDING_RECOVERY),
        AUCTION_SUB_DELIVERED("auction_sub_delivered", "已发货", ApplyStatus.AUCTION_STATUS_PENDING_RECOVERY),

		// 全部状态相关
		AUCTION_SUB_CANCELED("auction_sub_canceled", "已取消", ApplyStatus.AUCTION_STATUS_ALL),
		AUCTION_SUB_COMPLETED("auction_sub_completed", "已完成", ApplyStatus.AUCTION_STATUS_ALL),
		AUCTION_SUB_FAILED("auction_sub_failed", "已流拍", ApplyStatus.AUCTION_STATUS_ALL),
		AUCTION_SUB_DEAL("auction_sub_deal", "已成交", ApplyStatus.AUCTION_STATUS_ALL),
		// 寄售完成相关状态
		AUCTION_SUB_STORE_CONSIGNMENT_COMPLETED("auction_sub_store_consignment_completed", "门店寄售已完成", ApplyStatus.AUCTION_STATUS_ALL),
		AUCTION_SUB_MALL_CONSIGNMENT_COMPLETED("auction_sub_mall_consignment_completed", "商城寄售已完成", ApplyStatus.AUCTION_STATUS_ALL),
        ;

        private final String type;
        private final String desc;
        private final ApplyStatus parentStatus;
    }
}
