package com.yts.yyt.order.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 订单物流信息
 *
 * <AUTHOR>
 * @date 2025-01-06 19:27:17
 */
@Data
@Schema(description = "订单物流信息")
public class OrderLogisticsInfoVO implements Serializable {

 
	/**
	* id
	*/
    @Schema(description="id")
    private Long id;
 
	/**
	* orderId
	*/
    @Schema(description="orderId")
    private Long orderId;

	/**
	* 运单号
	*/
    @Schema(description="运单号")
    private String logisticsNumber;

	/**
	 * 快递公司
	 */
	@Schema(description="快递公司")
	private String logisticsCompany;

	/**
	 * 快递公司编码
	 */
	@Schema(description="快递公司编码")
	private String logisticsCompanyCode;

	/**
	* 发货方式
	 * @see com.yts.yyt.order.api.enums.OrderLogisticsEnum
	*/
    @Schema(description="发货方式 0快递上门 1自行寄出(快递单号) 2送货上门(自提)")
    private Integer type;
    

	/**
	* 省
	*/
    @Schema(description="省")
    private Integer provinceId;

	/**
	* 市
	*/
    @Schema(description="市")
    private Integer cityId;

	/**
	* 区
	*/
    @Schema(description="区")
    private Integer areaId;
 
	/**
	* address
	*/
    @Schema(description="address")
    private String address;

	/**
	* 地址标签
	*/
    @Schema(description="地址标签")
    private String label;
 
	/**
	* createTime
	*/
    @Schema(description="createTime")
    private LocalDateTime createTime;
 
	/**
	* updateTime
	*/
    @Schema(description="updateTime")
    private LocalDateTime updateTime;

	@Schema(description="预约开始时间 快递上门/送货上门 填写")
	private LocalDateTime reserveBeginTime;

	@Schema(description="预约结束时间 快递上门/送货上门 填写")
	private LocalDateTime reserveEndTime;
}
