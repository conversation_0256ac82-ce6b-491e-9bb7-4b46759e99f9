package com.yts.yyt.order.api.dto;

import com.yts.yyt.order.api.enums.OrderSearchEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
public class PageBuyBackDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private Long userId;

    /**
     * @see OrderSearchEnum
     */
    @Schema(description = "查询字段")
    private String queryType;

    @Schema(description = "查询字段值")
    private String queryWords;

    @Schema(description = "开始时间")
    private String beginTime;

    @Schema(description = "结束时间")
    private String endTime;

    @Schema(description = "订单状态")
    private String orderStatus;

}
