package com.yts.yyt.order.api.exception;

public enum OrderBizErrorCodeEnum {
    GET_DICTIONARY_CONFIGURATION_EXCEPTION(20000, "获取字典配置异常"),
    THE_DICTIONARY_CONFIGURATION_DOES_NOT_EXIST(20001, "该字典配置不存在"),
    FAILED_TO_BUILD_CONTRACT_AGREEMENT_PARAMETERS(20002, "构建合同协议参数失败"),
    UNSUPPORTED_PROTOCOL_TYPE(20003, "不支持的协议类型"),

    THE_PARAM_DOES_NOT_EXIST(20001, "参数配置不存在"),
    IDENTITY_AUTH_NOT_COMPLETED(20003, "未完成身份认证"),
    WALLET_NOT_ACTIVATED(20004, "未开通钱包"),
    USER_NOT_EXIST(20005, "用户不存在"),
    USER_ADDRESS_NOT_EXIST(20006, "用户收件地址不存在"),
    USER_ICCARD_NOT_EXIST(20007, "用户身份证不存在"),
    USER_BANK_CARD_NOT_EXIST(20008, "该银行卡信息不存在"),

    ORDER_NOT_EXIST(20009, "订单不存在"),
    UPDATE_GOODS_INFO_ERROR(20010, "更新商品信息失败"),
    UPDATE_USER_GOODS_ORDER_ERROR(20011, "更新商品归属失败"),

    ORDER_HAS_AGREEMENT(20012, "该订单已签署合同"),
    ORDER_HAS_GENERATE_AGREEMENT(20013, "该订单已签署合同"),

    ORDER_STATUS_NOT_ALLOW_AUDIT(20014, "只能审核待审核状态的订单"),

    ORDER_STATUS_NOT_ALLOW_PAY(20015, "订单状态不允许支付"),

    BUY_BACK_NOT_FOUND(20100, "该回购订单不存在"),
    PLEASE_SIGN_THE_REPURCHASE_AGREEMENT_FIRST(20101, "请先签订回购协议"),
    BUY_BACK_THIS_STATUS_CANNOT_BE_OPT(20102, "该状态不允许操作"),
    BUY_BACK_PLEASE_CONTACT_FOR_REFUND(20103, "7天内不可申请回购，可申请退货退款，申请退货退款点击确定，否则取消"),
    BUY_BACK_HAS_NOT_BEEN_COMPLETED(20104, "不允许回购，首发订单尚未完成"),
    FAILED_TO_INITIATE_THE_SIGNING_OF_REPURCHASE_AGREEMENT(20105, "回购协议签署发起失败"),
    PLEASE_GO_TO_MY_ORDER_TO_CONFIRM_RECEIPT_BEFORE_SELLING(20106, "请去我的订单中进行确认收货后再卖出"),
    THE_USER_HAS_NOT_SIGNED_THE_AGREEMENT(20107, "用户未签协议"),
    UNABLE_TO_REPURCHASE_AGAIN(20107, "您好，这件藏品此前已经过回购评估并被拒绝，按照规定，无法再次进行回购操作。希望您能理解！"),

    CONSIGNMENT_NOT_FOUND(20400, "该寄售订单不存在"),
    UNSIGNEDCONSIGNMENT_AGREEMENT(20401, "请先签订寄售协议"),
    CONSIGNMENTORDER_IS_NOT_ON_CONSIGNMENT(20402, "订单非待发货状态不允许取消寄售"),
    THE_ORDER_DOES_NOT_ALLOW_CONSIGNMENT(20403, "该订单不允许发起寄售"),

    THE_ORDER_DOES_NOT_ALLOW_RECEIVE(20404, "该订单不允许操作确认收货"),

    THE_ORDERSTATUS_NOTIN_CONSIGNMENT(20405, "订单不在寄售状态"),

    CONSIGNMENTORDER_GOODS_STATUS_ERROR(20406, "商品状态异常"),

    CONSIGNMENTORDER_IS_NOT_ON_SALE(20407, "订单非寄售状态"),

    CONSIGNMENTORDER_CONTRACT_NOT_SIGNED(20408, "未签署寄售协议"),

    CONSIGNMENT_GOODS_HAS_BEEN_PURCHASED(20409, "商品已被购买"),

    CONSIGNMENT_GOODS_OWENER_NOT_MATCH(20410, "商品拥有者不匹配"),

    CONSIGNMENT_ORDER_HAS_AGREEMENT(20411, "文物艺术品委托寄售合同已签署"),

    CONSIGNMENT_STATUS_MISMATCH(20412, "寄售订单状态不匹配"),

    FIRST_LAUNCH_GOOD_NOT_FOUND(20300, "商品信息不存在"),
    FIRST_LAUNCH_ONLY_SUPPORTS_WX(20302, "首发订单只支持微信支付"),
    FIRST_LAUNCH_GOOD_NOT_SEALING(20303, "商品不在销售中"),

    FIRST_LAUNCH_NOT_FOUND(20304, "订单不存在"),
    FIRST_LAUNCH_TYPE_MISMATCH(20305, "订单类型不匹配"),
    FIRST_LAUNCH_STATUS_MISMATCH(20306, "订单状态不匹配"),

    FIRST_LAUNCH_NOT_LOGISTICS(20307, "订单物流不存在"),

    FIRST_LAUNCH_EXISTS_LOGISTICS(20308, "订单物流单号已存在"),

    FIRST_LAUNCH_NOT_USER(20309, "该订单所属用户不存在"),

    FIRST_LAUNCH_NOT_BUSINESS(20310, "用户未签署买卖协议"),

    FIRST_LAUNCH_OCR_FAILED(20311, "OCR身份识别失败"),

    ORDER_TYPE_NOT_EXIST(20312, "订单类型不存在"),

    ERROR_ORDER_NOT_FOUND(20313, "退款维权订单不存在"),

    DUPLICATE_APPLICATION_REFUND(20314, "不可重复申请退款"),

    ORDER_PROTECT_REFUNDING(20315, "订单正在退款中"),

    ORDER_PROTECT_REFUNED(20316, "订单已退款"),

    FIRST_LAUNCH_PERMISSION_DENIED(20317, "无权限购买"),

    LOGISTICS_INFO_NOT_EXIST(20318, "物流信息不存在"),
    FIRST_LAUNCH_BUYER_ERROR(20319, "购买人不能是出售人"),
    PARAM_ERROR(20320, "参数异常"),

    ADD_GOODS_TRANSACTION_ERROR(20321, "添加商品交易记录失败"),

    // 冻结保证金失败
    DEPOSIT_FREEZE_ERROR(20322, "冻结保证金失败"),
    // 扣除保证金失败
    DEPOSIT_DEDUCT_ERROR(20323, "扣除保证金失败"),
    // 变更账户余额失败
    BALANCE_CHANGE_ERROR(20324, "变更账户余额失败"),
    // 获取保证金余额失败
    DEPOSIT_BALANCE_ERROR(20325, "获取保证金余额失败"),
    // 解冻保证金失败
    DEPOSIT_UNFREEZE_ERROR(20326, "解冻保证金失败"),

    ORDER_PROTECT_ADDRESS_ERROR(20327, "发件人地址不能为空"),
    ORDER_PROTECT_UPTIMES_ERROR(20328, "上门开始时间不能为空"),
    ORDER_PROTECT_UPTIMEE_ERROR(20329, "上门结束时间不能为空"),
    ORDER_PROTECT_LCOMPANY_ERROR(20330, "快递公司不能为空"),
    ORDER_PROTECT_LNUM_ERROR(20331, "快递单号不能为空"),
    ORDER_PROTECT_RTYPE_ERROR(20332, "请选择寄件方式"),
    ORDER_PROTECT_DAY_ERROR(20333, "已超出退款限定时间"),
    ORDER_PROTECT_SIGN_ERROR(20334, "商品未签收，请签收后寄回"),
    ORDER_PROTECT_ONLINE_ERROR(20335, "线下订单不能在线退款"),
    ORDER_URGE_NUM_ERROR(20336, "已催商家发货！"),
    ORDER_STATUS_DEL_ERROR(20336, "该订单状态禁止删除！"),
    LOGISTICS_NUMBER_ERROR(20337, "快递单号不正确,请重新输入"),

    /**
     * 竞拍模块异常
     */
    AUCTION_APPLY_NOT_EXIST(20400, "竞拍订单不存在"),
    AUCTION_STATUS_ERROR(20401, "竞拍状态异常"),

    DEPOSIT_NOT_PAID(20403, "未缴纳保证金"),
    DEPOSIT_BALANCE_NOT_ENOUGH(20404, "保证金余额不足"),

    AUCTION_OFFER_AMOUNT_TOO_LOW(20405, "出价金额必须大于上一次出价金额"),

    AUCTION_STATUS_CHANGE_NOT_ALLOWED(20406, "当前状态不允许变更"),

    AUCTION_ORDER_NOT_EXIST(20407, "竞拍订单不存在"),

    AUCTION_ORDER_STATUS_NOT_ALLOW_DELETE(20408, "该订单状态不允许删除"),

    AUCTION_ORDER_STATUS_NOT_ALLOW_CONSIGN(20409, "只能寄售待处理的订单"),

    AUCTION_ORDER_USER_NOT_MATCH(20410, "竞拍订单用户不匹配"),

    AUCTION_ORDER_STATUS_NOT_ALLOW_CANCEL_CONSIGN(20411, "只能取消寄售中的订单"),

    AUCTION_ORDER_STATUS_NOT_ALLOW_ADJUST_PRICE(20412, "只能调整商城寄售中的订单价格"),

    AUCTION_GOODS_STATUS_ERROR(20412, "竞拍商品状态异常"),

    AUCTION_ORDER_STATUS_NOT_ALLOW_RECEIVE(20413, "只能在待处理状态下申请取回藏品"),

    AUCTION_ORDER_STATUS_NOT_ALLOW_DELIVERY(20414, "只能在待发货状态下进行平台发货"),

    AUCTION_ORDER_STATUS_NOT_ALLOW_CONFIRM(20415, "只能在待收货状态下确认收货"),

    AUCTION_SELECT_OFFER_NOT_ALLOW(20416, "只能在竞拍中状态下选择出价"),

    AUCTION_RECORD_TYPE_ERROR(20417, "参拍记录查询类型错误"),

    GOODS_NOT_EXIST(20418, "商品不存在"),
    AUCTION_RESUBMIT_TIME_NOT_REACHED(20419, "可重新拍卖时间未到达"),

    AUCTION_MIN_ESTIMATED_VALUE_NOT_NULL(20420, "竞拍最小估值不能为空"),

    AUCTION_INVALID_CONSIGNMENT_TYPE(20421, "无效的寄售类型"),

    AUCTION_MIN_VALUE_LESS_THAN_CONSIGNMENT_PRICE(20422, "最小估值不能小于寄售价格"),

    AUCTION_ORDER_STATUS_NOT_ALLOW_UPDATE(20423, "只能在门店寄售状态下修改状态"),

    AUCTION_ORDER_STATUS_NOT_ALLOW_PAY(20424, "订单状态不允许支付"),

    AUCTION_ORDER_STATUS_NOT_ALLOW_AUDIT(20425, "只能审核待审核状态的订单"),

    LOGISTICS_ORDER_FAILED(20426, "快递下单失败"),
    RECEIPT_CODE_FAILED(20427, "收货码不正确"),

    AUCTION_ORDER_PICKUP_CODE_ERROR(20427, "取件码错误"),

    AUCTION_ORDER_CONSIGN_PRICE_ERROR(20428, "寄售价格错误"),

    AUCTION_ORDER_LINE_CONSIGN_AMOUNT_NOT_ALLOW(20429, "商品价格不允许商城寄售"),

    AUCTION_NO_BID(20430, "当前竞拍无人出价，无法提前结束"),

    AUCTION_SELF_OFFER_NOT_ALLOWED(20431, "竞拍不能出价给自己"),

    // 变更用户等级失败
    CHANGE_USER_VIP_ERROR(20432, "变更用户等级失败"),

    ESS_FLAG_ERROR(20433, "合同不存在"),
    ORDER_IS_DELIVERY(20434, "该订单已发货,不能修改收货地址"),
    ADDRESS_IS_NULL(20435, "用户收货地址为空"),
    RESELL_ERROR(20436, "转售失败"),
    IS_RESELL_ERROR(20437, "转售失败,藏品已转售"),
    QUERY_MERCHANT_INFO_ERROR(20438, "获取卖家信息为空"),
    QUERY_MERCHANT_ADDRESS_IS_NULL(20438, "卖家收件地址为空"),
    MERCHANT_NOT_APPROVED(20439, "商家入驻审核失败,无法进行转售操作"),
    USER_AUTHENTICATION_ERROR(20440, "获取用户实名信息失败"),
    USER_NOT_AUTHENTICATION(20441, "用户未实名,请先进行实名认证"),
    NO_BUY_OWN_GOODS(20442, "不可购买自己的店铺的商品"),
    LOT_BUY_LOCK(20443, "下单失败，商品已被购买锁定"),

    DELAY_CONFIRM_ERROR(20444, "延时交易确认失败"),
    DELAY_REFUND_ERROR(20445, "延时交易退款失败"),
    DELAY_REFUND_QUERY_ERROR(20446, "延时交易退款查询失败"),
    // 创建延迟订单失败
    CREATE_DELAY_ORDER_ERROR(20447, "创建延迟订单失败"),

    ADD_AND_FREEZE_BALANCE_ERROR(20448, "添加冻结金额失败"),

    REDUCE_AND_FREEZE_BALANCE_ERROR(20451, "减少冻结金额失败"),

    SETTLE_TRANSACTION_ERROR(20452, "调用交易结算失败"),

    // 解决失败
    UNFREEZE_BALANCE_ERROR(20453, "解冻金额失败"),

    // 获取支付参数失败
    GET_PAY_PARAM_ERROR(20449, "获取支付参数失败"),
    // 支付回调失败
    PAY_CALLBACK_ERROR(20450, "支付回调失败"),

    UPDATE_COUPON_TEMPLATE_ERROR(20451, "更新优惠券模板失败"),

    UPDATE_COUPON_SCOPE_ERROR(20452, "更新优惠券适用范围失败"),

    ORDER_COMMENT_SAVE_FAILED(20467, "订单评价保存失败"),

    // 优惠券相关错误码
    COUPON_NOT_EXIST(20468, "优惠券不存在"),
    COUPON_ALREADY_USED(20469, "优惠券已使用或已过期"),
    COUPON_EXPIRED(20470, "优惠券已过期"),
    COUPON_PLATFORM_APP_LIMIT(20471, "该优惠券仅限APP平台使用"),
    COUPON_PLATFORM_MINI_PROGRAM_LIMIT(20472, "该优惠券仅限小程序平台使用"),
    COUPON_THRESHOLD_NOT_MET(20473, "优惠券未达满减门槛"),
    COUPON_THRESHOLD_LESS_THAN_ZERO(20478, "优惠券门槛金额不能小于0"),
    COUPON_AMOUNT_TOO_LARGE(20481, "优惠券面值不能超过99999999"),

    // 优惠券过期处理相关错误码
    COUPON_EXPIRATION_UPDATE_FAILED(20474, "优惠券过期状态更新失败"),
    COUPON_TEMPLATE_HAS_UNUSED(20475, "该优惠券模板下存在未使用的优惠券，无法编辑"),
    COUPON_NOT_MEET_MEMBER_THRESHOLD(20476, "优惠券未达会员使用门槛"),
    COUPON_NOT_SUPPORTED_BY_STORE(20477, "优惠券不支持该店铺"),
    GOODS_AMOUNT_GREATER_THAN_COUPON(20478, "由于此优惠券大于此藏品的销售价，暂不支持使用，请重新选择优惠券！"),
    UPDATE_AMOUNT_GREATER_THAN_COUPON(20479, "由于支付最低支付金额为0.1元，请重新修改价格！"),
    COUPON_NOT_BELONG_TO_CURRENT_BUYER(20480, "该优惠券不属于当前购买人！"),

    ;

    private Integer code;
    private String msg;

    OrderBizErrorCodeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getMsg() {
        return msg;
    }

    public int getCode() {
        return code;
    }
}
