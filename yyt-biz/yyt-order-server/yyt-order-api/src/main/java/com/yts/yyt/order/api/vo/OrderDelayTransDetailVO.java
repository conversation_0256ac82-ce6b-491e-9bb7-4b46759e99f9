package com.yts.yyt.order.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;

/**
 * 延迟交易详情VO
 */
@Data
@Schema(description = "延迟交易详情VO")
public class OrderDelayTransDetailVO {

    @Schema(description = "汇付商户号")
    private String huifuId;

    @Schema(description = "延迟付款服务费")
    private BigDecimal fee;

    @Schema(description = "店铺类型 1商家 2个人 3 自营")
    private String shopType;
} 