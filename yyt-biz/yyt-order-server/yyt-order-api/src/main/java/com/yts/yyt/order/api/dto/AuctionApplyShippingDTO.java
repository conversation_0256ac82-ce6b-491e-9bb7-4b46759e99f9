package com.yts.yyt.order.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "竞拍寄货")
public class AuctionApplyShippingDTO implements Serializable {

	@Schema(description = "竞拍下单 可多个")
	private List<Long> applyIds;

	/**
	 * 寄件方式(0快递上门 1自行寄出 2送货上门)
	 */
	@Schema(description = "寄件方式(0快递上门 1自行寄出 2送货上门)")
	private Integer logisticsType;

	/**
	 * 寄件单号
	 */
	@Schema(description = "寄件单号")
	private String logisticsNumber;

	/**
	 * 寄件公司
	 */
	@Schema(description = "寄件公司")
	private String logisticsCompany;

	/**
	 * 寄件公司编码
	 */
	@Schema(description = "寄件公司编码")
	private String logisticsCompanyCode;

	/**
	 * 取件地址薄id
	 */
	@Schema(description = "取件地址薄id")
	private Long addressId;

	@Schema(description = "预约开始时间")
	private LocalDateTime reserveBeginTime;

	@Schema(description = "预约结束时间")
	private LocalDateTime reserveEndTime;

	/**
	 * 货物信息
	 */
	@Schema(description = "货物信息")
	private String cargo;

	/**
	 * 货物重量
	 */
	@Schema(description = "货物重量")
	private BigDecimal weight;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 时效类型
	 */
	@Schema(description = "时效类型,今天|明天|后天")
	private String dayType;

}
