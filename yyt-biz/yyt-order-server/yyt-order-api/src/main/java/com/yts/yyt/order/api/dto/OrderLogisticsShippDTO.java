package com.yts.yyt.order.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class OrderLogisticsShippDTO implements Serializable {
    /**
     * orderId
     */
    @Schema(description="orderId")
    private Long orderId;

    /**
     * 运单号
     */
    @Schema(description="运单号")
    private String logisticsNumber;

    /**
     * 快递公司
     */
    @Schema(description="快递公司")
    private String logisticsCompany;

    /**
     * 快递公司编码
     * CompanyConstant.SF
     * @see com.kuaidi100.sdk.contant.CompanyConstant
     */
    @Schema(description="快递公司编码")
    private String logisticsCompanyCode;

    /**
     * 发货方式
     * @see com.yts.yyt.order.api.enums.OrderLogisticsEnum
     */
    @Schema(description="发货方式 0快递上门 1自行寄出(快递单号) 2送货上门(自提)")
    private Integer type;

    /**
     * 预约开始时间 快递上门/送货上门 填写
     */
    @Schema(description="预约开始时间 快递上门/送货上门 填写")
    private LocalDateTime reserveBeginTime;

    /**
     * 预约结束时间 快递上门/送货上门 填写
     */
    @Schema(description="预约结束时间 快递上门/送货上门 填写")
    private LocalDateTime reserveEndTime;

    /**
     * 地址ID
     */
    @Schema(description="地址ID")
    private Long addressId;

    /**
     * 商品名称或其他摘要
     */
    @Schema(description="商品名称或其他摘要")
    private String cargo;

    /**
     * 重量
     */
    @Schema(description="重量")
    private String weight;

    /**
     * 备注
     */
    @Schema(description="备注")
    private String remarks;

    /**
     * LRecManName
     */
    private String recManName;

    /**
     * recManPhone
     */
    private String recManPhone;

    /**
     * recManPrintAddr
     */
    private String recManPrintAddr;
}
