package com.yts.yyt.order.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 竞价申请表
 *
 * <AUTHOR>
 * @date 2025-02-13 11:56:45
 */
@Data
@TableName("auction_receipt_code")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "竞拍申请表")
public class AuctionReceiptCodeEntity extends Model<AuctionReceiptCodeEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键")
    private Long id;
	/**
	 * 收货编码
	 */
	@Schema(description="收货编码")
	private String receiptCode;

	/**
	 * 竞拍申请id
	 */
	@Schema(description="竞拍申请id")
	private Long auctionApplyId;

	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	@Schema(description="创建时间")
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	@Schema(description="更新时间")
	private LocalDateTime updateTime;
}
