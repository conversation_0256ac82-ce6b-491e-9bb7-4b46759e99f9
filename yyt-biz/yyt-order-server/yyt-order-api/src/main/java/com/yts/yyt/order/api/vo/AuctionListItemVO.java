package com.yts.yyt.order.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(description = "竞拍列表项")
public class AuctionListItemVO {
    
    /**
     * 竞拍ID
     */
    private Long id;
    
    /**
     * 商品图片
     */
    private String applyImg;

    /**
     * 商品缩略图
     */
    private String previewImage;

    @Schema(description = "讲解视频")
    private String explanatoryVideo;
    /**
     * 申请名称
     */
    private String applyName;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 归属人头像
     */
    private String ownerHeadImg;

    /**
     * 归属人昵称
     */
    private String ownerNickname;

    /**
     * 最小估价
     */
    private BigDecimal minAuctionPrice;
    
    /**
     * 最大估价
     */
    private BigDecimal maxAuctionPrice;
    
    /**
     * 竞拍状态
     */
    private String auctionStatus;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
} 
