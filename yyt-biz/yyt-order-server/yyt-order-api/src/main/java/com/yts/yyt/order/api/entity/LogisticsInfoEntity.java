package com.yts.yyt.order.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 物流信息
 *
 * <AUTHOR>
 * @date 2025-01-08 15:18:35
 */
@Data
@TableName("logistics_info")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "物流信息")
public class LogisticsInfoEntity extends Model<LogisticsInfoEntity> {

 
	/**
	* id
	*/
    @TableId(type = IdType.INPUT)
    @Schema(description="物流id")
    private String id;

	/**
	* 订单编号
	*/
    @Schema(description="订单编号")
    private String orderNo;

	/**
	* 物流信息
	*/
    @Schema(description="物流信息")
    private String info;

	/**
	* 1-终态
	*/
    @Schema(description="1-终态")
    private Integer state;

	/**
	 * 1-订阅成功
	 */
	@Schema(description="1-订阅成功")
	private Integer subscribe;

	/**
	* 上次更新时间
	*/
    @Schema(description="上次更新时间")
    private LocalDateTime lastUpdate;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;
}