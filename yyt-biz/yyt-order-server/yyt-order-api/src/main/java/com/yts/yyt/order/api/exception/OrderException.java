package com.yts.yyt.order.api.exception;

import com.yts.yyt.common.core.exception.GlobalBizException;

public class OrderException extends GlobalBizException {


    public OrderException() {
        super();
    }

    public OrderException(String msg) {
        super(msg);

    }

    public OrderException(String msg, Throwable e) {
        super(msg, e);
    }

    public OrderException(String msg, int code) {
        super(msg, code);

    }

    public OrderException(OrderBizErrorCodeEnum codeEnum) {
        super(codeEnum.getMsg(), codeEnum.getCode());
    }

    public OrderException(Integer code, String msg) {
        super(msg, code);

    }

    public OrderException(String msg, int code, Throwable e) {
        super(msg, code, e);
    }

    public static OrderException build(OrderBizErrorCodeEnum codeEnum) {
        return new OrderException(codeEnum.getCode(), codeEnum.getMsg());
    }

    public static void isTrue(boolean condition, OrderBizErrorCodeEnum codeEnum){
        if(condition){
            throw build(codeEnum);
        }
    }

}
