/*
 *
 *      Copyright (c) 2018-2025, yyt All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: yyt
 *
 */

package com.yts.yyt.order.api.feign;

import com.yts.yyt.common.core.constant.ServiceNameConstants;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.feign.annotation.NoToken;
import com.yts.yyt.order.api.dto.OrderOfflinePayAuditDTO;
import com.yts.yyt.order.api.dto.OrderOfflinePayDTO;
import com.yts.yyt.order.api.dto.OrderPayBackDTO;
import com.yts.yyt.order.api.vo.OrderPayParameterVO;
import com.yts.yyt.order.api.vo.OrderYoPayVO;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2018/6/22
 */
@FeignClient(contextId = "remoteOrderPayService", value = ServiceNameConstants.ORDER_SERVER)
public interface RemoteOrderPayService {

	/**
	 * 支付回调
	 * @param orderPayBackDTO
	 * @return
	 */
	@PostMapping("/pay/payback")
	@NoToken
	R payback(@RequestBody OrderPayBackDTO orderPayBackDTO);


	/**
	 * 订单支付参数
	 * @param orderId
	 * @return
	 */
	@GetMapping("/pay/payParams")
	@NoToken
	R<OrderPayParameterVO> getOrderPayParams(@RequestParam("orderId") Long orderId);

//	/**
//	 * 线下支付
//	 * @param orderOfflinePayDTO
//	 * @return
//	 */
//	@PostMapping("/pay/offlinePay")
//	@NoToken
//	public R offlinePay(@RequestBody OrderOfflinePayDTO orderOfflinePayDTO);

//	/**
//	 * 线下支付审核
//	 * @param orderOfflinePayAuditDTO
//	 * @return
//	 */
//	@PostMapping("/pay/auditOfflinePayOrder")
//	@NoToken
//	public R auditOfflinePayOrder(@RequestBody OrderOfflinePayAuditDTO orderOfflinePayAuditDTO);
}
