package com.yts.yyt.order.api.feign;

import com.yts.yyt.common.core.constant.ServiceNameConstants;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.feign.annotation.NoToken;
import com.yts.yyt.order.api.dto.OrderOfflinePayDTO;
import com.yts.yyt.order.api.dto.OrderPayBackDTO;
import com.yts.yyt.order.api.vo.AuctionOrderPayVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@FeignClient(contextId = "remoteAuctionOrderService", value = ServiceNameConstants.ORDER_SERVER)
public interface RemoteAuctionOrderService {

    /**
     * 获取支付参数
     *
     * @param id 订单ID
     * @return 支付参数
     */
    @NoToken
    @GetMapping("/auctionOrder/payParams/{id}")
    R<AuctionOrderPayVO> getPayParams(@PathVariable("id") Long id);

    /**
     * 支付回调
     *
     * @param payBackDTO 支付回调信息
     * @return 处理结果
     */
    @NoToken
    @PostMapping("/auctionOrder/payBack")
    R<Boolean> payBack(@RequestBody OrderPayBackDTO payBackDTO);

    /**
     * 更新线下支付信息
     *
     * @param offlinePayDTO 线下支付信息
     * @return 更新结果
     */
    @NoToken
    @PostMapping("/auctionOrder/updateOfflinePay")
    R<Boolean> updateOfflinePay(@RequestBody OrderOfflinePayDTO offlinePayDTO);

    /**
     * 查询用户参拍总数
     *
     * @param userId 用户ID
     * @return 参拍总数
     */
//    @NoToken
//    @GetMapping("/auctionOrder/countUserAuctions")
//    R<Long> countUserAuctions(@RequestParam("userId") Long userId);
    /**
     * 查询用户竞拍成功记录总数
     *
     * @param userId 用户ID
     * @return 参拍总数
     */
    @NoToken
    @GetMapping("/auctionOrder/countUserAuctionSuccess")
    R<Long> countUserAuctionSuccess(@RequestParam("userId") Long userId);

}