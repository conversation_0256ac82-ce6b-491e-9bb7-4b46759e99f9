package com.yts.yyt.order.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单延迟交易确认表
 *
 * <AUTHOR>
 * @date 2024-02-13 12:09:20
 */
@Data
@TableName("order_delaytrans")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "订单延迟交易确认表")
@Accessors(chain = true)
public class OrderDelayTransEntity extends Model<OrderDelayTransEntity> {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键")
    private Long id;

    /**
     * 订单ID
     */
    @Schema(description="订单ID")
    private Long orderId;

    /**
     * 用户ID
     */
    @Schema(description="用户ID")
    private Long userId;

    /**
     * 状态
     * @see com.yts.yyt.order.api.enums.OrderEnum.OrderDelayStatus
     */
    @Schema(description="状态")
    private Integer status;

    @Schema(description="支付流水号")
    private String paySerio;

    @Schema(description="原交易请求流水号")
    private String orgReqSeqId;

    @Schema(description="原交易请求日期")
    private String orgReqDate;

    @Schema(description="交易流水号")
    private String reqSeqId;

    @Schema(description="交易总金额")
    private BigDecimal amount;

    @Schema(description="分帐总金额")
    private BigDecimal delayAmount;

    /**
     * 发起时间
     */
    @Schema(description="发起时间")
    private LocalDateTime delaytransTime;

    /**
     * 成交时间（交易时间）
     */
    @Schema(description="成交时间")
    private LocalDateTime transactionTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    @TableLogic
    @Schema(description="是否删除")
    @TableField(fill = FieldFill.INSERT)
    private String delFlag;
} 