package com.yts.yyt.order.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 藏品想要记录表
 *
 * <AUTHOR>
 * @date 2025-02-14 15:41:18
 */
@Data
@TableName("collection_appraisal_want_record")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "藏品想要记录表")
public class CollectionAppraisalWantRecordEntity extends Model<CollectionAppraisalWantRecordEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键")
    private Long id;

	/**
	* 鉴定单号
	*/
    @Schema(description="鉴定单号")
    private String collectionNo;

	/**
	* 想要用户
	*/
    @Schema(description="想要用户")
    private String wantUser;

	/**
	* 创建人id
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人id")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新人id
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人id")
    private String updateBy;

	/**
	* 修改时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="是否删除")
    private Integer delFlag;
}
