package com.yts.yyt.order.api.vo;


import com.yts.yyt.order.api.entity.LogisticsCompanyEntity;
import com.yts.yyt.order.api.entity.OrderLogisticsEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "订单详情")
public class OrderDetailSensitiveVO extends PageOrderVO{

    @Schema(description = "物流信息")
    
    private OrderLogisticsSensitiveVO logistics;

    @Schema(description = "物流公司")
    private LogisticsCompanyEntity logisticsCompany;

	/**
	 * 退款转账凭证-列表类型
	 */
	@Schema(description = "退款转账凭证")
	private List<String> refundVoucherImgList;
    

}
