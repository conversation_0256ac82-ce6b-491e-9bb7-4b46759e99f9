package com.yts.yyt.order.api.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yts.yyt.common.core.jackson.BigDecimal2StringSerializer;
import com.yts.yyt.order.api.entity.OrderLogisticsEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 收货订单VO
 */
@Data
public class AdminOrderLogisticsVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  暂定写死
     */
    public final static String DELIVERY_TYPE = "快递";
    public final static Integer SELLNUMBER = 1;

    private Long id;

    private Long userId;

    private Long goodsId;

    @Schema(description = "物流id")
    private Long logisticsId;

    @JsonSerialize(using = BigDecimal2StringSerializer.class)
    @Schema(description = "金额")
    private BigDecimal amount;

    @Schema(description = "商品名称")
    private String goodsName;

    @Schema(description = "商品主图")
    private String mainImage;

    @Schema(description = "商品预览图")
    private String previewImage;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "收货地址")
    private String address;


    @Schema(description = "订单编号")
    private String orderNo;

    @Schema(description = "订单类型 ")
    private String orderType;

    @Schema(description = "申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String createTime;

    @Schema(description = "数量")
    private Integer sellNumber = SELLNUMBER;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "支付类型 1-小程序 2-微信 3-支付宝 4-三方钱包")
    private Integer payType;

    @Schema(description = "用户名")
    private String userName;

    @Schema(description = "物流信息")
    private List<OrderLogisticsEntity> logistics;


}
