package com.yts.yyt.order.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

public interface OrderEnum {


    /**
     * 订单类型
     */
    @AllArgsConstructor
    @Getter
    public static enum OrderType{
        FIRST_LAUNCH("first_launch", "首发"),
        CONSIGNMENT("consignment", "寄售"),
        BUY_BACK("buy_back", "回购"),
        STORE("store", "店铺"),

        ;

        private final String type;

        private final String desc;

        public static boolean contains(String type) {
            for (OrderType status : OrderType.values()) {
                if (status.getType().equals(type)) {
                    return true;
                }
            }
            return false;
        }
    }


    /**
     * 订单状态
     */
    @AllArgsConstructor
    @Getter
    public static enum OrderStatus{

        /**
         *  寄售订单状态
		 *  寄售状态枚举：consignment_order_status_enum
         */
        CONSIGNMENT_WAIT_PLATFORM_RECEIVE("consignment_platform_receive","待验收"),//待(平台)收货
        CONSIGNMENT_ON_SALE("consignment_selling","寄售中"),
        CONSIGNMENT_SOLD_OUT("consignment_sold","已卖出"),
        CONSIGNMENT_WAIT_PLATFORM_SEND("consignmentr_platform_send","待发货"),//待(平台)寄回
        CONSIGNMENT_WAIT_USER_RECEIVE("consignment_user_receive","待收货"),//待(用户)收货
        CONSIGNMENT_CANCEL("consignment_cancel","已取消"),

		/**
		 * 回购订单
		 * 回购状态字典：buy_back_order_status_enum
		 */
        BUY_BACK_TO_BE_ACCEPTED("buy_back_to_be_accepted", "待验收"),
        BUY_BACK_PLATFORM_TO_BE_SHIPPED("buy_back_platform_to_be_shipped", "待发货"),//待发货(平台)
        BUY_BACK_USER_TO_BE_RECEIVED("buy_back_user_to_be_received", "待收货"),    //待收货(卖家)
        BUY_BACK_PROGRESSING("buy_back_progressing", "回购中"),
        BUY_BACK_END("buy_back_end", "已回购"),
        BUY_BACK_CANCEL("buy_back_cancel", "已取消"),

		/**
		 * 首发订单/正常订单状态流转
		 */
		FIRST_LAUNCH_WAITING_PAYMENT("first_launch_waiting_payment", "待付款"),
		FIRST_LAUNCH_WAITING_DELIVERY("first_launch_waiting_delivery", "待发货"),
		FIRST_LAUNCH_SHIPPED("first_launch_shipped", "待收货"),		//已发货
		FIRST_LAUNCH_RECEIVED("first_launch_received", "已收货"),
		FIRST_LAUNCH_COMPLETED("first_launch_completed", "交易完成"),
		/**
		 * 已过了3天无理由退货时间
		 */
		SALE_AFTER_COMPLETED_STATUS("saleAfterCompletedStatus", "售后完成"),
		FIRST_LAUNCH_CLOSED("first_launch_closed", "已关闭"),
		FIRST_LAUNCH_REFUNDING("first_launch_refunding", "退款中"),
		FIRST_LAUNCH_PENDING_AUDIT("first_launch_pending_audit", "线下支付-待审核"),

		;

        private final String type;

        private final String desc;
        
        // 根据 type 获取 desc
        public static String getDescByType(String type) {
            for (OrderStatus status : OrderStatus.values()) {
                if (status.getType().equals(type)) {
                    return status.getDesc();
                }
            }
            return null; // 如果没有匹配的 type，返回 null 或者一个默认值
        }
    }

    /**
     * 订单关闭子状态
     */
    @AllArgsConstructor
    @Getter
    public static enum OrderClosedSubStatus{

        CLOSED_UNPAID("closed_unpaid", "已关闭（未支付）"),
        CLOSED_PAID_NOT_SHIPPED("closed_paid_not_shipped", "已关闭（已支付-未发货）"),
        CLOSED_PAID_RETURNED("closed_paid_returned", "已关闭（已支付-退货）"),
        
        ;
        // 根据 type 获取 desc
        public static String getDescByType(String type) {
            for (OrderClosedSubStatus status : OrderClosedSubStatus.values()) {
                if (status.getType().equals(type)) {
                    return status.getDesc();
                }
            }
            return null; // 如果没有匹配的 type，返回 null 或者一个默认值
        }
        private final String type;

        private final String desc;
    }

	/**
	 * 订单延迟交易确认状态
	 */
	@AllArgsConstructor
	@Getter
	public static enum OrderDelayStatus{

		/**
		 * 0-待交易确认 1-付款中 2-付款成功 3-退款中 4-退款成功
		 */
		DELAY_WAIT_PAYMENT(0, "待交易确认"),
		DELAY_PAYING(1, "付款中"),
		DELAY_PAY_SUCCESS(2, "付款成功"),
		DELAY_REFUNDING(3, "退款中"),
		DELAY_REFUND_SUCCESS(4, "退款成功"),

		;

		private final Integer statue;

		private final String desc;

	}

    /**
     * 订单来源
     */
    @AllArgsConstructor
    @Getter
    public static enum OrderSource{
        APPLET(1, "小程序"),
        H5(2, "ANDROID"),
        PC(3, "IOS"),
        ;

        private final int type;

        private final String desc;

        public static boolean contains(int type) {
            for (OrderSource status : OrderSource.values()) {
                if (status.getType() == type) {
                    return true;
                }
            }
            return false;
        }
		// 根据 type 获取 desc
		public static String getDescByType(int type) {
			for (OrderSource status : OrderSource.values()) {
				if (status.getType() == type) {
					return status.getDesc();
				}
			}
			return "--";
		}

    }

    @AllArgsConstructor
    @Getter
    public static enum OrderPayType{
        MPWECHAT_PAY(1, "微信小程序"),
		WECHAT_PAY(2, "微信"),
		ALI_PAY(3, "支付宝"),
        PAYMENT_COMPANY_PAY(4, "三方支付"),
		OFFLINE_PAY(5, "线下支付"),
        ;

        private final int type;

        private final String desc;

        public static boolean contains(int type) {
            for (OrderPayType status : OrderPayType.values()) {
                if (status.getType()==type) {
                    return true;
                }
            }
            return false;
        }
		// 根据 type 获取 desc
		public static String getDescByType(int type) {
			for (OrderPayType status : OrderPayType.values()) {
				if (status.getType() == type) {
					return status.getDesc();
				}
			}
			return "--";
		}
    }

	@AllArgsConstructor
	@Getter
	public static enum OrderPayStatus{
		WAIT_PAY(0, "未支付"),
		SUCCESS(1, "支付成功"),
		FAILED(2, "支付失败"),
		;

		private final int type;

		private final String desc;

	}

	@AllArgsConstructor
	@Getter
	public static enum OrderProtectionType{
		ORDER_REFUND(1, "订单退款"),
		AFTER_SALES_REFUND(2, "售后退款"),
		;

		private final int type;

		private final String desc;

	}

	@AllArgsConstructor
	@Getter
	public static enum OrderProtectStatus{

		REFUND_IN_PROGRESS(1,"退款中"),
		REFUND_REJECTED(2,"退款拒绝"),
		REFUND_SUCCESS(3,"退款成功"),
		REFUND_CANCELLED(4,"取消退款"),
		;
		private final int type;

		private final String desc;
        // 根据 type 获取 desc
        public static String getDescByType(int type) {
            for (OrderProtectStatus status : OrderProtectStatus.values()) {
                if (status.getType() == type) {
                    return status.getDesc();
                }
            }
            return "--"; 
        }

	}

	@AllArgsConstructor
	@Getter
	public static enum ProtectionStatus {
		APPLY_REFUND(1,"申请退款"),
		REFUND_APPROVED(2,"退款通过"),
		REFUND_SUCCESS(3,"退款成功"),

		BUYER_TO_RETURN(4,"买家待退货"),

		SELLER_TO_RECEIVE(5,"卖家待收货"),

		SELLER_RECEIVED(6,"卖家已收货"),

		SELLER_REJECTED(7,"卖家拒绝"),

		PARTIAL_REFUND(8,"部分退款"),

		PROTECT_CANCEL(9,"已取消"),

		REFUND_IN_PROGRESS(10,"退款中"),

		;
		private final int type;

		private final String desc;
        

	}
	@AllArgsConstructor
	@Getter
	public static enum RefundType{
		ONLY_REFUND(1,"仅退款"),
		RETURN_AND_REFUND(2,"退货退款"),
		;
		private final int type;

		private final String desc;

	}

	@AllArgsConstructor
	@Getter
	public static enum RefundSendType{
		PICKUP_DOOR("1","上门取件"),
		OWN_SEND("2","自行寄出"),
		;
		private final String type;

		private final String desc;

	}

	@AllArgsConstructor
	@Getter
	public static enum RefundReasonType{
		REASON_TYPE1("1","不想要了（7天无理由）"),
		REASON_TYPE2("2","拍错藏品了"),
		REASON_TYPE3("3","地址/手机信息有误"),
		REASON_TYPE4("4","未收到货物"),
		REASON_TYPE5("5","其他原因"),
		;
		private final String type;

		private final String desc;

	}

	@AllArgsConstructor
	@Getter
	public static enum RefundLogisticsStatus{
		LOGISTICS_STATUS1("1","未收到货"),
		LOGISTICS_STATUS2("2","已收到货"),
		;
		private final String type;

		private final String desc;

	}

    @AllArgsConstructor
    @Getter
    public static enum ResellFlag{
        NO_RESELL("0","未转售"),
        IS_RESELL("1","已转售"),
        ;
        private final String type;

        private final String desc;

    }

	@AllArgsConstructor
	@Getter
	public static enum OrderCompleted{
		UNCOMPLETED(0,"未完成"),
		COMPLETED(1,"已完成"),
		;
		private final Integer status;

		private final String desc;

	}
}


