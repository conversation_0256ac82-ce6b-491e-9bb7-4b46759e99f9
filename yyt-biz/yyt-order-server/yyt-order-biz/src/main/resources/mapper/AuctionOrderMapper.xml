<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yts.yyt.order.mapper.AuctionOrderMapper">

    <select id="queryMyAuctionOrders" resultType="com.yts.yyt.order.api.vo.MyAuctionOrderVO">
        SELECT
            ao.id as orderId,
            gi.preview_image as previewImage,
            gi.name as goodsName,
            gi.place_of_origin as placeOfOrigin,
            ao.offer_amount as offerAmount,
            ao.status
        FROM
            auction_order ao
            LEFT JOIN goods_info gi ON ao.goods_id = gi.id
        WHERE
            ao.deleted = 0 AND
            ao.user_id = #{userId}
            AND ao.status IN
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
            <if test="startTime != null">
                AND ao.create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND ao.create_time &lt;= #{endTime}
            </if>
        ORDER BY
            ao.create_time DESC
    </select>

    <select id="queryAuctionRecords" resultType="com.yts.yyt.order.api.vo.AuctionRecordVO">
        SELECT
            ao.id ,
            ao.id as orderId,
            aa.id as auctionApplyId,
            aa.sold_price as wonAmount,
            aa.auction_end_time as endTime,
            gi.preview_image as previewImage,
            gi.name as goodsName,
            ao.offer_amount as offerAmount,
            ao.status,
            ao.create_time as offerTime
        FROM
            auction_order ao
            LEFT JOIN goods_info gi ON ao.goods_id = gi.id
            LEFT JOIN auction_apply aa ON ao.auction_apply_id = aa.id

        ${ew.customSqlSegment}
    </select>

    <select id="queryAdminPage" resultType="com.yts.yyt.order.api.vo.AuctionOrderPageVO">
        SELECT
            ao.id as orderId,
            ao.auction_apply_id auctionApplyId,
            ao.order_no as orderNo,
            ao.create_time as createTime,
            ao.order_source as orderSource,
            gi.name as goodsName,
            gi.goods_no as goodsNo,
            gi.preview_image as previewImage,
            ao.offer_amount as offerAmount,
            ao.status,
            ao.remark,
            ui.username,ui.head_img userHeadImg,ui.mobile userMobile
        FROM
            auction_order ao
                INNER JOIN goods_info gi ON ao.goods_id = gi.id
                INNER JOIN user_info ui on ao.user_id = ui.id
        ${ew.customSqlSegment}
    </select>

    <select id="getOrderPayParams" resultType="com.yts.yyt.order.api.vo.OrderPayParameterVO">
        SELECT a.order_no orderNo,b.goods_no goodsNo,b.`name` goodsName,a.offer_amount amount,c.`name`,a.goods_id collectionId,b.type,a.user_id userId
        FROM auction_order a
                 INNER JOIN goods_info b on a.goods_id = b.id
                 LEFT JOIN goods_category c on b.goods_category_id = c.id
        where a.id = #{id}
    </select>

    <select id="queryOfferRecords" resultType="com.yts.yyt.order.api.vo.AuctionOfferRecordVO">
        SELECT
            ao.user_id as userId,
            ui.username,
            ui.head_img as headImg,
            ao.offer_amount as offerAmount,
            ao.create_time as offerTime
        FROM
            auction_order ao
            LEFT JOIN user_info ui ON ao.user_id = ui.id
            ${ew.customSqlSegment}
    </select>

    <select id="detail" resultType="com.yts.yyt.order.api.vo.AuctionOrderDetailVO" >
        SELECT ao.id orderId,ao.user_id userId,ao.goods_id goodsId,ao.auction_apply_id auctionApplyId,
               ao.`status`,ao.order_no orderNo,ao.create_time createTime,ao.pay_serio paySerio,ao.pay_time payTime,
               ao.pay_type payType,ao.won_time wonTime,ao.offer_amount offerAmount,ao.deposit_amount depositAmount,
               ao.expires_time expiresTime,ao.order_source orderSource,ao.pickup_code pickupCode,ao.pay_images payImages,
               ao.consign_amount consignAmount,ao.consign_fee consignFee,ao.consign_expires_time consignExpiresTime,ao.remark,
               ao.update_time updateTime,
               gi.`name` goodsName,gt.`name` goodsTypeName,gi.preview_image goodPreImage,
               ap.sold_price wonAmount,
               ao.logistics_number
        FROM auction_order ao
                 INNER JOIN auction_apply ap on ao.auction_apply_id = ap.id
                 INNER JOIN goods_info gi on ao.goods_id = gi.id
                 LEFT JOIN goods_type gt on gi.goods_type_id = gt.id
        where ao.id  = #{id}
    </select>

</mapper>
