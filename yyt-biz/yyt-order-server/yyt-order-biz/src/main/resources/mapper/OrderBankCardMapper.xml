<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yts.yyt.order.mapper.OrderBankCardMapper">

  <resultMap id="orderBankCardMap" type="com.yts.yyt.order.api.entity.OrderBankCardEntity">
        <id property="id" column="id"/>
        <result property="orderId" column="order_id"/>
        <result property="userId" column="user_id"/>
        <result property="bankCardId" column="bank_card_id"/>
        <result property="accountName" column="account_name"/>
        <result property="bankName" column="bank_name"/>
        <result property="bankNo" column="bank_no"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
  </resultMap>
</mapper>