<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yts.yyt.order.mapper.CouponPayMapper">

    <resultMap type="com.yts.yyt.order.api.entity.CouponPayEntity" id="CouponPayMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="orderId" column="order_id" jdbcType="INTEGER"/>
        <result property="amt" column="amt" jdbcType="VARCHAR"/>
        <result property="huifuId" column="huifu_id" jdbcType="VARCHAR"/>
        <result property="payStatus" column="pay_status" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="VARCHAR"/>
    </resultMap>
    
    <select id="getUnpaidAndFailedCouponPaymentsByUserId" resultType="com.yts.yyt.order.api.vo.CouponPayVO">
        SELECT
            ROUND( sum( amt ), 2 ) AS amt,
            huifu_id,
            GROUP_CONCAT(id ORDER BY id SEPARATOR ',') AS ids
        FROM
            coupon_pay
        WHERE 
            del_flag = 0
            and pay_status in (0,2)
            and user_id = #{userId}
        GROUP BY
            user_id,
            huifu_id
    </select>

</mapper>

