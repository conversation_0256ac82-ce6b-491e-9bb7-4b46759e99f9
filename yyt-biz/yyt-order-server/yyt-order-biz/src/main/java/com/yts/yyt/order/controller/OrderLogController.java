package com.yts.yyt.order.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.log.annotation.SysLog;
import com.yts.yyt.order.api.entity.OrderLogEntity;
import com.yts.yyt.order.service.OrderLogService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.yts.yyt.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 订单操作日志
 *
 * <AUTHOR>
 * @date 2025-01-06 19:28:37
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/orderLog" )
@Tag(description = "orderLog" , name = "订单操作日志管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class OrderLogController {

    private final  OrderLogService orderLogService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param orderLog 订单操作日志
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('order_orderLog_view')" )
    public R getOrderLogPage(@ParameterObject Page page, @ParameterObject OrderLogEntity orderLog) {
        LambdaQueryWrapper<OrderLogEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(orderLogService.page(page, wrapper));
    }


    /**
     * 通过id查询订单操作日志
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('order_orderLog_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(orderLogService.getById(id));
    }

    /**
     * 新增订单操作日志
     * @param orderLog 订单操作日志
     * @return R
     */
    @Operation(summary = "新增订单操作日志" , description = "新增订单操作日志" )
    @SysLog("新增订单操作日志" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('order_orderLog_add')" )
    public R save(@RequestBody OrderLogEntity orderLog) {
        return R.ok(orderLogService.save(orderLog));
    }

    /**
     * 修改订单操作日志
     * @param orderLog 订单操作日志
     * @return R
     */
    @Operation(summary = "修改订单操作日志" , description = "修改订单操作日志" )
    @SysLog("修改订单操作日志" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('order_orderLog_edit')" )
    public R updateById(@RequestBody OrderLogEntity orderLog) {
        return R.ok(orderLogService.updateById(orderLog));
    }

    /**
     * 通过id删除订单操作日志
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除订单操作日志" , description = "通过id删除订单操作日志" )
    @SysLog("通过id删除订单操作日志" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('order_orderLog_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(orderLogService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param orderLog 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('order_orderLog_export')" )
    public List<OrderLogEntity> export(OrderLogEntity orderLog,Long[] ids) {
        return orderLogService.list(Wrappers.lambdaQuery(orderLog).in(ArrayUtil.isNotEmpty(ids), OrderLogEntity::getId, ids));
    }
}