package com.yts.yyt.order.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yts.yyt.order.api.dto.CollectionAppraisalInfoDto;
import com.yts.yyt.order.api.entity.CollectionAppraisalInfoEntity;
import com.yts.yyt.order.api.vo.CollectionAppraisalInfoVo;

public interface CollectionAppraisalInfoService extends IService<CollectionAppraisalInfoEntity> {

    /**
     * 新增藏品鉴定信息
     * @param collectionAppraisalInfo
     * @return
     */
    CollectionAppraisalInfoEntity generateCollectionAppraisal(CollectionAppraisalInfoEntity collectionAppraisalInfo);

    /**
     * 分页获取藏品鉴定信息
     * @param collectionAppraisalInfoDto
     * @return
     */
    Page<CollectionAppraisalInfoVo> getCollectionAppraisalInfoPage(CollectionAppraisalInfoDto collectionAppraisalInfoDto);

    /**
     * 修改鉴定状态，鉴定估价
     * @param dto
     */
    void updateAppraisalState(CollectionAppraisalInfoDto dto);

    /**
     * 更新想要人次
     * @param type
     * @param collectionNo
     */
    void updateWantCount(String type, String collectionNo);

    CollectionAppraisalInfoVo getCollectionAppraisalInfoById(Long id);
}
