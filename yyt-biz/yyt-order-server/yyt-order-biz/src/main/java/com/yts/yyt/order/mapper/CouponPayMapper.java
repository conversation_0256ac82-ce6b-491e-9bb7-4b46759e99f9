package com.yts.yyt.order.mapper;

import com.yts.yyt.common.data.datascope.YytBaseMapper;
import com.yts.yyt.order.api.entity.CouponPayEntity;
import com.yts.yyt.order.api.vo.CouponPayVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface CouponPayMapper extends YytBaseMapper<CouponPayEntity> {

    /**
     * 根据商户用户ID获取优惠券支付信息
     * 未支付和支付失败的状态
     * @param userId
     * @return
     */
    CouponPayVO getUnpaidAndFailedCouponPaymentsByUserId(@Param("userId") Long userId);
}

