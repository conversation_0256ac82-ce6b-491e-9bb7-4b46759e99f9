package com.yts.yyt.order.mq.consumer;

import com.yts.yyt.common.mq.idempotent.service.IdempotentService;
import com.yts.yyt.common.rocketmq.constans.RocketMQConstants;
import com.yts.yyt.common.rocketmq.util.MessageViewParseUtil;
import com.yts.yyt.order.api.entity.WxOrderShippingEntity;
import com.yts.yyt.order.api.enums.BusinessTypeEnum;
import com.yts.yyt.order.service.WxOrderShippingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

/**
 * 微信订单发货MQ
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
		consumerGroup ="${rocketmq.env}_" + RocketMQConstants.ConsumerGroup.GROUP_ORDER_SHIPPING,
		topic = "${rocketmq.env}_" + RocketMQConstants.Topic.TOPIC_ORDER_SHIPPING
)
public class WxOrderShippingListener implements RocketMQListener {

	private final WxOrderShippingService wxOrderShippingService;
    
    private final IdempotentService idempotentService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ConsumeResult consume(MessageView messageView) {
        String messageId = messageView.getMessageId().toString();
		ByteBuffer body = messageView.getBody();
		String message = StandardCharsets.UTF_8.decode(body).toString();
		log.info("[微信小程序订单待发货]---->>> messageId：{} , message:{}", messageId,message);
		boolean isFirstProcess = idempotentService.tryInsertRecord(messageId, BusinessTypeEnum.WX_ORDER_SHIPPING.getCode());
		if (!isFirstProcess) {
			log.info("[微信小程序订单待发货]---->>> 重复消息, messageId={}", messageId);
			return ConsumeResult.SUCCESS;
		}
		WxOrderShippingEntity entity = MessageViewParseUtil.parsePayloadMessage(message, WxOrderShippingEntity.class);
		wxOrderShippingService.save(entity);
		return ConsumeResult.SUCCESS;
    }
} 
