package com.yts.yyt.order.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.shop.response.WxMaShopDeliveryGetCompanyListResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yts.yyt.order.api.entity.WxOrderShippingEntity;
import com.yts.yyt.order.mapper.WxOrderShippingMapper;
import com.yts.yyt.order.service.WxOrderShippingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 微信小程序订单发货信息
 *
 * <AUTHOR>
 * @date 2025-01-06 19:28:37
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WxOrderShippingServiceImpl extends ServiceImpl<WxOrderShippingMapper, WxOrderShippingEntity> implements WxOrderShippingService {

    /**
     * 获取微信小程序所有运力id的列表url
     */
    @Value("${smallprogram.deliveryUrl:https://api.weixin.qq.com/cgi-bin/express/delivery/open_msg/get_delivery_list}")
    private  String  deliveryUrl;

    @Override
    public boolean updateStatus(Long id, Integer status, String errorMsg) {
        WxOrderShippingEntity entity = getById(id);
        if (entity == null) {
            return false;
        }
        
        entity.setStatus(status);
        entity.setErrorMsg(errorMsg);
        entity.setUpdateTime(LocalDateTime.now());
        
        return updateById(entity);
    }

    /**
     * 获取快递公司列表
     *
     * @return
     */
    @Override
    public List<WxMaShopDeliveryGetCompanyListResponse.CompanyListBean> getDeliveryList(WxMaService wxMaService) {
        log.info("获取快递公司列表");
        List<WxMaShopDeliveryGetCompanyListResponse.CompanyListBean> companyList = new ArrayList<WxMaShopDeliveryGetCompanyListResponse.CompanyListBean>();
        try {
            String responseContent = wxMaService.post(deliveryUrl, "{}");
            JSONObject response = JSONUtil.parseObj(responseContent);
            Integer errcode = (Integer) response.get("errcode");
            if (errcode == 0) {
                companyList = response.getBeanList("delivery_list", WxMaShopDeliveryGetCompanyListResponse.CompanyListBean.class);
            }
            return companyList;
        } catch (WxErrorException e) {
            log.info("获取快递公司列表 返回异常：{}", e.getMessage());
            return companyList;
        } catch (Exception e) {
            log.info("获取快递公司列表 返回异常：{}", e.getMessage());
            return companyList;
        }
    }
}
