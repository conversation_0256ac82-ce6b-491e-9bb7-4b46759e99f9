package com.yts.yyt.order.controller;

import java.util.List;

import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.log.annotation.SysLog;
import com.yts.yyt.common.security.annotation.HasPermission;
import com.yts.yyt.common.security.annotation.Inner;
import com.yts.yyt.order.api.dto.coupon.CouponActivityCreateDTO;
import com.yts.yyt.order.api.dto.coupon.CouponActivityEditDTO;
import com.yts.yyt.order.api.dto.coupon.CouponActivityListDTO;
import com.yts.yyt.order.api.dto.coupon.IdDTO;
import com.yts.yyt.order.api.vo.ActivitySimpleVO;
import com.yts.yyt.order.api.vo.CouponActivityVO;
import com.yts.yyt.order.service.CouponActivityService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

/**
 * 优惠券活动管理
 *
 * <AUTHOR>
 * @date 2024-02-13
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/admin/coupon/activity")
@Tag(description = "优惠券活动管理", name = "优惠券活动管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class CouponActivityController {

    private final CouponActivityService couponActivityService;

    /**
     * 优惠券活动列表
     * 
     * @param listDTO 查询参数
     * @return R
     */
    @Operation(summary = "优惠券活动列表", description = "后台管理优惠券活动列表查询 权限：coupon_activity_view")
    @PostMapping("/list")
    @SysLog("查询优惠券活动列表")
    @HasPermission("coupon_activity_view")
    public R<Page<CouponActivityVO>> list(@RequestBody CouponActivityListDTO listDTO) {
        return R.ok(couponActivityService.getActivityPage(listDTO));
    }

    /**
     * 创建优惠券活动
     * 
     * @param createDTO 创建参数
     * @return R
     */
    @Operation(summary = "创建优惠券活动", description = "后台管理创建优惠券活动 权限：coupon_activity_add")
    @SysLog("创建优惠券活动")
    @PostMapping("/create")
    @HasPermission("coupon_activity_add")
    public R<Boolean> create(@RequestBody @Validated CouponActivityCreateDTO createDTO) {
        return R.ok(couponActivityService.createActivity(createDTO));
    }

    /**
     * 编辑优惠券活动
     * 
     * @param editDTO 编辑参数
     * @return R
     */
    @Operation(summary = "编辑优惠券活动", description = "后台管理编辑优惠券活动 权限：coupon_activity_edit")
    @SysLog("编辑优惠券活动")
    @PostMapping("/edit")
    @HasPermission("coupon_activity_edit")
    public R<Boolean> edit(@RequestBody @Validated CouponActivityEditDTO editDTO) {
        return R.ok(couponActivityService.editActivity(editDTO));
    }

    /**
     * 删除优惠券活动
     * 
     * @param idDTO 活动ID
     * @return R
     */
    @Operation(summary = "删除优惠券活动", description = "后台管理删除优惠券活动 权限：coupon_activity_del")
    @SysLog("删除优惠券活动")
    @PostMapping("/delete")
    @HasPermission("coupon_activity_del")
    public R<Boolean> delete(@RequestBody IdDTO idDTO) {
        return R.ok(couponActivityService.deleteActivity(idDTO.getId()));
    }

    /**
     * 查询活动列表
     * 
     * @return R
     */
    @Operation(summary = "查询活动列表", description = "查询优惠券活动列表，返回活动ID和名称 权限：coupon_activity_view")
    @PostMapping("/simple/list")
    @SysLog("查询优惠券活动简单列表")
    public R<List<ActivitySimpleVO>> simpleList() {
        return R.ok(couponActivityService.getActivitySimpleList());
    }
}
