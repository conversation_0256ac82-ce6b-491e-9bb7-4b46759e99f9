package com.yts.yyt.order.service;

import com.alibaba.fastjson.JSON;
import com.yts.yyt.admin.api.entity.SysDictItem;
import com.yts.yyt.admin.api.feign.RemoteDictService;
import com.yts.yyt.admin.api.feign.RemoteParamService;
import com.yts.yyt.common.core.dto.IdDTO;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.pay.huifu.dto.HuifuRefundDTO;
import com.yts.yyt.common.pay.huifu.vo.ScanPayRefundVO;
import com.yts.yyt.goods.api.dto.GoodsLotUpdateStateDTO;
import com.yts.yyt.goods.api.dto.MerchantResellDTO;
import com.yts.yyt.goods.api.entity.GoodsLotInfo;
import com.yts.yyt.goods.api.exception.GoodsBizErrorEnum;
import com.yts.yyt.goods.api.exception.GoodsException;
import com.yts.yyt.goods.api.feign.RemoteGoodsInfoService;
import com.yts.yyt.goods.api.feign.RemoteGoodsLotInfoService;
import com.yts.yyt.goods.api.vo.GoodsInfoVO;
import com.yts.yyt.merchant.api.dto.CityPartnerAndMerchantDTO;
import com.yts.yyt.merchant.api.dto.MerchantDTO;
import com.yts.yyt.merchant.api.feign.RemoteMerchantService;
import com.yts.yyt.order.api.exception.OrderBizErrorCodeEnum;
import com.yts.yyt.order.api.exception.OrderException;
import com.yts.yyt.pay.api.feign.RemoteHuifuRefundService;
import com.yts.yyt.user.api.dto.AccountOperateDTO;
import com.yts.yyt.user.api.dto.AccountTransactionSettleDTO;
import com.yts.yyt.user.api.entity.UserBankCardEntity;
import com.yts.yyt.user.api.feign.*;
import com.yts.yyt.user.api.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class BaseRemoteService {

    @Autowired
    private RemoteDictService remoteDictService;
    @Autowired
    private RemoteGoodsInfoService remoteGoodsInfoService;
    @Autowired
    private RemoteParamService remoteParamService;
    @Autowired
    private RemoteBizUserInfoService userInfoService;
    @Autowired
    private RemoteUserAddressService remoteUserAddressService;
    @Autowired
    private RemoteUserIdCardService remoteUserIdCardService;
    @Autowired
    private RemoteUserBankCardService remoteUserBankCardService;

    @Autowired
    private RemoteGoodsLotInfoService remoteGoodsLotInfoService;

    @Lazy
    @Autowired
    private RemoteMerchantService remoteMerchantService;

    @Autowired
    private RemoteUserAuthenticationService remoteUserAuthenticationService;
    

    @Autowired
    private RemoteAccountService remoteAccountService;

	@Autowired
	private RemoteHuifuRefundService remoteHuifuRefundService;

    @Lazy
    @Autowired
    private RemoteUserHuifuAccountService remoteUserHuifuAccountService;
    

    public List<SysDictItem> getSysDictItem(String dictType) {
        R<List<SysDictItem>> listR = remoteDictService.getInnerDictByType(dictType);
        if (listR == null || !listR.isOk() || listR.getData() == null) {
            throw OrderException.build(OrderBizErrorCodeEnum.GET_DICTIONARY_CONFIGURATION_EXCEPTION);
        }
        return listR.getData();
    }

    public Map<String, String> getSysDictItemReturnMap(String dictType) {
        List<SysDictItem> items = getSysDictItem(dictType);
        Map<String, String> map = new HashMap<>();
        for (SysDictItem item : items) {
            map.put(item.getLabel(), item.getItemValue());
        }
        return map;
    }

    /**
     * @param dictType 字典类型
     * @param label    字典项名称
     */
    public SysDictItem getSysDictItem(String dictType, String label) {
        R<List<SysDictItem>> listR = remoteDictService.getInnerDictByType(dictType);
        if (listR == null || !listR.isOk() || listR.getData() == null) {
            throw OrderException.build(OrderBizErrorCodeEnum.GET_DICTIONARY_CONFIGURATION_EXCEPTION);
        }

        for (SysDictItem datum : listR.getData()) {
            if (datum.getLabel().equals(label)) {
                return datum;
            }
        }
        throw OrderException.build(OrderBizErrorCodeEnum.THE_DICTIONARY_CONFIGURATION_DOES_NOT_EXIST);
    }

    /**
     * 通过id获取商品详细
     *
     * @param goodsId 商品id
     * @return GoodsInfoVO
     */
    public GoodsInfoVO getGoodsInfo(Long goodsId) {
        R<GoodsInfoVO> goodsInfoVOR = remoteGoodsInfoService.info(goodsId);
        if (goodsInfoVOR == null || !goodsInfoVOR.isOk() || goodsInfoVOR.getData() == null) {
            throw OrderException.build(OrderBizErrorCodeEnum.FIRST_LAUNCH_GOOD_NOT_FOUND);
        }
        return goodsInfoVOR.getData();
    }

    public String getParamByKey(String key) {
        R<String> r = remoteParamService.getByKey(key);
        if (r == null || r.getData() == null || !r.isOk()) {
            throw OrderException.build(OrderBizErrorCodeEnum.THE_PARAM_DOES_NOT_EXIST);
        }
        return r.getData();
    }

    public UserInfoVO getUserInfo(Long userId) {
        R<UserInfoVO> r = userInfoService.getDetailById(userId);
        if (r == null || r.getData() == null || !r.isOk()) {
            throw OrderException.build(OrderBizErrorCodeEnum.USER_NOT_EXIST);
        }
        return r.getData();
    }

    public QueryUserAddressVO getUserAddressById(Long id) {
        R<QueryUserAddressVO> r = remoteUserAddressService.info(id);
        if (r == null || r.getData() == null || !r.isOk()) {
            throw OrderException.build(OrderBizErrorCodeEnum.USER_ADDRESS_NOT_EXIST);
        }
        return r.getData();
    }

    public UserBankCardEntity getUserBankCard(Long id) {
        R<UserBankCardEntity> r = remoteUserBankCardService.info(id);
        if (r == null || r.getData() == null || !r.isOk()) {
            throw OrderException.build(OrderBizErrorCodeEnum.USER_BANK_CARD_NOT_EXIST);
        }
        return r.getData();
    }

    public QueryUserInfoVO getUserIcCardInfoById(Long userId) {
        R<QueryUserInfoVO> r = remoteUserIdCardService.info(userId);
        if (r == null || r.getData() == null || !r.isOk()) {
            throw OrderException.build(OrderBizErrorCodeEnum.USER_ICCARD_NOT_EXIST);
        }
        return r.getData();
    }

    public UserInfoVO getUserInfoByMobile(String mobile) {
        R<UserInfoVO> r = userInfoService.getByMobile(mobile);
        if (r == null || r.getData() == null || !r.isOk()) {
            throw OrderException.build(OrderBizErrorCodeEnum.USER_ICCARD_NOT_EXIST);
        }
        return r.getData();
    }


    /**
     * 更新拍品状态
     * 
     * @param goodsLotUpdateStateDTO
     */
    public void updateGoodsLotState(GoodsLotUpdateStateDTO goodsLotUpdateStateDTO) {

        R<Boolean> r = remoteGoodsLotInfoService.feignUpdateState(goodsLotUpdateStateDTO);
        if (r == null || !r.isOk() || !r.getData()) {
            throw OrderException.build(OrderBizErrorCodeEnum.UPDATE_GOODS_INFO_ERROR);
        }
    }

    /**
     * 根据拍品id获取拍品信息
     * 
     * @param id
     * @return
     */
    public GoodsLotInfo getGoodsLotById(Long id) {
        IdDTO idDTO = new IdDTO();
        idDTO.setId(String.valueOf(id));
        R<GoodsLotInfo> r = remoteGoodsLotInfoService.queryById(idDTO);
        if (r == null || !r.isOk() || r.getData() == null) {
            throw OrderException.build(OrderBizErrorCodeEnum.FIRST_LAUNCH_GOOD_NOT_FOUND);
        }
        return r.getData();
    }

    /**
     * 转售
     * 
     * @param dto
     * @return
     */
    public Boolean resell(MerchantResellDTO dto) {
        R<Boolean> r = remoteGoodsLotInfoService.resell(dto);
        if (r == null || !r.isOk() || r.getData() == null) {
            throw OrderException.build(OrderBizErrorCodeEnum.RESELL_ERROR);
        }
        return r.getData();
    }

    /**
     * 根据商户id获取商户信息
     * 
     * @return
     */
    public CityPartnerAndMerchantDTO getPartnerByMerchantId(Long id) {
        IdDTO idDTO = new IdDTO();
        idDTO.setId(String.valueOf(id));
        R<CityPartnerAndMerchantDTO> r = remoteMerchantService.getPartnerByMerchantId(idDTO);
        if (r == null || !r.isOk() || r.getData() == null) {
            throw GoodsException.build(GoodsBizErrorEnum.QUERY_MERCHANT_INFO_ERROR);
        }
        return r.getData();
    }

    /**
     * 根据系统用户ID获取商户ID列表
     * 
     * @param userId
     * @return
     */
    public List<Long> getMerchantIdsBySysUserId(Long userId) {
        R<List<Long>> r = remoteMerchantService.getMerchantIdsBySysUserId(userId);
        return r.getData();
    }

    /**
     * 根据移动端用户id获取商户信息
     * 
     * @return
     */
    public MerchantDTO getMerchantByUserId(Long id) {
        IdDTO idDTO = new IdDTO();
        idDTO.setId(String.valueOf(id));
        R<MerchantDTO> r = remoteMerchantService.getMerchantByUserId(idDTO);
        // 调用结果有处理，不做判断空处理，直接返回
        if (r == null || !r.isOk() || r.getData() == null) {
            throw GoodsException.build(GoodsBizErrorEnum.QUERY_MERCHANT_INFO_ERROR);
        }
        return r.getData();
    }

    /**
     * 获取用户实名认证信息
     * 
     * @param userId   用户ID
     * @param authType 实名类型
     * @return 是否实名
     */
    public Boolean getUserAuthentication(Long userId, String authType) {
        R<UserAuthenticationVO> userAuthentication = remoteUserAuthenticationService.getUserAuthentication(userId,
                authType);
        if (userAuthentication == null || !userAuthentication.isOk()) {
            // 实名认证信息查询失败
            throw OrderException.build(OrderBizErrorCodeEnum.USER_AUTHENTICATION_ERROR);
        }
        return userAuthentication.getData() != null;
    }

    /**
     * 扫码支付退款
     *
     * @param dto 退款参数
     * @return 退款结果
     */
    public ScanPayRefundVO huifuRefund(HuifuRefundDTO dto) {
		R<ScanPayRefundVO> scanPayRefundVO = remoteHuifuRefundService.huifuRefund(dto);
		if (scanPayRefundVO == null || !scanPayRefundVO.isOk() || scanPayRefundVO.getData() == null || !scanPayRefundVO.getData().getTradeSuccess()) {
			throw OrderException.build(OrderBizErrorCodeEnum.DELAY_REFUND_QUERY_ERROR);
		}
		return scanPayRefundVO.getData();
    }

    /**
     * 添加并冻结余额
     */
    public void addAndFreezeBalance(AccountOperateDTO dto) {
        R<Boolean> r = remoteAccountService.addAndFreezeBalance(dto);
        if (r == null || !r.isOk() || !r.getData()) {
            throw OrderException.build(OrderBizErrorCodeEnum.ADD_AND_FREEZE_BALANCE_ERROR);
        }
    }
    /**
     * 交易结算
     */
    public void settleTransaction(AccountTransactionSettleDTO dto) {
        R<Boolean> r = remoteAccountService.settleTransaction(dto);
        if (r == null || !r.isOk() || !r.getData()) {
            throw OrderException.build(OrderBizErrorCodeEnum.SETTLE_TRANSACTION_ERROR);
        }
    }

    /**
     * 解冻
     */
    public void unfreezeBalance(AccountOperateDTO dto) {
        R<Boolean> r = remoteAccountService.unfreezeBalance(dto);
        if (r == null || !r.isOk() || !r.getData()) {
            throw OrderException.build(OrderBizErrorCodeEnum.UNFREEZE_BALANCE_ERROR);
        }
    }

	/**
	 * 减少冻结金额
	 */
	public void reduceFreezeBalance(AccountOperateDTO dto) {
		R<Boolean> r = remoteAccountService.reduceFreezeBalance(dto);
		if (r == null || !r.isOk() || !r.getData()) {
			throw OrderException.build(OrderBizErrorCodeEnum.REDUCE_AND_FREEZE_BALANCE_ERROR);
		}
	}

    /**
     * 根据汇付id获取账户信息
     * @param huifuId
     * @return
     */
    public UserHuifuAccountQueryVO queryUserHuifuAccountInfo(String huifuId){
        IdDTO dto = new IdDTO();
        dto.setId(huifuId);
        R<UserHuifuAccountQueryVO> r = remoteUserHuifuAccountService.query(dto);
        if (r == null || !r.isOk()) {
            log.error("根据id查询用户汇付账户信息失败：{},{}", JSON.toJSON(dto), r);
        }
        return r.getData();
    }

}
