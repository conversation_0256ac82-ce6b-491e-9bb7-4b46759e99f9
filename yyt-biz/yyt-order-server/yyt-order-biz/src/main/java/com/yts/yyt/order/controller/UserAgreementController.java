package com.yts.yyt.order.controller;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.ess.CallbackAes;
import com.yts.yyt.common.ess.properties.EssProperties;
import com.yts.yyt.common.feign.annotation.NoToken;
import com.yts.yyt.common.log.annotation.SysLog;
import com.yts.yyt.common.security.annotation.Inner;
import com.yts.yyt.common.security.util.SecurityUtils;
import com.yts.yyt.common.swagger.base.BaseController;
import com.yts.yyt.order.api.dto.UserAgreementDTO;
import com.yts.yyt.order.service.UserAgreementService;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.nio.charset.StandardCharsets;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 用户协议
 *
 * <AUTHOR>
 * @date 2025-01-07 11:02:15
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/agreement" )
@Tag(description = "userAgreement" , name = "用户协议管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class UserAgreementController extends BaseController{

    private final UserAgreementService userAgreementService;

	private final EssProperties essProperties;

	@Operation(summary = "获取签约状态", description = "获取合同签约状态" )
    @GetMapping("/getSignStatus/{orderId}")
    public R<Boolean> getUserAgreement(@PathVariable("orderId") Long orderId) {
        return R.ok(userAgreementService.getSignStatusByOrderId(orderId));
    }

    /**
     *
     * @param orderId
     * @return
     */
    @Operation(summary = "签置合同", description = "获取签约URL" )
    @GetMapping("/getSign/{orderId}")
    @SysLog("获取签约地址")
    public R<String> getSignUrl(@PathVariable("orderId") Long orderId) {

        return R.ok(userAgreementService.getSignUrl(orderId, SecurityUtils.getUser().getId()));
    }

	/**
	 *
	 * @param orderId
	 * @return
	 */
	@Operation(summary = "获取下载地址", description = "获取下载地址" )
	@GetMapping("/getDownloadUrl/{orderId}")
	@SysLog("获取下载地址")
	public R<String> getDownloadUrl(@PathVariable("orderId") Long orderId) {

		return R.ok(userAgreementService.getDownLoadUrl(orderId));
	}

	@NoToken
	@Inner(value = false)
	@Operation(summary = "电子签回调", description = "电子签回调" )
	@PostMapping("/tx/callback")
	public String callback(@RequestBody Map<String, String> requestBody) {
		log.info("腾讯电子签回调:{}", requestBody);
		if (ObjUtil.isEmpty(requestBody)) {
			return "SUCCESS";
		}
		byte[] key = essProperties.callbackKey.getBytes();
		// 传入CallbackUrlKey和密文
        byte[] origData = null;
        try {
            origData = CallbackAes.aesDecrypt(requestBody.get("encrypt").getBytes(StandardCharsets.UTF_8), key);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // 打印解密后的内容，格式为json
		String plaintext = new String(origData, StandardCharsets.UTF_8);
		log.info("腾讯电子签回调明文{}", plaintext);

		JSONObject plaintextJson = JSONUtil.parseObj(plaintext);
		if (plaintextJson.getStr("MsgType", "").equals("FlowStatusChange")) {
			List<UserAgreementDTO> list = new ArrayList<>();
			JSONObject msgData = plaintextJson.getJSONObject("MsgData");
			String flowId = msgData.getStr("FlowId");
			msgData.getJSONArray("Approvers")
					.forEach(approver -> {
						JSONObject approverJson = JSONUtil.parseObj(approver);
						UserAgreementDTO userAgreementDTO = new UserAgreementDTO();
							userAgreementDTO.setMobile(approverJson.getStr("ApproverMobile"));
							userAgreementDTO.setApproveStatus(approverJson.getLong("ApproveCallbackStatus"));
							userAgreementDTO.setEssFlowId(flowId);
						list.add(userAgreementDTO);
					});
			userAgreementService.agreementBack(list);
		} else{
			log.info("腾讯电子签回调明文忽略:{}", plaintext);
		}
		return "SUCCESS";
	}

	public static void main (String[] args) throws Exception {

		String ciphertext = "t1fE8qbxFkkoe8cUtIXuTHHWi3Eykcq4yUHlTnhWndRNZdtxX8aI33Yx8uTH4S7xtTaa2ynXBNTUXPMfPny9KkjtcBxGsxjjgrz9gQ9tfFCS0+WqadGJqPNdaC/cqZLcPb1Q81gZshU2HCEsnz1Fl9EZvKM/MNEUa9nYrDWiY9kYNI1DNOw9mXASxK2OCJtXHEY802198BvVz0blCMzoTUchrmS+CUfFWBxmFpKYdEJo/qN52YDZ/wZErhJAe1bveV+8XcML3lJM3+D0S5NIBZrpbtR3XN0GR8gMA8tQzqDvRd5gW6878o6BtyNUzW0Ie1OTtSRLyry+5jGvNi2+aw==";
		byte[] text = CallbackAes.aesDecrypt(ciphertext.getBytes(StandardCharsets.UTF_8), "864AEAB160D54ED3B25C9454F372DFD5".getBytes());
		System.out.println(	new String(text, StandardCharsets.UTF_8));
	}
}
