package com.yts.yyt.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yts.yyt.common.core.constant.UserMsgConstants;
import com.yts.yyt.common.core.dto.IdDTO;
import com.yts.yyt.common.file.service.ImageUrlService;
import com.yts.yyt.common.logistics.LogisticsService;
import com.yts.yyt.common.logistics.model.ExpressCompany;
import com.yts.yyt.common.pay.huifu.dto.HuifuRefundDTO;
import com.yts.yyt.common.pay.huifu.vo.ScanPayRefundVO;
import com.yts.yyt.common.security.util.SecurityUtils;
import com.yts.yyt.goods.api.dto.GoodsLotUpdateStateDTO;
import com.yts.yyt.goods.api.enums.GoodsInfoEnum;
import com.yts.yyt.order.api.constant.OrderLogTemplate;
import com.yts.yyt.order.api.dto.OrderProtectionAuditDTO;
import com.yts.yyt.order.api.dto.OrderProtectionDTO;
import com.yts.yyt.order.api.dto.OrderProtectionExportDTO;
import com.yts.yyt.order.api.dto.OrderProtectionTransferDTO;
import com.yts.yyt.order.api.dto.admin.AdminProtectionOrderDTO;
import com.yts.yyt.order.api.entity.*;
import com.yts.yyt.order.api.enums.CouponEnum;
import com.yts.yyt.order.api.enums.OrderEnum;
import com.yts.yyt.order.api.exception.OrderBizErrorCodeEnum;
import com.yts.yyt.order.api.exception.OrderException;
import com.yts.yyt.order.api.vo.AdminProtectionOrderVO;
import com.yts.yyt.order.api.vo.OrderProtectionExportVO;
import com.yts.yyt.order.event.OrderStatusChangeEvent;
import com.yts.yyt.order.mapper.OrderDelayTransMapper;
import com.yts.yyt.order.mapper.OrderInfoMapper;
import com.yts.yyt.order.mapper.OrderLogisticsMapper;
import com.yts.yyt.order.mapper.OrderProtectionMapper;
import com.yts.yyt.order.service.*;
import com.yts.yyt.order.utils.OrderNoGenerator;
import com.yts.yyt.user.api.constant.TradeDescConstants;
import com.yts.yyt.user.api.constant.enums.AccountEnum;
import com.yts.yyt.user.api.dto.AccountOperateDTO;
import com.yts.yyt.user.api.feign.RemoteUserMsgService;
import com.yts.yyt.user.api.vo.UserInfoVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.seata.spring.annotation.GlobalTransactional;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 订单维权表
 *
 * <AUTHOR>
 * @date 2025-01-06 19:28:37
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderProtectionServiceImpl extends ServiceImpl<OrderProtectionMapper, OrderProtectionEntity>
		implements OrderProtectionService {

	private final OrderInfoMapper orderInfoMapper;
	private final OrderLogisticsMapper orderLogisticsMapper;
	private final OrderLogService orderLogService;
	private final BaseRemoteService baseRemoteService;
	private final OrderNoGenerator orderNoGenerator;
	private final OrderProtectionMapper orderProtectionMapper;
	private final LogisticsService logisticsService;
	private final RemoteUserMsgService remoteUserMsgService;
	private final ImageUrlService imageUrlService;
	private final LogisticsInfoService logisticsInfoService;
	private final FirstLaunchOrderInfoServiceImpl firstLaunchOrderInfoService;
	private final OrderDelayTransService orderDelayTransService;
	private final OrderDelayTransMapper orderDelayTransMapper;
    private final CouponUsageService couponUsageService;
	private final ApplicationEventPublisher publisher;

	@Override
	@Transactional
	public void protectRefund(OrderProtectionAuditDTO dto) {
		// DC 2025/3/5 15:50 处理订单已经申请过退款的情况，激活退款数据重新进入退款中
		OrderProtectionEntity orderProtectionEntity = orderProtectionMapper
				.selectOne(new QueryWrapper<OrderProtectionEntity>().lambda()
						.eq(OrderProtectionEntity::getOrderId, dto.getOrderId()));
		if (ObjUtil.isNotEmpty(orderProtectionEntity)) {
			// 只能申请一次
			throw OrderException.build(OrderBizErrorCodeEnum.DUPLICATE_APPLICATION_REFUND);
			// activeOrderProtection(orderProtectionEntity,dto);
		} else {
			OrderInfoEntity orderInfoEntity = createOrderProtection(dto,
					OrderEnum.ProtectionStatus.APPLY_REFUND.getType(), null, true);

			// 更新退款中状态
			orderInfoEntity.setProtectStatus(OrderEnum.OrderProtectStatus.REFUND_IN_PROGRESS.getType());
			// orderInfoEntity.setStatus(OrderEnum.OrderStatus.FIRST_LAUNCH_REFUNDING.getType());
			orderInfoMapper.updateById(orderInfoEntity);
			// 生成订单日志
			UserInfoVO userInfo = baseRemoteService.getUserInfo(orderInfoEntity.getUserId());
			orderLogService.createOrderLog(orderInfoEntity,
					String.format(OrderLogTemplate.ORDER_USER_APPLY_REFUND, userInfo.getUsername()));
			// 卖家消息 todo
			// 买家退款申请通知
		}

	}

	@Override
	public void checkRefund(IdDTO dto) {
		OrderProtectionEntity orderProtectionEntity = orderProtectionMapper
				.selectOne(new QueryWrapper<OrderProtectionEntity>().lambda()
						.eq(OrderProtectionEntity::getOrderId, Long.parseLong(dto.getId())));
		if (ObjUtil.isNotEmpty(orderProtectionEntity)) {
			// 只能申请一次
			throw OrderException.build(OrderBizErrorCodeEnum.DUPLICATE_APPLICATION_REFUND);
		}
		OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(dto.getId());
		if (ObjUtil.isEmpty(orderInfoEntity)) {
			throw OrderException.build(OrderBizErrorCodeEnum.ORDER_NOT_EXIST);
		}
		// 退款检验订单退款/售后截至时间
		firstLaunchOrderInfoService.validateOrderManyDay(orderInfoEntity);
	}

	@Override
	@GlobalTransactional
	@Transactional(rollbackFor = Exception.class)
	public void protectAgree(Long userId, Long orderId) {
		log.info("[protectAgree] 开始处理退款同意, userId: {}, orderId: {}", userId, orderId);

		// 1. 校验订单是否存在
		OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
		if (ObjectUtil.isNull(orderInfoEntity)) {
			log.error("[protectAgree] 订单不存在, orderId: {}", orderId);
			throw OrderException.build(OrderBizErrorCodeEnum.ORDER_NOT_EXIST);
		}
		log.info("[protectAgree] 订单信息查询成功, orderId: {}, status: {}", orderId, orderInfoEntity.getStatus());

		// 2. 校验订单状态
		if (orderInfoEntity.getStatus().equals(OrderEnum.OrderStatus.FIRST_LAUNCH_CLOSED.getType())) {
			log.error("[protectAgree] 订单状态不正确, orderId: {}, status: {}", orderId, orderInfoEntity.getStatus());
			throw OrderException.build(OrderBizErrorCodeEnum.FIRST_LAUNCH_STATUS_MISMATCH);
		}

		// 3. 校验维权订单状态
		log.info("[protectAgree] 开始校验维权订单状态, orderId: {}", orderId);
		checkStatus(orderId);
		log.info("[protectAgree] 维权订单状态校验通过, orderId: {}", orderId);

		// 4. 发起退款
		log.info("[protectAgree] 开始发起退款, orderId: {}", orderId);
		HuifuRefundDTO dto = new HuifuRefundDTO();
		dto.setOrderId(String.valueOf(orderId));
		// 调用汇付退款接口 获取退款流水号
		ScanPayRefundVO scanPayRefundVO = baseRemoteService.huifuRefund(dto);
		log.info("[protectAgree] 汇付退款接口调用成功, orderId: {}, hfSeqId: {}", orderId, scanPayRefundVO.getHfSeqId());

		// 5. 处理退款
		refunding(orderId, scanPayRefundVO.getHfSeqId());
		log.info("[protectAgree] 退款处理完成, orderId: {}", orderId);
	}

	@Override
	@GlobalTransactional
	@Transactional(rollbackFor = Exception.class)
	public void protectReject(OrderProtectionDTO orderProtectionDTO) {
		// OrderProtectionEntity orderProtectionEntity =
		// orderProtectionMapper.selectOne(new
		// QueryWrapper<OrderProtectionEntity>().lambda()
		// .eq(OrderProtectionEntity::getOrderId,orderProtectionDTO.getOrderId())
		// .eq(OrderProtectionEntity::getProtectionStatus,OrderEnum.ProtectionStatus.APPLY_REFUND.getType()));
		// if (orderProtectionEntity == null) {
		// throw OrderException.build(OrderBizErrorCodeEnum.FIRST_LAUNCH_NOT_FOUND);
		// }
		// if (StringUtils.isNotBlank(orderProtectionDTO.getReason())) {
		// orderProtectionEntity.setReason(orderProtectionDTO.getReason());
		// }
		OrderProtectionEntity orderProtectionEntity = checkStatus(orderProtectionDTO.getOrderId());
		orderProtectionEntity.setProtectionStatus(OrderEnum.ProtectionStatus.SELLER_REJECTED.getType());
		orderProtectionEntity.setUpdateBy(orderProtectionDTO.getLoginUserId().toString());
		orderProtectionMapper.updateById(orderProtectionEntity);

		OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderProtectionEntity.getOrderId());
		// 更新退款拒绝状态
		orderInfoEntity.setProtectStatus(OrderEnum.OrderProtectStatus.REFUND_REJECTED.getType());
		orderInfoMapper.updateById(orderInfoEntity);

		// 生成订单日志
		if (StringUtils.isNotBlank(orderProtectionDTO.getReason())) {
			orderLogService.createOrderLog(orderInfoEntity, String
					.format(OrderLogTemplate.ORDER_PLATFORM_REFUSE_REFUND_REASON, orderProtectionDTO.getReason()));
		} else {
			orderLogService.createOrderLog(orderInfoEntity, OrderLogTemplate.ORDER_PLATFORM_REFUSE_REFUND);
		}

		// 拒绝退款通知
		Map<String, Object> map = new HashMap<>();
		map.put("userId", orderInfoEntity.getUserId());
		map.put("msgScene", UserMsgConstants.REJECT_REFUND_NOTIFICATION);
		map.put("orderId", orderInfoEntity.getOrderNo());
		map.put("reason", orderProtectionDTO.getReason());
		map.put("orderAmount", orderInfoEntity.getAmount());
		remoteUserMsgService.sendUserMsg(map);
	}

	/**
	 * 后台确认收货
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void adminConfirmReceipt(OrderProtectionDTO orderProtectionDTO) {
		OrderProtectionEntity orderProtectionEntity = orderProtectionMapper
				.selectOne(new QueryWrapper<OrderProtectionEntity>().lambda()
						.eq(OrderProtectionEntity::getOrderId, orderProtectionDTO.getOrderId())
						.eq(OrderProtectionEntity::getProtectionStatus,
								OrderEnum.ProtectionStatus.APPLY_REFUND.getType()));
		if (orderProtectionEntity == null) {
			throw OrderException.build(OrderBizErrorCodeEnum.FIRST_LAUNCH_NOT_FOUND);
		}

		orderProtectionEntity.setProtectionStatus(OrderEnum.ProtectionStatus.SELLER_RECEIVED.getType());
		orderProtectionEntity.setUpdateBy(orderProtectionDTO.getLoginUserId().toString());
		orderProtectionMapper.updateById(orderProtectionEntity);

		OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderProtectionEntity.getOrderId());

		// 生成订单日志
		orderLogService.createOrderLog(orderInfoEntity, OrderLogTemplate.ORDER_SELLER_CONFIRM);
	}

	/**
	 * 转账
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	@GlobalTransactional
	public void adminTransfer(OrderProtectionTransferDTO dto) {
		OrderProtectionEntity orderProtectionEntity = orderProtectionMapper
				.selectOne(new QueryWrapper<OrderProtectionEntity>().lambda()
						.eq(OrderProtectionEntity::getOrderId, dto.getOrderId()));
		if (ObjectUtil.isNull(orderProtectionEntity)) {
			throw OrderException.build(OrderBizErrorCodeEnum.FIRST_LAUNCH_NOT_FOUND);
		}
		// 退款申请通过后,才可以转账
		if (!orderProtectionEntity.getProtectionStatus().equals(OrderEnum.ProtectionStatus.REFUND_APPROVED.getType())) {
			throw OrderException.build(OrderBizErrorCodeEnum.FIRST_LAUNCH_STATUS_MISMATCH);
		}
		// 增加退款支付凭证, 修改退款申请表状态为退款成功
		orderProtectionEntity.setRefundVoucherImgs(JSONUtil.toJsonStr(dto.getRefundVoucherImgs()));
		orderProtectionEntity.setUpdateTime(LocalDateTime.now());
		orderProtectionEntity.setProtectionStatus(OrderEnum.ProtectionStatus.REFUND_SUCCESS.getType());
		orderProtectionEntity.setUpdateBy(dto.getLoginUserId().toString());
		orderProtectionMapper.updateById(orderProtectionEntity);

		// 退款成功修改订单状态已关闭 并且更新退款成功状态
		OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderProtectionEntity.getOrderId());
		orderInfoEntity.setStatus(OrderEnum.OrderStatus.FIRST_LAUNCH_CLOSED.getType());
		orderInfoEntity.setProtectStatus(OrderEnum.OrderProtectStatus.REFUND_SUCCESS.getType());
		orderInfoMapper.updateById(orderInfoEntity);

		// 退款成功修改拍品状态
		GoodsLotUpdateStateDTO goodsLotUpdateStateDTO = new GoodsLotUpdateStateDTO();
		goodsLotUpdateStateDTO.setIds(Collections.singletonList(orderInfoEntity.getLotId()));
		goodsLotUpdateStateDTO.setState(GoodsInfoEnum.State.SELLING.getCode());
		baseRemoteService.updateGoodsLotState(goodsLotUpdateStateDTO);

		// 生成订单日志
		orderLogService.createOrderLog(orderInfoEntity, OrderLogTemplate.ORDER_REFUND_SUCCESS);
	}

	@Override
	@GlobalTransactional
	@Transactional(rollbackFor = Exception.class)
	public void protectTransfer(Long orderId, String refundSerio) {

		OrderProtectionEntity orderProtectionEntity = orderProtectionMapper
				.selectOne(new QueryWrapper<OrderProtectionEntity>().lambda()
						.eq(OrderProtectionEntity::getOrderId, orderId)
						.orderByDesc(OrderProtectionEntity::getCreateTime)
						.last("limit 1"));
		// 退款成功修改订单状态已关闭 并且更新退款成功状态
		OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
		if (orderProtectionEntity == null) {
			// DC 2025/3/3 18:42 在支付模块直接发起了退款，但是用户未发起退款申请，在这里也依次写入数据库
			OrderProtectionAuditDTO orderProtectionAuditDTO = new OrderProtectionAuditDTO();
			orderProtectionAuditDTO.setLoginUserId(orderInfoEntity.getUserId());
			orderProtectionAuditDTO.setOrderId(orderId);
			orderProtectionAuditDTO.setRefundType(OrderEnum.RefundType.ONLY_REFUND.getType());
			orderProtectionAuditDTO.setReasonType(OrderEnum.RefundReasonType.REASON_TYPE5.getType());
			orderProtectionAuditDTO.setLogisticsStatus(OrderEnum.RefundLogisticsStatus.LOGISTICS_STATUS1.getType());
			orderProtectionAuditDTO.setReason("系统自动退款");
			createOrderProtection(orderProtectionAuditDTO, OrderEnum.ProtectionStatus.REFUND_SUCCESS.getType(),
					refundSerio, false);

		} else {
			// 打印日志 卖家主动退款
			if (orderProtectionEntity.getProtectionStatus() != OrderEnum.ProtectionStatus.REFUND_APPROVED.getType()) {
				log.warn("卖家主动退款, orderId:{}", orderId);
			}
			Long userId = orderProtectionEntity.getUserId();
			// DC 2025/3/3 18:42 正常流程更新退款状态
			orderProtectionEntity.setUpdateBy(userId.toString());
			orderProtectionEntity.setProtectionStatus(OrderEnum.ProtectionStatus.REFUND_SUCCESS.getType());
			orderProtectionEntity.setRefundSerio(refundSerio);
			orderProtectionEntity.setRefundTime(LocalDateTime.now());
			orderProtectionMapper.updateById(orderProtectionEntity);
		}
		// DC 2025/3/27 18:25 线下支付订单不能在线退款
		if (orderInfoEntity.getPayType() == OrderEnum.OrderPayType.OFFLINE_PAY.getType()) {
			throw OrderException.build(OrderBizErrorCodeEnum.ORDER_PROTECT_ONLINE_ERROR);
		}
		orderInfoEntity.setStatus(OrderEnum.OrderStatus.FIRST_LAUNCH_CLOSED.getType());
		orderInfoEntity.setProtectStatus(OrderEnum.OrderProtectStatus.REFUND_SUCCESS.getType());
		orderInfoMapper.updateById(orderInfoEntity);

		// 根据订单id查询 更新状态
		OrderDelayTransEntity orderDelayTransEntity = orderDelayTransMapper
				.selectOne(new QueryWrapper<OrderDelayTransEntity>().lambda()
						.eq(OrderDelayTransEntity::getOrderId, orderId));
		orderDelayTransEntity.setStatus(OrderEnum.OrderDelayStatus.DELAY_REFUND_SUCCESS.getStatue());
		orderDelayTransEntity.setReqSeqId(refundSerio);
		orderDelayTransMapper.updateById(orderDelayTransEntity);

		// 退款成功修改拍品状态
		GoodsLotUpdateStateDTO goodsLotUpdateStateDTO = new GoodsLotUpdateStateDTO();
		goodsLotUpdateStateDTO.setIds(Collections.singletonList(orderInfoEntity.getLotId()));
		goodsLotUpdateStateDTO.setState(GoodsInfoEnum.State.SELLING.getCode());
		baseRemoteService.updateGoodsLotState(goodsLotUpdateStateDTO);

        // 退款成功
        CouponUsageEntity couponUsage = couponUsageService.lambdaQuery()
                .eq(CouponUsageEntity::getOrderId, orderInfoEntity.getId())
                .eq(CouponUsageEntity::getStatus, CouponEnum.UsageStatus.VERIFIED.getCode())
                .one();

        // 退款返还优惠券 优惠券使用记录 状态：3-已退款
        //              用户优惠券  状态：1-未用
        couponUsageService.updateCouponUsageStatus(orderInfoEntity.getId(),
                CouponEnum.UsageStatus.REFUNDED.getCode(),
                CouponEnum.UserCouponStatus.UNUSED.getCode());
        
		// 生成订单日志
		orderLogService.createOrderLog(orderInfoEntity, OrderLogTemplate.ORDER_REFUND_SUCCESS);

		AccountOperateDTO accountOperateDTO = new AccountOperateDTO();
		accountOperateDTO.setUserId(orderDelayTransEntity.getUserId());
		accountOperateDTO.setAmount(orderDelayTransEntity.getAmount());
		accountOperateDTO.setBusinessId(String.valueOf(orderProtectionEntity.getId()));
		accountOperateDTO.setAccountType(AccountEnum.AccountTypeEnum.BALANCE_ACCOUNT.getType());
		accountOperateDTO.setTradeDesc(TradeDescConstants.ORDER_REFUND_SUCCESS);
		accountOperateDTO.setBusinessTradeDesc(TradeDescConstants.ORDER_REFUND);
		accountOperateDTO.setBusinessType(AccountEnum.BusinessTypeEnum.REFUND);
		baseRemoteService.reduceFreezeBalance(accountOperateDTO);

		// 发送订单退款成功消息
		if(!ObjectUtil.isEmpty(orderInfoEntity.getShareCode())) {
			publisher.publishEvent(new OrderStatusChangeEvent(orderInfoEntity.getId(),orderInfoEntity.getStatus(),OrderEnum.OrderProtectStatus.REFUND_SUCCESS.getType(),null));
		}
	}

	@Override
	@Transactional
	public void protectCancel(Long userId, Long orderId) {
		OrderProtectionEntity orderProtectionEntity = orderProtectionMapper
				.selectOne(new QueryWrapper<OrderProtectionEntity>().lambda()
						.eq(OrderProtectionEntity::getOrderId, orderId)
						.eq(OrderProtectionEntity::getProtectionStatus,
								OrderEnum.ProtectionStatus.APPLY_REFUND.getType()));
		if (orderProtectionEntity == null) {
			throw OrderException.build(OrderBizErrorCodeEnum.FIRST_LAUNCH_NOT_FOUND);
		}
		orderProtectionEntity.setProtectionStatus(OrderEnum.ProtectionStatus.PROTECT_CANCEL.getType());
		orderProtectionEntity.setUpdateBy(userId.toString());
		orderProtectionMapper.updateById(orderProtectionEntity);

		OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderProtectionEntity.getOrderId());
		// 更新取消退款状态
		orderInfoEntity.setProtectStatus(OrderEnum.OrderProtectStatus.REFUND_CANCELLED.getType());
		// DC 2025/3/5 14:54 回退订单状态
		// orderInfoEntity.setStatus(orderProtectionEntity.getApplyingOrderStatus());
		orderInfoMapper.updateById(orderInfoEntity);
		// 生成订单日志
		UserInfoVO userInfo = baseRemoteService.getUserInfo(orderInfoEntity.getUserId());
		orderLogService.createOrderLog(orderInfoEntity,
				String.format(OrderLogTemplate.ORDER_USER_CLOSE_REFUND, userInfo.getUsername()));
	}

	@Override
	public void adminProtectClose(OrderProtectionDTO orderProtectionDTO) {
		// OrderProtectionEntity orderProtectionEntity =
		// orderProtectionMapper.selectOne(new
		// QueryWrapper<OrderProtectionEntity>().lambda()
		// .eq(OrderProtectionEntity::getOrderId,orderProtectionDTO.getOrderId())
		// .eq(OrderProtectionEntity::getProtectionStatus,OrderEnum.ProtectionStatus.APPLY_REFUND.getType()));
		// if (orderProtectionEntity == null) {
		// throw OrderException.build(OrderBizErrorCodeEnum.FIRST_LAUNCH_NOT_FOUND);
		// }
		OrderProtectionEntity orderProtectionEntity = checkStatus(orderProtectionDTO.getOrderId());
		orderProtectionEntity.setProtectionStatus(OrderEnum.ProtectionStatus.PROTECT_CANCEL.getType());
		orderProtectionEntity.setUpdateBy(orderProtectionDTO.getLoginUserId().toString());
		orderProtectionMapper.updateById(orderProtectionEntity);

		OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderProtectionEntity.getOrderId());
		// 更新取消退款状态
		orderInfoEntity.setProtectStatus(OrderEnum.OrderProtectStatus.REFUND_CANCELLED.getType());
		// DC 2025/3/5 14:54 回退订单状态
		// orderInfoEntity.setStatus(orderProtectionEntity.getApplyingOrderStatus());
		orderInfoMapper.updateById(orderInfoEntity);
		// 生成订单日志
		if (StringUtils.isNotBlank(orderProtectionDTO.getReason())) {
			orderLogService.createOrderLog(orderInfoEntity,
					String.format(OrderLogTemplate.ORDER_SELLER_CLOSE_REFUND, orderProtectionDTO.getReason()));
		} else {
			orderLogService.createOrderLog(orderInfoEntity, OrderLogTemplate.ORDER_SELLER_CLOSE);
		}
	}

	@Override
	public Page<AdminProtectionOrderVO> pageAdminOrderProtect(Page page, AdminProtectionOrderDTO dto) {
		QueryWrapper<AdminProtectionOrderVO> wrapper = new QueryWrapper<>();
		List<String> orderType = new ArrayList<>();
		orderType.add(OrderEnum.OrderType.FIRST_LAUNCH.getType());
		orderType.add(OrderEnum.OrderType.CONSIGNMENT.getType());
		orderType.add(OrderEnum.OrderType.STORE.getType());
		wrapper.in("a.order_type", orderType);
		if (ObjUtil.isNotEmpty(dto.getBeginTime()) && ObjUtil.isNotEmpty((dto.getEndTime()))) {
			wrapper.between("h.refund_time", dto.getBeginTime(), dto.getEndTime());
		}
		if (ObjUtil.isNotEmpty(dto.getQueryType())) {
			wrapper.like(firstLaunchOrderInfoService.getSearchColumn(dto.getQueryType()), dto.getQueryWords());
		}
		if (ObjUtil.isNotEmpty(dto.getProtectionStatus())) {
			if (dto.getProtectionStatus() == 1) {
				// 申请退款相关状态：1-申请退款 4-买家待退货 5-卖家待收货 6-卖家已收货
				wrapper.in("h.protection_status", Arrays.asList(OrderEnum.ProtectionStatus.APPLY_REFUND.getType(),
						OrderEnum.ProtectionStatus.BUYER_TO_RETURN.getType(),
						OrderEnum.ProtectionStatus.SELLER_TO_RECEIVE.getType(),
						OrderEnum.ProtectionStatus.REFUND_IN_PROGRESS.getType(),
						OrderEnum.ProtectionStatus.SELLER_RECEIVED.getType()));
			} else if (dto.getProtectionStatus() == 3) {
				// 退款成功相关状态：3-退款成功 8-部分退款
				wrapper.in("h.protection_status", Arrays.asList(OrderEnum.ProtectionStatus.REFUND_SUCCESS.getType(),
						OrderEnum.ProtectionStatus.PARTIAL_REFUND.getType()));
			} else {
				wrapper.eq("h.protection_status", dto.getProtectionStatus());
			}
		}

		if (ObjUtil.isNotEmpty(dto.getRefundType())) {
			wrapper.eq("h.refund_type", dto.getRefundType());
		}

//		// 商户查询自己的订单, 其他配置角色可以查询所有订单
		if (!firstLaunchOrderInfoService.getSelectRoleData()) {
			Long userId = SecurityUtils.getUser().getId();
			List<Long> merchantIds = baseRemoteService.getMerchantIdsBySysUserId(userId);
			if (ObjectUtil.isEmpty(merchantIds) || merchantIds.size() < 1)
				return new Page<AdminProtectionOrderVO>();
			wrapper.in("b.merchant_id", merchantIds);
		}
		wrapper.orderByDesc("h.create_time");

		Page<AdminProtectionOrderVO> voPage = orderInfoMapper.pageAdminOrderProtect(page, wrapper);

		// 添加退货时 买家填写的地址
		List<AdminProtectionOrderVO> voList = voPage.getRecords();
		if (CollUtil.isNotEmpty(voList)) {
			for (int i = 0; i < voList.size(); i++) {
				AdminProtectionOrderVO vo = voList.get(i);
				setRefundLogistics(vo, orderLogisticsMapper);

				vo.setImgUrl(imageUrlService.convertImageUrl(vo.getMainImage()).getSmallWebpUrl());
				vo.setMainImage(imageUrlService.convertImageUrl(vo.getMainImage()).getOriginUrl());
			}
		}

		return voPage;
	}

	@Override
	public AdminProtectionOrderVO detailOrderProtect(Long id) {
		AdminProtectionOrderVO vo = orderInfoMapper.detailOrderProtect(id);
		// DC 2025/3/4 15:34 刷入操作日志
		List<OrderLogEntity> operationLog = orderLogService.listByOrderId(vo.getOrderId());

		vo.setOperationLog(operationLog);
		// 添加退货时 买家填写的地址
		setRefundLogistics(vo, orderLogisticsMapper);
		vo.setImgUrl(imageUrlService.convertImageUrl(vo.getMainImage()).getSmallWebpUrl());
		vo.setMainImage(imageUrlService.convertImageUrl(vo.getMainImage()).getOriginUrl());
		return vo;
	}

	/**
	 * 订单备注
	 */
	@Transactional(rollbackFor = Exception.class)
	public void remark(Long orderId, String remark) {
		OrderProtectionEntity orderProtectionEntity = orderProtectionMapper
				.selectOne(new QueryWrapper<OrderProtectionEntity>().lambda()
						.eq(OrderProtectionEntity::getOrderId, orderId));
		if (ObjectUtil.isNull(orderProtectionEntity)) {
			throw OrderException.build(OrderBizErrorCodeEnum.FIRST_LAUNCH_NOT_FOUND);
		}
		orderProtectionEntity.setRemark(remark);
		orderProtectionEntity.setUpdateTime(LocalDateTime.now());
		orderProtectionMapper.updateById(orderProtectionEntity);
	}

	/**
	 * 功能 新增退款记录
	 * 创建于 2025/3/4 13:52
	 *
	 * <AUTHOR>
	 * @param dto           入参
	 * @param protectStatus 状态
	 * @param refundSerio   退款流水号
	 * @param isValid       是否验证
	 * @return OrderInfoEntity
	 */
	private OrderInfoEntity createOrderProtection(OrderProtectionAuditDTO dto, Integer protectStatus,
			String refundSerio, boolean isValid) {
		OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(dto.getOrderId());
		if (orderInfoEntity == null) {
			throw OrderException.build(OrderBizErrorCodeEnum.FIRST_LAUNCH_NOT_FOUND);
		}
		OrderProtectionEntity orderProtectionEntity = new OrderProtectionEntity();

		if (isValid) {
			if (orderInfoEntity.getProtectStatus() != 0
					&& orderInfoEntity.getProtectStatus() != OrderEnum.OrderProtectStatus.REFUND_CANCELLED.getType()) {
				throw OrderException.build(OrderBizErrorCodeEnum.DUPLICATE_APPLICATION_REFUND);
			}

			firstLaunchOrderInfoService.validateOrderManyDay(orderInfoEntity);

			firstLaunchOrderInfoService.validateOrderRecStatus(orderInfoEntity, dto);

			firstLaunchOrderInfoService.vlidateApplyProtectionOrderStatus(orderInfoEntity);
		} else {
			// DC 2025/3/25 16:25 来自于直接退款并新增数据，有变动注意维护
			orderProtectionEntity.setRefundTime(LocalDateTime.now());
		}
		orderProtectionEntity.setId(IdUtil.getSnowflakeNextId());
		orderProtectionEntity.setUserId(orderInfoEntity.getUserId());
		orderProtectionEntity.setProtectionNo(orderNoGenerator.generateOrderNo());
		orderProtectionEntity.setProtectionType(OrderEnum.OrderProtectionType.ORDER_REFUND.getType());
		orderProtectionEntity.setProtectionStatus(protectStatus);
		// orderProtectionEntity.setRefundSerio(refundSerio);
		orderProtectionEntity.setAmount(orderInfoEntity.getAmount());
		orderProtectionEntity.setCreateBy(String.valueOf(dto.getLoginUserId()));
		orderProtectionEntity.setUpdateBy(String.valueOf(dto.getLoginUserId()));
		orderProtectionEntity.setApplyingOrderStatus(orderInfoEntity.getStatus());
		// orderProtectionEntity.setRefundTime(LocalDateTime.now());

		// 校验参数并设置参数
		firstLaunchOrderInfoService.vlidateDtoAndReset(orderProtectionEntity, dto);

		// DC 2025/3/27 15:33 如果退货退款则添加物流信息
		OrderLogisticsEntity realLogistics = firstLaunchOrderInfoService.shippingLogistic(dto,
				orderInfoEntity.getLotId(), orderProtectionEntity.getId());
		if (realLogistics != null) {
			if (StrUtil.isNotEmpty(realLogistics.getLogisticsNumber())) {
				orderProtectionEntity.setLogisticsNumber(realLogistics.getLogisticsNumber());
				if (StrUtil.isBlank(orderProtectionEntity.getLogisticsCompany())) {
					ExpressCompany expressCompany = logisticsService.autonumber(realLogistics.getLogisticsNumber());
					orderProtectionEntity.setLogisticsCompany(expressCompany.getName());
				}
			}
			if (realLogistics.getId() != null) {
				orderProtectionEntity.setOrderLogisticsId(realLogistics.getId());
			}
		}

		orderProtectionMapper.insert(orderProtectionEntity);

		return orderInfoEntity;
	}

	@NotNull
	private OrderProtectionEntity checkStatus(Long orderId) {
		// 判断是仅退款还是退货退款
		OrderProtectionEntity orderProtectionEntity = orderProtectionMapper
				.selectOne(new QueryWrapper<OrderProtectionEntity>().lambda()
						.eq(OrderProtectionEntity::getOrderId, orderId));
		if (ObjectUtil.isNull(orderProtectionEntity)) {
			throw OrderException.build(OrderBizErrorCodeEnum.FIRST_LAUNCH_NOT_FOUND);
		}
		Integer refundType = orderProtectionEntity.getRefundType();
		// 仅退款, 判断是否在申请状态即可 在申请状态可以 同意 拒绝 关闭订单
		if (refundType.equals(OrderEnum.RefundType.ONLY_REFUND.getType())) {
			if (!orderProtectionEntity.getProtectionStatus()
					.equals(OrderEnum.ProtectionStatus.APPLY_REFUND.getType())) {
				throw OrderException.build(OrderBizErrorCodeEnum.FIRST_LAUNCH_NOT_FOUND);
			}
			// 退货退款, 需判断卖家是否已收货 收到货后才能 同意 拒绝 关闭订单
		} else if (refundType.equals(OrderEnum.RefundType.RETURN_AND_REFUND.getType())) {
			// 收到货后才能同意退款申请
			if (!orderProtectionEntity.getProtectionStatus()
					.equals(OrderEnum.ProtectionStatus.SELLER_RECEIVED.getType())) {
				throw OrderException.build(OrderBizErrorCodeEnum.FIRST_LAUNCH_STATUS_MISMATCH);
			}
		}
		return orderProtectionEntity;
	}

	private void setRefundLogistics(AdminProtectionOrderVO vo, OrderLogisticsMapper orderLogisticsMapper) {
		if (StrUtil.isNotBlank(vo.getRefundLogisticsNumber())) {
			LambdaQueryWrapper<OrderLogisticsEntity> ew = new LambdaQueryWrapper<OrderLogisticsEntity>()
					.eq(OrderLogisticsEntity::getOrderId, vo.getId())
					.eq(OrderLogisticsEntity::getLogisticsNumber, vo.getRefundLogisticsNumber());
			OrderLogisticsEntity orderLogisticsEntity = orderLogisticsMapper.selectOne(ew);
			vo.setRefundLogistics(orderLogisticsEntity);
			boolean upState = logisticsInfoService.validateLogisticsSign(vo.getRefundLogisticsNumber(),
					String.valueOf(vo.getId()));
			vo.setRefundLogisticsStatus(upState);
		}
	}

	/**
	 * 根据订单号查询延迟交易记录
	 * 
	 * @param orderId 订单ID
	 * @return 延迟交易记录
	 */
	public OrderDelayTransEntity getOrderDelayTrans(Long orderId) {
		return orderDelayTransService.findByOrderId(orderId);
	}

	private void refunding(Long orderId,String hfSeqId) {
		log.info("退款处理 - 开始处理, orderId: {}", orderId);

		// 1. 查询订单信息
		OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
		if (ObjectUtil.isNull(orderInfoEntity)) {
			log.error("退款处理 - 订单不存在, orderId: {}", orderId);
			throw OrderException.build(OrderBizErrorCodeEnum.ORDER_NOT_EXIST);
		}

		// 2. 校验支付方式
		if (orderInfoEntity.getPayType() == OrderEnum.OrderPayType.OFFLINE_PAY.getType()) {
			log.error("退款处理 - 线下支付订单不能在线退款, orderId: {}", orderId);
			throw OrderException.build(OrderBizErrorCodeEnum.ORDER_PROTECT_ONLINE_ERROR);
		}

		// 3. 查询维权记录
		OrderProtectionEntity orderProtectionEntity = orderProtectionMapper
				.selectOne(new QueryWrapper<OrderProtectionEntity>().lambda()
						.eq(OrderProtectionEntity::getOrderId, orderId)
						.orderByDesc(OrderProtectionEntity::getCreateTime)
						.last("limit 1"));

		// 生成订单日志
		orderLogService.createOrderLog(orderInfoEntity, OrderLogTemplate.ORDER_PLATFORM_CONFIRM_REFUND);
		// 同意退款通知
		Map<String, Object> map = new HashMap<>();
		map.put("userId", orderInfoEntity.getUserId());
		map.put("msgScene", UserMsgConstants.AGREE_REFUND_NOTIFICATION);
		map.put("orderId", orderInfoEntity.getOrderNo());
		map.put("remark", OrderEnum.ProtectionStatus.REFUND_APPROVED.getDesc());
		map.put("orderAmount", orderInfoEntity.getAmount());
		remoteUserMsgService.sendUserMsg(map);
        

		// 4 更新已有维权记录状态
		log.info("退款处理 - 更新已有维权记录状态, orderId: {}, protectionStatus: {}", orderId,
				orderProtectionEntity.getProtectionStatus());
		Long userId = orderProtectionEntity.getUserId();
		orderProtectionEntity.setUpdateBy(userId.toString());
		orderProtectionEntity.setProtectionStatus(OrderEnum.ProtectionStatus.REFUND_IN_PROGRESS.getType());
		orderProtectionEntity.setRefundTime(LocalDateTime.now());
		orderProtectionEntity.setRefundSerio(hfSeqId);
		orderProtectionMapper.updateById(orderProtectionEntity);

		log.info("退款处理 - 处理完成, orderId: {}", orderId);
	}

	@Override
	public List<OrderProtectionExportVO> listProtectionByIds(OrderProtectionExportDTO dto) {
		QueryWrapper<AdminProtectionOrderVO> wrapper = new QueryWrapper<>();
		List<String> orderType = new ArrayList<>();
		orderType.add(OrderEnum.OrderType.FIRST_LAUNCH.getType());
		orderType.add(OrderEnum.OrderType.CONSIGNMENT.getType());
		orderType.add(OrderEnum.OrderType.STORE.getType());
		wrapper.in("a.order_type", orderType);
		if (ObjUtil.isNotEmpty(dto.getBeginTime()) && ObjUtil.isNotEmpty((dto.getEndTime()))) {
			wrapper.between("h.refund_time", dto.getBeginTime(), dto.getEndTime());
		}
		if (ObjUtil.isNotEmpty(dto.getQueryType())) {
			wrapper.like(firstLaunchOrderInfoService.getSearchColumn(dto.getQueryType()), dto.getQueryWords());
		}
		if (ObjUtil.isNotEmpty(dto.getProtectionStatus())) {
			if (dto.getProtectionStatus() == 1) {
				// 申请退款相关状态：1-申请退款 4-买家待退货 5-卖家待收货 6-卖家已收货
				wrapper.in("h.protection_status", Arrays.asList(OrderEnum.ProtectionStatus.APPLY_REFUND.getType(),
						OrderEnum.ProtectionStatus.BUYER_TO_RETURN.getType(),
						OrderEnum.ProtectionStatus.SELLER_TO_RECEIVE.getType(),
						OrderEnum.ProtectionStatus.REFUND_IN_PROGRESS.getType(),
						OrderEnum.ProtectionStatus.SELLER_RECEIVED.getType()));
			} else if (dto.getProtectionStatus() == 3) {
				// 退款成功相关状态：3-退款成功 8-部分退款
				wrapper.in("h.protection_status", Arrays.asList(OrderEnum.ProtectionStatus.REFUND_SUCCESS.getType(),
						OrderEnum.ProtectionStatus.PARTIAL_REFUND.getType()));
			} else {
				wrapper.eq("h.protection_status", dto.getProtectionStatus());
			}
		}

		if (ObjUtil.isNotEmpty(dto.getRefundType())) {
			wrapper.eq("h.refund_type", dto.getRefundType());
		}

//		// 商户查询自己的订单, 其他配置角色可以查询所有订单
		if (!firstLaunchOrderInfoService.getSelectRoleData()) {
			Long userId = SecurityUtils.getUser().getId();
			List<Long> merchantIds = baseRemoteService.getMerchantIdsBySysUserId(userId);
			if (ObjectUtil.isEmpty(merchantIds) || merchantIds.size() < 1)
				return new ArrayList<OrderProtectionExportVO>();
			wrapper.in("b.merchant_id", merchantIds);
		}
		wrapper.orderByDesc("h.create_time");

		return  orderProtectionMapper.listProtectionByIds(wrapper);
	}
}
