package com.yts.yyt.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yts.yyt.common.core.util.IDS;
import com.yts.yyt.order.api.entity.OrderInfoEntity;
import com.yts.yyt.order.api.entity.OrderLogEntity;
import com.yts.yyt.order.api.entity.OrderLogisticsEntity;
import com.yts.yyt.order.api.vo.OrderLogVO;
import com.yts.yyt.order.mapper.OrderLogMapper;
import com.yts.yyt.order.service.OrderLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * 订单操作日志
 *
 * <AUTHOR>
 * @date 2025-01-06 19:28:37
 */
@Service
@RequiredArgsConstructor
public class OrderLogServiceImpl extends ServiceImpl<OrderLogMapper, OrderLogEntity> implements OrderLogService {
    private final OrderLogMapper orderLogMapper;

    @Override
    public void createOrderLog(OrderInfoEntity orderInfo, String action) {
        OrderLogEntity orderLogEntity = new OrderLogEntity();
        orderLogEntity.setId(IDS.uniqueID());
        orderLogEntity.setOrderId(orderInfo.getId());
        orderLogEntity.setAction(action);
        orderLogEntity.setUid(orderInfo.getUserId());
        orderLogEntity.setOrderStatus(orderInfo.getStatus());
        save(orderLogEntity);
    }

    @Override
    public List<OrderLogEntity> listByOrderId(Long orderId) {
        LambdaQueryWrapper<OrderLogEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderLogEntity::getOrderId, orderId);
        return orderLogMapper.selectList(queryWrapper);
    }
}