package com.yts.yyt.order.controller;

import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.security.annotation.Inner;
import com.yts.yyt.common.swagger.base.BaseController;
import com.yts.yyt.order.api.entity.OrderInfoEntity;
import com.yts.yyt.order.api.enums.OrderEnum;
import com.yts.yyt.order.api.feign.RemoteFirstLaunchOrderService;
import com.yts.yyt.order.api.vo.OrderCountVO;
import com.yts.yyt.order.api.vo.OrderStatVO;
import com.yts.yyt.order.mapper.OrderInfoMapper;
import com.yts.yyt.order.service.FirstLaunchOrderInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 订单信息内部调用控制层
 */
@RestController
@RequestMapping("/first/launch")
@Tag(description = "orderInfoInner", name = "订单信息内部调用")
@RequiredArgsConstructor
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class OrderInfoInnerController extends BaseController implements RemoteFirstLaunchOrderService {

    private final FirstLaunchOrderInfoService firstLaunchOrderInfoService;
    
    /**
     * 功能 获取自提订单列表
     * 创建于 2025/3/12 09:48
     * <AUTHOR>
     * @param limit 获取数量
     * @param orderStatus 订单状态
     * @param payTime 订单支付时间
     * @return R<List<OrderInfoEntity>>
     */
    @Operation(summary = "获取自提订单列表" , description = "获取自提订单列表" )
    @GetMapping("/getPickupOrderList/{limit}/{orderStatus}/{payTime}" )
    @Inner
    @Override
    public R<List<OrderInfoEntity>> getPickupOrderList(@PathVariable Integer limit, @PathVariable String orderStatus, @PathVariable String payTime){
        return R.ok(firstLaunchOrderInfoService.getPickupOrderList(limit,orderStatus,payTime));
    }

    @Operation(summary = "订单统计" , description = "订单统计" )
    @PostMapping("/countOrders/{userId}")
    @Inner
    @Override
    public R<OrderCountVO> countOrders(@RequestBody @PathVariable("userId") Long  userId) {
        OrderCountVO orderCountVO = ((OrderInfoMapper)firstLaunchOrderInfoService.getBaseMapper()).countOrdersByUserId(userId);
        
        // 如果查询结果为空，创建一个新的空对象
        if (orderCountVO == null) {
            orderCountVO = new OrderCountVO();
            orderCountVO.setTotalCount(0L);
            orderCountVO.setWaitingPaymentCount(0L);
            orderCountVO.setWaitingDeliveryCount(0L);
            orderCountVO.setShippedCount(0L);
            orderCountVO.setRefundingCount(0L);
        }
        
        // 单独查询退款/售后订单数
        orderCountVO.setRefundingCount(firstLaunchOrderInfoService.lambdaQuery()
                .eq(OrderInfoEntity::getUserId, userId)
                .eq(OrderInfoEntity::getProtectStatus, OrderEnum.OrderProtectStatus.REFUND_IN_PROGRESS.getType())
                .count());
        
        return R.ok(orderCountVO);
    }

    /**
     * 功能 通过用户列表获取用户订单交易量及交易额
     * 创建于 2025/3/6 11:39
     * <AUTHOR>
     * @param userIds 用户ID
     * @param orderStatus 订单状态
     * @return List<OrderStatVO>
     */
    @Operation(summary = "通过用户列表获取用户订单交易量及交易额" , description = "通过用户列表获取用户订单交易量及交易额" )
    @GetMapping("/getOrderStat" )
    @Inner
    @Override
    public R<List<OrderStatVO>> getOrderStat(@RequestParam("userIds") List<Long> userIds, @RequestParam("orderStatus") String orderStatus){
        return R.ok(firstLaunchOrderInfoService.getUserOrderStat(userIds,orderStatus));
    }
}
