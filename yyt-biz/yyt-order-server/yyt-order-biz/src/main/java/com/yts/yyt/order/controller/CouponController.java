package com.yts.yyt.order.controller;

import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.log.annotation.SysLog;
import com.yts.yyt.common.security.annotation.HasPermission;
import com.yts.yyt.common.security.annotation.Inner;
import com.yts.yyt.order.api.dto.coupon.CouponCreateDTO;
import com.yts.yyt.order.api.dto.coupon.CouponEditDTO;
import com.yts.yyt.order.api.dto.coupon.CouponListDTO;
import com.yts.yyt.order.api.dto.coupon.CouponStatusDTO;
import com.yts.yyt.order.api.vo.CouponVO;
import com.yts.yyt.order.service.CouponService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

/**
 * 优惠券管理
 *
 * <AUTHOR>
 * @date 2024-02-13
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/admin/coupon")
@Tag(description = "优惠券管理", name = "优惠券管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class CouponController {

    private final CouponService couponService;

    /**
     * 优惠券列表
     * 
     * @param listDTO 查询参数CouponListDTO
     * @return R
     */
    @Operation(summary = "优惠券列表", description = "后台管理优惠券列表查询")
    @PostMapping("/list")
    @HasPermission("coupon_view")
    public R<Page<CouponVO>> list(@RequestBody CouponListDTO listDTO) {
        return R.ok(couponService.getCouponPage(listDTO));
    }

    /**
     * 创建优惠券
     * 
     * @param createDTO 创建参数
     * @return R
     */
    @Operation(summary = "创建优惠券", description = "后台管理创建优惠券")
    @SysLog("创建优惠券")
    @PostMapping("/create")
    @HasPermission("coupon_add")
    public R<Boolean> create(@RequestBody @Validated CouponCreateDTO createDTO) {
        return R.ok(couponService.createTemplate(createDTO));
    }

    /**
     * 编辑优惠券
     * 
     * @param editDTO 编辑参数
     * @return R
     */
    @Operation(summary = "编辑优惠券", description = "后台管理编辑优惠券")
    @SysLog("编辑优惠券")
    @PostMapping("/edit")
    @HasPermission("coupon_edit")
    public R<Boolean> edit(@RequestBody @Validated CouponEditDTO editDTO) {
        return R.ok(couponService.editTemplate(editDTO));
    }

    /**
     * 优惠券启用/禁用
     * 
     * @param statusDTO 状态参数
     * @return R
     */
    @Operation(summary = "优惠券启用/禁用", description = "后台管理优惠券启用/禁用")
    @SysLog("优惠券启用/禁用")
    @PostMapping("/status")
    @HasPermission("coupon_status")
    public R<Boolean> status(@RequestBody CouponStatusDTO statusDTO) {
        return R.ok(couponService.updateStatus(statusDTO));
    }

    /**
     * 删除优惠券
     * 
     * @param id 优惠券ID
     * @return R
     */
    @Operation(summary = "删除优惠券", description = "后台管理删除优惠券")
    @SysLog("删除优惠券")
    @PostMapping("/delete/{id}")
    @HasPermission("coupon_del")
    public R<Boolean> delete(@PathVariable Long id) {
        return R.ok(couponService.deleteTemplate(id));
    }
}
