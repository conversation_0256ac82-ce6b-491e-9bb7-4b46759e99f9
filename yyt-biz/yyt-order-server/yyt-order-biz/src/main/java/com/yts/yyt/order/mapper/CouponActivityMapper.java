package com.yts.yyt.order.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yts.yyt.order.api.entity.CouponActivityEntity;

/**
 * 优惠券活动Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-13
 */
@Mapper
public interface CouponActivityMapper extends BaseMapper<CouponActivityEntity> {
    
    /**
     * 根据关键字查询触发类型名称列表
     *
     * @param keyword 关键字
     * @return 触发类型名称列表
     */
    @Select("SELECT name FROM coupon_trigger_type WHERE name LIKE CONCAT('%', #{keyword}, '%') AND del_flag = '0'")
    List<String> selectNamesByKeyword(String keyword);
} 
