package com.yts.yyt.order.mapper;

import com.yts.yyt.common.data.datascope.YytBaseMapper;
import com.yts.yyt.order.api.entity.OrderLogEntity;
import com.yts.yyt.order.api.vo.OrderLogVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface OrderLogMapper extends YytBaseMapper<OrderLogEntity> {

	@Select("select * from order_log where order_id = #{orderId} order by create_time desc ")
	List<OrderLogVO> firstLaunchOrderLog(@Param("orderId")Long orderId);
}
