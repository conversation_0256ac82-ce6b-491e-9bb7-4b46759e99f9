//package com.yts.yyt.order.service;
//
//import com.alibaba.fastjson.JSONObject;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import com.yts.yyt.order.api.dto.AuctionApplyAcceptancePassDTO;
//import com.yts.yyt.order.api.dto.AuctionApplyPageQueryDTO;
//import com.yts.yyt.order.api.dto.AuctionApplyShippingDTO;
//import com.yts.yyt.order.api.dto.AuctionApplySubmitDTO;
//import com.yts.yyt.order.api.vo.AuctionApplyVO;
//import com.yts.yyt.order.service.impl.AuctionApplyServiceImpl;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import java.math.BigDecimal;
//import java.time.LocalDateTime;
//import java.util.Arrays;
//
//@Slf4j
//public class AuctionApplyServiceTest extends BaseSpringTest {
//
//    @Autowired
//    private AuctionApplyServiceImpl auctionApplyService;
//
//    @Test
//    public void testSubmit() {
//        auctionApplyService.getById(111L);
//    }
//
//    @Test
//    public void testShipping() {
//        AuctionApplyShippingDTO shippingDTO = new AuctionApplyShippingDTO();
//        shippingDTO.setApplyIds(Arrays.asList(1662212883725201408L));
//        shippingDTO.setLogisticsType(2);
//        shippingDTO.setCargo("测试货物");
//        shippingDTO.setWeight(new BigDecimal("1.5"));
//        shippingDTO.setRemark("测试备注");
//        shippingDTO.setDayType("今天");
//        shippingDTO.setReserveBeginTime(LocalDateTime.of(2025, 2, 26, 18, 0, 0));
//        shippingDTO.setReserveEndTime(LocalDateTime.of(2025, 2, 26, 19, 0, 0));
//        auctionApplyService.shipping(shippingDTO);
//        log.info("发货完成");
//    }
//
//    @Test
//    public void testAcceptancePass() {
//        AuctionApplyAcceptancePassDTO passDTO = new AuctionApplyAcceptancePassDTO();
//        passDTO.setApplyId(1662212883725201408L);
//        passDTO.setGoodsId(1655344292444839936L);
//        passDTO.setMinAuctionPrice(new BigDecimal("100"));
//        passDTO.setMaxAuctionPrice(new BigDecimal("1000"));
//        passDTO.setReceiptCode("184774");
//
//        auctionApplyService.acceptancePass(passDTO);
//        log.info("验收通过");
//    }
//
//    @Test
//    public void testPublish() {
//        AuctionApplyAcceptancePassDTO publishDTO = new AuctionApplyAcceptancePassDTO();
//        publishDTO.setApplyId(1662212883725201408L);
//
//        auctionApplyService.publish(publishDTO);
//        log.info("发布竞拍完成");
//    }
//
//    @Test
//    public void testFullProcess() {
//        String[] applyImg = { "img1.jpg", "img2.jpg" };
//        // 1. 提交申请
//        AuctionApplySubmitDTO submitDTO = new AuctionApplySubmitDTO();
//        submitDTO.setUserId(1L);
//        submitDTO.setGoodsTypeId(1L);
//        submitDTO.setApplyImg(applyImg);
//        submitDTO.setApplyName("测试商品");
//        submitDTO.setPlaceOfOrigin("中国");
//
//        AuctionApplyVO applyVO = auctionApplyService.submit(submitDTO);
//        log.info("提交申请结果：{}", applyVO);
//
//        // 2. 发货
//        AuctionApplyShippingDTO shippingDTO = new AuctionApplyShippingDTO();
//        shippingDTO.setApplyIds(Arrays.asList(applyVO.getId()));
//        shippingDTO.setLogisticsType(0);
//        shippingDTO.setCargo("测试货物");
//        shippingDTO.setWeight(new BigDecimal("1.5"));
//        shippingDTO.setRemark("测试备注");
//        shippingDTO.setDayType("明天");
//        shippingDTO.setReserveBeginTime(LocalDateTime.now());
//        shippingDTO.setReserveEndTime(LocalDateTime.now().plusDays(1));
//        auctionApplyService.shipping(shippingDTO);
//        log.info("发货完成");
//
//        // 3. 验收通过
//        AuctionApplyAcceptancePassDTO passDTO = new AuctionApplyAcceptancePassDTO();
//        passDTO.setApplyId(applyVO.getId());
//        passDTO.setGoodsId(1L);
//        passDTO.setMinAuctionPrice(new BigDecimal("100"));
//        passDTO.setMaxAuctionPrice(new BigDecimal("1000"));
//
//        auctionApplyService.acceptancePass(passDTO);
//        log.info("验收通过");
//
//        // 4. 发布竞拍
//        auctionApplyService.publish(passDTO);
//        log.info("发布竞拍完成");
//    }
//
//
//    @Test
//    public void pageByUser() {
//    	Page page = new Page<>(1, 10);
//    	AuctionApplyPageQueryDTO queryDTO = new AuctionApplyPageQueryDTO();
//    	queryDTO.setUserId(1739849572000119L);
////    	queryDTO.setStartDate(LocalDate.of(2025,02,23));
////    	queryDTO.setEndDate(LocalDate.of(2025,02,23));
//    	queryDTO.setStartDate("2025-02-23");
//    	queryDTO.setEndDate("2025-02-23");
//    	queryDTO.setAuctionSubStatus("auction_sub_canceled");
//        Page<AuctionApplyVO> pageByUser = auctionApplyService.pageByUser(page, queryDTO);
//
//        System.out.println(JSONObject.toJSONString(pageByUser));
//    }
//
//
//
//}