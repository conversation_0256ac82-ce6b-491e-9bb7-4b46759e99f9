package com.yts.yyt.order.controller;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import org.junit.Before;
import org.junit.runner.RunWith;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;
import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;

@RunWith(SpringRunner.class)
@SpringBootTest
@AutoConfigureMockMvc
public class BaseSpringOauthTest {

    public static final Long mockUserId = 999L;

    @Autowired
    protected MockMvc mockMvc;

    @Autowired
    protected WebApplicationContext context;

    static {
        // 静态初始化程序代码将在初始化spring应用程序上下文之前执行
        System.setProperty("PROFILES_ACTIVE", "cloud");
        System.setProperty("NACOS_NAMESPACE", "public");
    }


    @Before
    public void setUp(){


        mockMvc= MockMvcBuilders
                .webAppContextSetup(context)
                // 添加spring-security的验证
                .apply(springSecurity())
                .build();
    }
}
