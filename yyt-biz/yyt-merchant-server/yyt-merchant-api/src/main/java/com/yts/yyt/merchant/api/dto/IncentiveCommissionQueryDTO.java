package com.yts.yyt.merchant.api.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@Schema(description = "佣金结算表")
public class IncentiveCommissionQueryDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 角色类型：见字典commossion_role_type
     */
    @Schema(description="角色类型(1商户 2合伙人)：见字典commossion_role_type")
    private String roleType;

    /**
     * 角色对应用户ID
     */
    @Schema(description="角色对应用户ID")
    private List<Long> roleUid;

    /**
     * 合伙人区域城市
     */
    @Schema(description="合伙人区域城市")
    private String partnerArea;

    /**
     * 状态：见字典incentive_commission_status
     * @see com.yts.yyt.merchant.api.enums.IncentiveCommissionStatusEnum
     */
    @Schema(description="状态：见字典incentive_commission_status")
    private String status;

    /**
     * 开始时间
     */
    @Schema(description="开始时间: 00-00-00 00:00:00")
    private String startTime;
    
    /**
     * 结束时间
     */
    @Schema(description="结束时间: 00-00-00 23:59:59")
    private String endTime;

    /**
     * 合伙人名称/商户名称
     */
    @Schema(description="合伙人名称/商户名称")
    private String partnerOrMerchantName;

    /**
     * 交易流水号
     */
    @Schema(description="交易流水号")
    private String tradeSerialNo;

    /**
     * 是否传发票：见字典incentive_commission_Invoice
     * @see com.yts.yyt.merchant.api.enums.IncentiveCommissionInvoiceEnum
     */
    @Schema(description="是否传发票，见字典incentive_commission_Invoice")
    private Integer isInvoice;

    /**
     * 发票关联号
     */
    @Schema(description="发票关联号")
    private String invoiceNo;

    /**
     * 统计周期：week month
     */
    @Schema(description="统计周期：week month")
    private String reportCycle;

    /**
     * 统计日期：比如周统计202516（2025年第16周），月统计202504
     */
    @Schema(description="统计日期：比如周统计202516（2025年第16周），月统计202504")
    private String reportDate;

    /**
     * 合伙人名称
     */
    @Schema(description="合伙人名称/手机号")
    private String partnerNameOrPhone;

    /**
     * 角色id
     */
    @Schema(description="角色id")
    private Long roleId;

}

