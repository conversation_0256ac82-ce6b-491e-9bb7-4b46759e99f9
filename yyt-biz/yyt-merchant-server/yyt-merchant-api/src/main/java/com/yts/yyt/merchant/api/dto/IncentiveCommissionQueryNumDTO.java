package com.yts.yyt.merchant.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(description = "佣金结算表")
public class IncentiveCommissionQueryNumDTO implements Serializable {

    /**
     * 角色id
     */
    @Schema(description="角色id")
    private Long roleId;

    /**
     * 开始时间
     */
    @Schema(description="开始时间: 00-00-00 00:00:00")
    private String startTime;

    /**
     * 结束时间
     */
    @Schema(description="结束时间: 00-00-00 23:59:59")
    private String endTime;

    /**
     * 发票关联号
     */
    @Schema(description="发票关联号")
    private String invoiceNo;


    /**
     * 交易流水号
     */
    @Schema(description="交易流水号")
    private String tradeSerialNo;


    /**
     * 合伙人名称
     */
    @Schema(description="合伙人名称/手机号")
    private String partnerNameOrPhone;

    /**
     * userId
     */
    @Schema(description="合伙人id")
    private Long userId;

}
