package com.yts.yyt.merchant.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 佣金角色类型
 */
@AllArgsConstructor
@Getter
public enum CommossionRoleTypeEnum {
	ROLE_TYPE1("1","商户"),
	ROLE_TYPE2("2","合伙人"),
	;

	private final String type;
	private final String desc;

	// 根据type获取desc
	public static String getDescByType(String type) {
		for (CommossionRoleTypeEnum e : values()) {
			if (e.getType().equals(type)) {
				return e.getDesc();
			}
		}
		return null;
	}

	@Override
	public String toString() {
		return type + " = " + desc;
	}
}
