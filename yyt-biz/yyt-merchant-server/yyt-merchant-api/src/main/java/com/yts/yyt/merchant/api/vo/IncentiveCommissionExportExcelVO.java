package com.yts.yyt.merchant.api.vo;


import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Schema(description = "佣金结算导出Excel实体")
@Accessors(chain = true)
public class IncentiveCommissionExportExcelVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty("结算ID")
    private String id;

    @ExcelProperty("用户类型")
    private String roleType;

    @ExcelProperty("合伙人ID/商家ID")
    private String roleIdAndMerchantId;

    @ExcelProperty("结算周期")
    private String cycle;


    @ExcelProperty( "基础佣金(元)")
    private String commissionBase;




    /**
     * 激励总金额（元）
     */
    @ExcelProperty("激励总金额（元）")
    private String commissionTotal;


    @ExcelProperty("状态")
    private String status;

    @ExcelProperty("发放时间")
    private String grantTime;


    @ExcelProperty("收款信息")
    private String account;

    /**
     * 交易流水号
     */
    @ExcelProperty("交易流水号")
    private String tradeSerialNo;


}
