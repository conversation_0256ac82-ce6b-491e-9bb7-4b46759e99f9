package com.yts.yyt.merchant.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@Schema(description = "商户佣金明细")
public class MerchantCommissionListVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "周期开始时间")
    private String cycleStart;

    @Schema(description = "周期结束时间")
    private String cycleEnd;

    @Schema(description = "统计日期：比如周统计202516（2025年第16周），月统计202504")
    private String reportDate;

    @Schema(description = "统计周期：week month")
    private String reportCycle;

    @Schema(description = "商户名称")
    private String shopName;

    @Schema(description = "联系手机")
    private String contactPhone;

    @Schema(description = "商户id")
    private Long merchantId;

    @Schema(description = "基础佣金")
    private BigDecimal commissionBase;

    @Schema(description = "激励总金额")
    private BigDecimal commissionTotal;

    @Schema(description = "专项激励")
    private BigDecimal commissionSpecial;

    @Schema(description = "高货佣金")
    private BigDecimal highCommission;

    @Schema(description = "小精品佣金")
    private BigDecimal mediumCommission;

    @Schema(description = "普货佣金")
    private BigDecimal normalCommission;

    @Schema(description = "持卡人姓名")
    private String cardholderName;

    @Schema(description = "银行名称")
    private String bankName;

    @Schema(description = "分行名称")
    private String branchName;

    @Schema(description = "银行卡号")
    private String cardNumber;

}