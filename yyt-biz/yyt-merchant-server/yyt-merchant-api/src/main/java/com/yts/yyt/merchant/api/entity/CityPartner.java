package com.yts.yyt.merchant.api.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yts.yyt.common.core.entity.BaseEntity;
import com.yts.yyt.common.core.validation.UrlValidation;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@TableName("city_partner")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "城市合伙人")
public class CityPartner extends BaseEntity<CityPartner> {

        /**
     * 合伙人名称
     */
    @Schema(description="合伙人名称")
	@NotBlank(message = "合伙人名称不能为空")
    private String partnerName;
        /**
     * 统一社会信用代码
     */
    @Schema(description="统一社会信用代码")
    private String unifiedSocialCreditCode;
        /**
     * 企业注册地址
     */
    @Schema(description="企业注册地址")
    private String enterpriseRegistryAddr;
        /**
     * 注册资本
     */
    @Schema(description="注册资本")
    private BigDecimal registeredCapital;
        /**
     * 经营范围
     */
    @Schema(description="经营范围")
    private String businessScope;
        /**
     * 营业执照扫描件
     */
    @Schema(description="营业执照扫描件")
    @UrlValidation
    private String businessLicenseScan;
        /**
     * 本地资源简述
     */
    @Schema(description="本地资源简述")
    private String localResources;
        /**
     * 联系人
     */
    @Schema(description="联系人")
	@NotBlank(message = "联系人不能为空")
    private String contactPerson;
        /**
     * 联系电话
     */
    @Schema(description="联系电话")
	@NotBlank(message = "联系电话不能为空")
    private String contactPhone;
	/**
     * 身份证号
     */
    @Schema(description="身份证号")
    private String idCard;
        /**
     * 电子邮箱
     */
    @Schema(description="电子邮箱")
    private String email;
        /**
     * 所属区域
     */
    @Schema(description="所属区域")
    private String region;
        /**
     * 合伙人账号
     */
    @Schema(description="合伙人账号")
    private String partnerAccount;
        /**
     * 合作状态
     */
    @Schema(description="合作状态")
    private String status;
	/**
	 * 邀请码
	 */
	@Schema(description = "邀请码")
	private String inviteNo;
	/**
	 * 合伙人编号
	 */
	@Schema(description = "合伙人编号")
	private Integer partnerNo;
	/**
	 * 移动端用户id
	 */
	@Schema(description = "移动端用户id")
	private Long userId;
	/**
	 * 系统用户id
	 */
	@Schema(description = "系统用户id")
	private Long sysUserId;
	/**
	 * 备注
	 */
	@Schema(description = "remark")
	private String remark;

	/**
	 * 渠道经理系统用户id
	 */
	@Schema(description = "渠道经理系统用户id")
	@TableField(exist = false)
	private Long channelManagerSysUserId;

}

