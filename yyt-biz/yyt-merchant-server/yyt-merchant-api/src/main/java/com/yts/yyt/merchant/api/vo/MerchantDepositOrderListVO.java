package com.yts.yyt.merchant.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Schema(description = "消保金订单列表返回对象")
public class MerchantDepositOrderListVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    @Schema(description = "订单ID")
    private Long id;

    /**
     * 店铺信息
     */
    @Schema(description = "店铺头像")
    private String shopAvatar;
    /**
     * 店铺名称
     */
    @Schema(description = "店铺名称")
    private String shopName;
    /**
     * 店铺联系人电话
     */
    @Schema(description = "店铺联系人电话")
    private String contactPhone;

    /**
     * 店铺类型
     */
    @Schema(description = "店铺类型")
    private String shopType;

    /**
     * 店铺类型描述
     */
    @Schema(description = "店铺类型描述")
    private String shopTypeDesc;

    /**
     * 消保金额
     */
    @Schema(description = "消保金额")
    private BigDecimal amount;

    /**
     * 店铺权益
     */
    @Schema(description = "店铺权益")
    private String benefitLevelName;

    /**
     * 支付渠道
     */
    @Schema(description = "支付渠道")
    private String payChannel;

    /**
     * 支付渠道描述
     */
    @Schema(description = "支付渠道描述")
    private String payChannelDesc;

    /**
     * 消保金类型
     */
    @Schema(description = "消保金类型")
    private String depositType;

    /**
     * 消保金类型描述
     */
    @Schema(description = "消保金类型描述")
    private String depositTypeDesc;

    /**
     * 消保金缴纳时间
     */
    @Schema(description = "消保金缴纳时间")
    private String payTime;

    /**
     * 消保金支付状态
     */
    @Schema(description = "消保金支付状态")
    private String payStatus;

    /**
     * 消保金支付状态描述
     */
    @Schema(description = "消保金支付状态描述")
    private String payStatusDesc;
}