package com.yts.yyt.merchant.api.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@Schema(description = "商家信息表")
public class MerchantDetailVO {

	@Schema(description = "店铺id")
	private Long id;

	@Schema(description = "移动端用户id")
	private Long userId;

	/**
	 * 商家企业名称
	 */
	@Schema(description = "商家企业名称")
	private String merchantEnterpriseName;
	/**
	 * 统一社会信用代码
	 */
	@Schema(description = "统一社会信用代码")
	private String unifiedSocialCreditCode;
	/**
	 * 店铺名
	 */
	@Schema(description = "店铺名")
	private String shopName;
	/**
	 * 店铺类型
	 */
	@Schema(description = "店铺类型 字典: merchant_shop_type")
	private String shopType;
	/**
	 * 联系人姓名
	 */
	@Schema(description = "联系人姓名")
	private String contactPersonName;
	/**
	 * 联系电话
	 */
	@Schema(description = "联系电话")
	private String contactPhone;
	/**
	 * 注册时间
	 */
	@Schema(description = "注册时间")
	private LocalDateTime registrationTime;
	/**
	 * 所在省份
	 */
	@Schema(description = "所在省份")
	private String province;
	/**
	 * 所在城市
	 */
	@Schema(description = "所在城市")
	private String city;
	/**
	 * 店铺头像
	 */
	@Schema(description = "店铺头像")
	private String shopAvatar;
	/**
	 * 店铺介绍
	 */
	@Schema(description = "店铺介绍")
	private String shopIntroduction;
	/**
	 * 退货地址
	 */
	@Schema(description = "退货地址")
	private String returnAddress;
	/**
	 * 营业执照
	 */
	@Schema(description = "营业执照")
	private String businessLicense;
	/**
	 * 备注
	 */
	@Schema(description = "备注")
	private String remark;

	@Schema(description = "审核状态 见字典: merchant_audit_state")
	private Integer auditState;

	@Schema(description = "审核原因")
	private String auditReason;

	@Schema(description = "商家类型 转售商家还是合伙人添加的商家 字典: merchant_type")
	private String merchantType;

	@Schema(description = "商家权益等级字典id")
	private String benefitLevel;

	@Schema(description = "商家权益等级")
	private String benefitLevelName;

	@Schema(description = "商家权益等级logo")
	private String benefitLevelLogo;

	@Schema(description = "汇付id")
	private String huifuId;

}