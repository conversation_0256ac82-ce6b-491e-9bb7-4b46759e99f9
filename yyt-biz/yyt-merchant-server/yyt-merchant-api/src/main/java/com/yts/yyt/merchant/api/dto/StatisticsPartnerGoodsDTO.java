package com.yts.yyt.merchant.api.dto;

import com.yts.yyt.common.core.entity.BasePage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 合伙人藏品统计查询DTO
 *
 */
@Data
@Schema(description = "合伙人藏品统计查询参数")
public class StatisticsPartnerGoodsDTO extends BasePage {

	/**
	 * 合伙人ID
	 */
	@Schema(description = "合伙人ID")
	private Long partnerId;

	/**
	 * 合伙人名称
	 */
	@Schema(description = "合伙人名称")
	private String partnerName;

	/**
	 * 区域ID
	 */
	@Schema(description = "区域ID")
	private Long region;

	/**
	 * 日期，格式：yyyy-MM
	 */
	@Schema(description = "日期，格式：yyyy-MM")
	private String date;

	/**
	 * 年份
	 */
	@Schema(description = "年份")
	private String year;

	/**
	 * 月份
	 */
	@Schema(description = "月份")
	private String month;
	
	/**
	 * 日期
	 */
	@Schema(description = "日期")
	private String day;
}