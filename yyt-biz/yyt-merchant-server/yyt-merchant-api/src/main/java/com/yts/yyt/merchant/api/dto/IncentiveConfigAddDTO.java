package com.yts.yyt.merchant.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(description = "激励配置表")
public class IncentiveConfigAddDTO implements Serializable {

    /**
     * 佣金金额（元）
     */
    @Schema(description="佣金金额（元）")
    private String commission;
    /**
     * 配置名称
     */
    @Schema(description="配置名称")
    private String confTitle;

    /**
     * 配置类型：（见字典incentive_conf_type）
     * @see com.yts.yyt.merchant.api.enums.IncentiveConfTypeEnum
     */
    @Schema(description="配置类型：（见字典incentive_conf_type）")
    private String confType;

    /**
     * 条件最大值
     */
    @Schema(description="条件最大值")
    private String maxVal;

    /**
     * 商户比例
     */
    @Schema(description="商户比例")
    private String merchantRate;

    /**
     * 条件值（单条件）
     */
    @Schema(description="条件值（单条件）")
    private String middleVal;

    /**
     * 条件最小值
     */
    @Schema(description="条件最小值")
    private String minVal;

    /**
     * 合伙人比例
     */
    @Schema(description="合伙人比例")
    private String partnerRate;

    /**
     * 备注信息
     */
    @Schema(description="备注信息")
    private String remarks;

}

