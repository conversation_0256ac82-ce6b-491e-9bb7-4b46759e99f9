package com.yts.yyt.merchant.api.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(description = "佣金结算表")
public class IncentiveCommissionAddDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 基础佣金（元）
     */
    @Schema(description="基础佣金（元）")
    private BigDecimal commissionBase;
    
    /**
     * 阶梯冲刺奖励（元
     */
    @Schema(description="阶梯冲刺奖励（元")
    private BigDecimal commissionLadder;
    
    /**
     * 销售佣金（元）
     */
    @Schema(description="销售佣金（元）")
    private BigDecimal commissionSale;
    
    /**
     * 专项激励（元）
     */
    @Schema(description="专项激励（元）")
    private BigDecimal commissionSpecial;

    /**
     * 激励总金额（元）
     */
    @Schema(description="激励总金额（元）")
    private BigDecimal commissionTotal;
    
    /**
     * 配置实例化
     */
    @Schema(description="配置实例化")
    private String confInstance;
    
    /**
     * 周期结束时间
     */
    @Schema(description="周期结束时间")
    private LocalDateTime cycleEnd;
    
    /**
     * 周期开始时间
     */
    @Schema(description="周期开始时间")
    private LocalDateTime cycleStart;

    /**
     * 角色类型：见字典commossion_role_type
     */
    @Schema(description="角色类型：见字典commossion_role_type")
    private String roleType;
    
    /**
     * 角色对应用户ID
     */
    @Schema(description="角色对应用户ID")
    private Long roleUid;

    /**
     * 备注信息
     */
    @Schema(description="备注信息")
    private String remarks;

}

