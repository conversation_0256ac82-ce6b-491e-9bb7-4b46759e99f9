package com.yts.yyt.merchant.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
@Data
@Schema(description = "佣金结算表")
public class PartnerIncentiveCommissionQueryDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 角色对应用户ID-唯一
     */
    @Schema(description="角色对应用户ID")
    private Long roleId;

    /**
     * 发票关联号
     */
    @Schema(description="发票关联号")
    private String invoiceNo;


    /**
     * 合伙人名称
     */
    @Schema(description="合伙人名称/手机号")
    private String partnerNameOrPhone;


    /**
     * 开始时间
     */
    @Schema(description="开始时间: 00-00-00 00:00:00")
    private String startTime;

    /**
     * 结束时间
     */
    @Schema(description="结束时间: 00-00-00 23:59:59")
    private String endTime;

    /**
     * 结束时间
     */
    @Schema(description="状态: 0 未发放,1 已发放")
    private Integer status;

    /**
     * 结束时间
     */
    @Schema(description="状态: 0 未上传,1 已上传")
    private Integer isInvoice;

    /**
     * 交易流水号
     */
    @Schema(description="交易流水号")
    private String tradeSerialNo;


}
