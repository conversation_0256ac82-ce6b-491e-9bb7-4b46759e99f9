package com.yts.yyt.merchant.api.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MerchantErrorEnum {

    OPERATION_FAIL(90000, "操作失败"),
    REMOTE_CALL_ERROR(90001, "远程调用失败"),
    MQ_SEND_ERROR(90002, "MQ发送失败"),
	NOT_CONFIG_GOODS_DICT_ERROR(90007, "未配置藏品导入规则，请联系管理员"),
	PARTNER_DELETE_ERROR(90006, "存在商户信息，不可删除"),
	SEND_MOBILE_MESSAGE_ERROR(90004, "手机短信发送失败"),

    RECORD_NOT_EXIST_ERROR(90003, "记录不存在"),
    PARAMS_ERROR(90002, "参数异常"),
    PHONE_REPEAT_ERROR(90001, "手机号重复"),
    CODE_ERROR(90005, "验证码错误,请重新输入"),
	PROVINCE_CITY_EXIST(90006, "省份/城市,已存在"),
    ROLE_ACCOUNT_NOT_EXIST(90007, "账户不存在"),
    IS_INC_NOT_EXIST(90008, "未知的收支类型"),
    COMMISSION_BALANCE_NOT_EXIST(90009, "佣金余额不足"),
    CITY_PARTNER_REGION_EXIST(90010, "该区域已存在合伙人,请重新选择"),
    ADD_MERCHANT_ERROR(90011, "创建失败,请重新输入"),
    NOT_DISABLE_ONESELF(90012, "不能禁用本人账户"),
    DELETE_AREA_ERROR(90013, "该区域已有合伙人,请解绑后删除"),
    EXIST_BANK_INFO(90014, "用户只能创建一张银行卡信息"),
    INCENTIVE_COMMISSION_NOT_EXIST(90015, "账单信息不存在"),
    MERCHANT_BANK_INFO_NOT_EXIST(90016, "银行卡信息不存在"),
    INCENTIVE_COMMISSION_LOG_EXIST(90017, "账单已付款登记，不可重复登记"),
    DEPOSIT_ORDER_ERROR(90018, "缴纳消保金下单失败"),
    DEPOSIT_CONFIG_NOT_EXIST(90019, "消保金权益配置不存在"),
    DEPOSIT_PAY_AMOUNT_ERROR(90020, "消保金缴纳金额与配置不匹配"),
    ONLY_PERSONAL_MERCHANT_NEED_REVIEW(90021, "只有个人商家才需要审核"),
    ONLY_REJECTED_MERCHANT_CAN_EDIT(90022, "只有审核被拒绝的商家才能重新编辑"),
    MERCHANT_ALREADY_EXISTS(90023, "您已有店铺，不能重复入驻"),
    NOT_PARTNER(90024, "当前登录用户不是合伙人账号,不能新增商家"),
    DEPOSIT_ORDER_NOT_EXIST(90025, "消保金订单不存在"),
    MERCHANT_NOT_EXIST(90026, "商户不存在"),
    SET_ROLE_ERROR(90027, "设置角色失败"),
    CREATE_SYSTEM_USER_ERROR(90028, "创建系统用户失败"),
    QUERY_USER_INFO_ERROR(90029, "查询用户信息失败"),
    CONTACT_PHONE_EXIST_ERROR(90030, "联系电话已被其他合伙人占用"),
    GET_SYSTEM_PARAM_ERROR(90031, "获取系统参数失败"),
    GET_SYSTEM_ROLE_ERROR(90032, "查询角色失败"),
    ORDER_SERVICE_ERROR(90033, "订单调用失败"),
    LOCK_SYSUSER_ERROR(90034, "锁定系统用户失败"),
    UNLOCK_SYSUSER_ERROR(90035, "解锁系统用户失败"),
    DEPOSIT_REPAY_AMOUNT_ERROR(90036, "消保金补缴金额与配置不匹配"),
    CREATE_APP_USER_ERROR(90037, "创建APP用户失败"),
    BUSINESS_LICENSE_NOT_NULL(90038, "营业执照不能为空"),
	ID_CARD_NOT_NULL(90039, "身份证不能为空"),
	SELF_SUPPORT_SHOP_CREATE_ERROR(90040, "该商家不能创建自营店铺,请重新输入"),
    MERCHANT_DEPOSIT_REPAY_ERROR(90041, "商户没有初始权益,不能补缴消保金"),
    MERCHANT_DEPOSIT_GROUP_ERROR(90042, "店铺类型与消保金权益类型不匹配"),
	AUDIT_REASON_NOT_NULL(90043, "审核原因不能为空"),
	ONLY_PENDING_AUDIT_MERCHANT_NEED_REVIEW(90044, "只有待审核状态的商家才需要审核"),
	MERCHANT_NOT_MATCH(90045, "当前登录用户和被修改店铺所属人不匹配"),
    GOODS_SERVICE_ERROR(90046, "商品服务调用失败"),
    INCENTIVE_CONF_ERROR(90047, "激励配置不存在"),
    ID_CARD_QUERY_ERROR(90048, "获取实名身份证信息失败，请稍后再试"),

    GOODS_NAME_NOT_EMPTY(90049, "藏品名称不能为空"),
    GOODS_NAME_NOT_OVER_THIRTY(90050, "藏品名称不得超过30个字符"),
    GOODS_ERA_NOT_EMPTY(90051, "藏品年代不能为空"),
    SALE_PRICE_NOT_EMPTY(90052, "销售价格不能为空"),
    GOODS_CONDITION_NOT_EMPTY(90053, "藏品品相不能为空"),
    GOODS_SIZE_NOT_EMPTY(90054, "藏品尺寸不能为空"),
    GOODS_MAINIMG_MD5_EMPTY(90055, "主图md5不能为空"),
    GOODS_MAINIMG_MD5_EXIST(90056, "藏品已上传，请勿重复上传"),
    AMOUNT_IS_INC(90057, "未知的入账方式"),
	SHOP_TYPE_ERROR(90058,"无效的店铺类型"),
    INCENTIVE_BASE_ERROR(90059, "基础激励配置区间出现重叠"),
    VIEW_NO_PERMISSION(90060, "无权限查看"),
    GOODS_NUMBER_NOT_EMPTY(90061, "藏品编号不能为空"),
    GOODS_NAME_SENSITIVE(90062, "商品名称包含敏感词"),
    SENSITIVE_WORD_ERROR(90063, "敏感词检测异常"),
	CONTACT_INFO_NOT_NULL(90064, "申请入驻商家类型时,联系人和联系电话不能为空"),
	DISABLE_ENABLE_USER_INFO_ERROR(90065, "禁用启用移动端用户失败"),
	GET_DICT_ERROR(90066, "获取字典数据失败"),
	SELF_SUPPORT_PARTNER_ADD_SELF_SUPPORT_MERCHANT(90067, "非自营合伙人无法创建自营商家"),

    INCENTIVE_COMMISSION_STATUS_ERROR(90068, "激励佣金状态错误"),
    INCENTIVE_COMMISSION_ROLE_ERROR(90069, "激励佣金角色错误"),
    INVOICE_NO_NO_EQUALS(90070, "发票单号不一致"),
    INCENTIVE_INVOICE_No_EXIST(90071, "发票单号不存在"),
    COMMISSION_IDS_ERROR(90072, "佣金ID集合异常"),
	/**
	 * 进件相关
	 */
	NO_PERMISSION(90200, "无权限操作"),
	ACTIVATE_FAILED(90201, "激活失败,请稍后重试"),
	ENTERPRISE_USER_ENTRY_ERROR(90202, "企业商家进件激活失败"),
	INDIVIDUAL_USER_ENTRY_ERROR(90203, "个人商家进件激活失败"),
	USER_BUSI_OPEN_ERROR(90204, "用户业务入驻失败"),
	MERCHANT_DETAIL_NOT_EXIST(90205, "商家详情不存在,请补充信息后重新激活"),
	ENTERPRISE_USER_MODIFY_ERROR(90206, "企业商家进件修改失败"),
	INDIVIDUAL_USER_MODIFY_ERROR(90207, "个人商家进件修改失败"),
    GET_ACCOUNT_ERROR(90068, "获取账户信息失败"),
    DEPOSIT_ORDER_HAS_PAY(90069, "消保金订单已支付,请勿重复支付"),
    DEPOSIT_ORDER_PAYBACK_ERROR(90070, "消保金订单支付回调失败"),
    HAS_ON_SALE_LOT(1001, "存在在售藏品，无法转出消保金"),
    HAS_UNFINISHED_ORDER(1002, "存在未完成订单，无法转出消保金"),
    TRANSFER_FAILED(1003, "转账失败"),
    DEPOSIT_BALANCE_NOT_ENOUGH(1004, "消保金余额不足"),
    // 消保金转出余额支付失败
    DEPOSIT_TRANSFER_PAY_FAILED(1005, "消保金转出,余额支付失败"),
	ACTIVATE_FAILED_CONTACT_ADMIN(90071, "激活失败,请联系管理员"),
    HAS_PROCESSING_ORDER(90072,"存在处理中的转出订单，请稍后再试"),
	// 查询渠道经理用户异常
	QUERY_CHANNEL_MANAGER_USER_ERROR(90073, "查询渠道经理用户异常"),

    ;

    private final Integer code;
    private final String msg;

    public void throwException() {
    	throw new MerchantException(this.msg,this.code);
    }

}
