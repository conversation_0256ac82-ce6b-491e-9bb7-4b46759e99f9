package com.yts.yyt.merchant.service;

import com.alibaba.fastjson.JSON;
import com.yts.yyt.merchant.task.SettlementCommissionTask;
import com.yts.yyt.order.api.entity.TransactionSettingEntity;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class IncentiveComissionTest extends BaseSpringTest{
    @Autowired
    private SettlementCommissionTask settlementCommissionTask;

    @Autowired
    private BaseRemoteService remoteService;

    /**
     * 调试佣金生成
     * @return
     */
    @Test
    public void testSettlementCommissionTask() {
        List<TransactionSettingEntity> confResult = remoteService.getConfByRuleConfig("deposit_person_equity");

        System.out.println(JSON.toJSONString(confResult));
    }

}
