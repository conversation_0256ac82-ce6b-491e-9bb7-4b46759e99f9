//package com.yts.yyt.merchant.service;
//
//
//import cn.hutool.json.JSONUtil;
//import com.alibaba.fastjson.JSON;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import com.yts.yyt.admin.api.dto.UserInfo;
//import com.yts.yyt.admin.api.feign.RemoteUserService;
//import com.yts.yyt.common.core.util.R;
//import com.yts.yyt.goods.api.dto.AddChainAndQRCodeDTO;
//import com.yts.yyt.goods.api.feign.RemoteGoodsTraceabilityPreService;
//import com.yts.yyt.merchant.api.dto.SearchStoreGoodsInfoDTO;
//import com.yts.yyt.merchant.api.entity.StoreGoodsInfo;
//import com.yts.yyt.merchant.api.vo.StoreGoodsInfoVO;
//import org.junit.Test;
//import org.springdoc.core.annotations.ParameterObject;
//import org.springframework.beans.factory.annotation.Autowired;
//
//public class GoodsTraceabilityServiceTest extends BaseSpringTest{
//
//    @Autowired
//    private RemoteGoodsTraceabilityPreService remoteGoodsTraceabilityPreService;
//
//    @Autowired
//    private RemoteUserService remoteUserService;
//
//    @Autowired
//    private StoreGoodsInfoService storeGoodsInfoService;
//
//    @Test
//    public void test_page(){
//        Page pageParam = new Page();
//        pageParam.setCurrent(1);
//        pageParam.setSize(100L);
//        SearchStoreGoodsInfoDTO search = new SearchStoreGoodsInfoDTO();
//        search.setStartCreateTime("2025-04-14 00:00:00");
//        search.setEndCreateTime("2025-04-15 23:59:59");
//        Page<StoreGoodsInfoVO> page = storeGoodsInfoService.getBasePage(pageParam, search);
//        System.out.println(JSON.toJSONString(page));
//    }
//
//    @Test
//    public void test_addchain(){
//        StoreGoodsInfo goodsInfo = storeGoodsInfoService.getById(1676075500280123392L);
//        AddChainAndQRCodeDTO code = getChainData(goodsInfo);
//        System.out.println(JSON.toJSONString(code));
//        remoteGoodsTraceabilityPreService.pushQRCodeChain(code);
//    }
//
//    private AddChainAndQRCodeDTO getChainData(StoreGoodsInfo goodsInfo) {
//        AddChainAndQRCodeDTO dto = new AddChainAndQRCodeDTO();
//
//
//        dto.setName(goodsInfo.getName());
//        dto.setGoodsEra(goodsInfo.getGoodsEra());
//        dto.setGoodsCondition(goodsInfo.getGoodsCondition());
//        dto.setGoodsSize(goodsInfo.getGoodsSize());
////        dto.setMainImage(JSONUtil.toList(JSONUtil.parseArray(goodsInfo.getMainImage()), String.class));
//        dto.setMainImage(goodsInfo.getMainImage());
//        dto.setPreviewImage(goodsInfo.getPreviewImage());
//        dto.setSalePrice(goodsInfo.getSalePrice());
//        dto.setStockQuantity(1);
//        dto.setDetailInfo(goodsInfo.getDetailInfo());
//        dto.setPlaceOfOrigin(goodsInfo.getPlaceOfOrigin());
//        dto.setOwnership("");
//        dto.setGoodsNo(goodsInfo.getGoodsNo());
//
//        return dto;
//    }
//
//}
