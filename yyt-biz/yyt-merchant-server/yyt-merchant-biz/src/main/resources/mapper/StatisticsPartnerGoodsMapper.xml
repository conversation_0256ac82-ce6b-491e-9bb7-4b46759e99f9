<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yts.yyt.merchant.mapper.StatisticsPartnerGoodsMapper">

    <resultMap id="BaseResultMap" type="com.yts.yyt.merchant.api.entity.StatisticsPartnerGoods">
        <id column="id" property="id"/>
        <result column="partner_id" property="partnerId"/>
        <result column="region" property="region"/>
        <result column="year" property="year"/>
        <result column="month" property="month"/>
        <result column="data" property="data"/>
        <result column="counts" property="counts"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <resultMap id="StatisticsVOResultMap" type="com.yts.yyt.merchant.api.vo.StatisticsPartnerGoodsVO">
        <id column="id" property="id"/>
        <result column="partner_id" property="partnerId"/>
        <result column="partner_name" property="partnerName"/>
        <result column="region" property="region"/>
        <result column="region_name" property="regionName"/>
        <result column="sys_user_id" property="sysUserId"/>
        <result column="channel_manager" property="channelManager"/>
        <result column="year" property="year"/>
        <result column="month" property="month"/>
        <result column="data" property="data"/>
        <result column="counts" property="counts"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 分页查询合伙人藏品统计 -->
    <select id="queryStatisticsPage" resultMap="StatisticsVOResultMap">
        SELECT 
            spg.id,
            spg.partner_id,
            cp.partner_name,
            spg.region,
            CONCAT(sr.province, '', sr.city) as region_name,
            cp.sys_user_id as sys_user_id,
            cp.create_by as channel_manager,
            spg.year,
            spg.month,
            spg.data,
            spg.counts,
            spg.create_time,
            spg.update_time
        FROM 
            statistics_partner_goods spg
        LEFT JOIN 
            city_partner cp ON spg.partner_id = cp.id
        LEFT JOIN 
            city_partner_area sr ON spg.region = sr.id
        WHERE 
            spg.del_flag = '0'
            <if test="dto.partnerId != null">
                AND spg.partner_id = #{dto.partnerId}
            </if>
            <if test="dto.partnerName != null and dto.partnerName != ''">
                AND cp.partner_name LIKE CONCAT('%', #{dto.partnerName}, '%')
            </if>
            <if test="dto.region != null">
                AND spg.region = #{dto.region}
            </if>
            <if test="dto.year != null and dto.year != ''">
                AND spg.year = #{dto.year}
            </if>
            <if test="dto.month != null and dto.month != ''">
                AND spg.month = #{dto.month}
            </if>
        ORDER BY
			spg.counts DESC
    </select>

    <!-- 统计合伙人指定日期的藏品上传数据 -->
    <select id="statisticsPartnerMonthData" resultType="com.yts.yyt.merchant.api.dto.PartnerDailyStatisticsDTO">
        SELECT
        	sgi.city_partner_id as partnerId,
            DAY(sgi.create_time) as day,
            COUNT(sgi.id) as count
        FROM 
            store_goods_info sgi
        WHERE 
            sgi.del_flag = '0'
			AND city_partner_id is not null
			AND merchant_id is not null
            <if test="dateParam != null and dateParam != ''">
                AND sgi.create_time LIKE CONCAT(#{dateParam}, '%')
            </if>
        GROUP BY
			sgi.city_partner_id,DAY(sgi.create_time)
    </select>

    <!-- 查询所有合伙人ID -->
    <select id="selectPartnerIds" resultType="java.lang.Long">
        SELECT 
            id 
        FROM 
            city_partner
        WHERE 
            del_flag = '0'
    </select>

    <!-- 获取合伙人区域ID -->
    <select id="getPartnerRegionId" resultType="java.lang.Long">
        SELECT 
            region
        FROM 
            city_partner cp
        WHERE 
            cp.id = #{partnerId}
            AND cp.del_flag = '0'
    </select>

    <!-- 查询合伙人藏品统计数据用于导出 -->
    <select id="queryStatisticsForExport" resultMap="StatisticsVOResultMap">
        SELECT 
            spg.id,
            spg.partner_id,
            cp.partner_name,
            spg.region,
            CONCAT(cpa.province, '', cpa.city) as region_name,
            cp.sys_user_id as sys_user_id,
            cp.create_by as channel_manager,
            spg.year,
            spg.month,
            spg.data,
            spg.counts,
            spg.create_time,
            spg.update_time
        FROM 
            statistics_partner_goods spg
        LEFT JOIN 
            city_partner cp ON spg.partner_id = cp.id
        LEFT JOIN
            city_partner_area cpa ON spg.region = cpa.id
        WHERE 
            spg.del_flag = '0'
            <if test="dto.year != null and dto.year != ''">
                AND spg.year = #{dto.year}
            </if>
            <if test="dto.month != null and dto.month != ''">
                AND spg.month = #{dto.month}
            </if>
            <if test="dto.partnerIds != null and dto.partnerIds.size() > 0">
                AND spg.partner_id IN
                <foreach collection="dto.partnerIds" item="partnerId" open="(" separator="," close=")">
                    #{partnerId}
                </foreach>
            </if>
            <if test="dto.region != null">
                AND spg.region = #{dto.region}
            </if>
        ORDER BY 
            spg.year DESC,
            spg.month DESC,
            spg.update_time DESC
    </select>

    <!-- 获取指定年月的藏品上传总数 -->
    <select id="getMonthTotalCount" resultType="java.lang.Integer">
        SELECT 
            SUM(spg.counts) as total_count
        FROM 
            statistics_partner_goods spg
        LEFT JOIN 
            city_partner cp ON spg.partner_id = cp.id
        WHERE 
            spg.del_flag = '0'
            <if test="dto.partnerName != null and dto.partnerName != ''">
                AND cp.partner_name LIKE CONCAT('%', #{dto.partnerName}, '%')
            </if>
            <if test="dto.region != null">
                AND spg.region = #{dto.region}
            </if>
            <if test="dto.year != null and dto.year != ''">
                AND spg.year = #{dto.year}
            </if>
            <if test="dto.month != null and dto.month != ''">
                AND spg.month = #{dto.month}
            </if>
    </select>

    <!-- 查询指定日期有上传藏品的合伙人ID列表 -->
    <select id="selectPartnerIdsByDate" resultType="java.lang.Long">
        SELECT DISTINCT city_partner_id
        FROM store_goods_info
        WHERE del_flag = '0'
			AND city_partner_id is not null
			AND merchant_id is not null
        <if test="dateParam != null and dateParam != ''">
            AND create_time LIKE CONCAT(#{dateParam}, '%')
        </if>
    </select>

    <!-- 查询昨天更新的商品信息 -->
    <select id="queryUpdatedGoodsInfo" resultType="com.yts.yyt.merchant.api.dto.PartnerUpdatedDailyCountDTO">
        SELECT 
            city_partner_id as partnerId,
            DATE_FORMAT(create_time,'%Y-%m-%d') as createDate,
            count(id) as count
        FROM 
            store_goods_info 
        WHERE 
            create_time &lt; #{yesterday}
            AND update_time LIKE CONCAT(#{yesterday}, '%')
            AND del_flag = 1
        GROUP BY
            city_partner_id, DATE_FORMAT(create_time,'%Y-%m-%d')
    </select>
</mapper>