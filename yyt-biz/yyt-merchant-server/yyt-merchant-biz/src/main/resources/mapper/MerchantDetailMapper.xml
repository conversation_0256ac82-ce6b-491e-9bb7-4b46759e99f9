<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yts.yyt.merchant.mapper.MerchantDetailMapper">

    <resultMap type="com.yts.yyt.merchant.api.entity.MerchantDetailEntity" id="MerchantDetailMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="merchantId" column="merchant_id" jdbcType="INTEGER"/>
        <result property="businessLicense" column="business_license" jdbcType="VARCHAR"/>
        <result property="licenseValidType" column="license_valid_type" jdbcType="INTEGER"/>
        <result property="licenseStartDate" column="license_start_date" jdbcType="VARCHAR"/>
        <result property="licenseEndDate" column="license_end_date" jdbcType="VARCHAR"/>
        <result property="regProvince" column="reg_province" jdbcType="VARCHAR"/>
        <result property="regCity" column="reg_city" jdbcType="VARCHAR"/>
        <result property="regDistrict" column="reg_district" jdbcType="VARCHAR"/>
        <result property="regProvinceName" column="reg_province_name" jdbcType="VARCHAR"/>
        <result property="regCityName" column="reg_city_name" jdbcType="VARCHAR"/>
        <result property="regDistrictName" column="reg_district_name" jdbcType="VARCHAR"/>
        <result property="regDetail" column="reg_detail" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="idCardNumber" column="id_card_number" jdbcType="VARCHAR"/>
        <result property="idValidType" column="id_valid_type" jdbcType="INTEGER"/>
        <result property="idStartDate" column="id_start_date" jdbcType="VARCHAR"/>
        <result property="idEndDate" column="id_end_date" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

</mapper>

