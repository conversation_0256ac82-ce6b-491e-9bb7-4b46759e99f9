<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yts.yyt.merchant.mapper.CityPartnerChannelManagerMapper">

    <resultMap type="com.yts.yyt.merchant.api.entity.CityPartnerChannelManagerEntity" id="CityPartnerChannelManagerMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="partnerId" column="partner_id" jdbcType="INTEGER"/>
        <result property="sysUserId" column="sys_user_id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="VARCHAR"/>
    </resultMap>

</mapper>

