package com.yts.yyt.merchant.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yts.yyt.merchant.api.dto.CityPartnerDTO;
import com.yts.yyt.merchant.api.dto.CityPartnerQueryDTO;
import com.yts.yyt.merchant.api.entity.CityPartner;
import com.yts.yyt.merchant.api.entity.CityPartnerArea;
import com.yts.yyt.merchant.api.vo.CityPartnerVO;

import java.util.List;

public interface CityPartnerService extends IService<CityPartner> {

    void add(CityPartner cityPartner);

	Page<CityPartnerVO> pageList(Page<CityPartnerVO> page, CityPartnerQueryDTO queryDto);

	void updateStatus(CityPartnerDTO partnerDTO);

	void sendAccount(Long id);

	CityPartnerVO detail(Long id);

	/**
	 *  根据系统用户ID查询城市合伙人信息
	 * @param sysUserId
	 * @return
	 */
    CityPartnerVO getPartnerDetailBySysUserId(Long sysUserId);

	/**
	 * 根据系统用户ID查询城市合伙人商户ID
	 * @param sysUserId 系统用户ID
	 * @return 合伙人商户ID集合
	 */
	List<Long> getPartnerMerchantIdBySysUserId(Long sysUserId);

	CityPartnerArea getRegionInfoById(Long id);

	CityPartner getPartnerAllInfoBySysUserId(Long sysUserId);

	void update(CityPartnerDTO dto);
}
