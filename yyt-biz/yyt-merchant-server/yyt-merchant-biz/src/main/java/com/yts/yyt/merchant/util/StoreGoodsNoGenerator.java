package com.yts.yyt.merchant.util;

import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Component
@RequiredArgsConstructor
public class StoreGoodsNoGenerator {

    private final RedisTemplate<String, Object> redisTemplate;
    private static final String GOODS_NO_KEY = "goods:no:sequence";
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    /**
     * 生成商品编号
     * 规则：年月日+时分秒+最长6位自增序号
     * @return 商品编号
     */
    public String generateNo() {
        // 1. 获取当前时间字符串
        LocalDateTime now = LocalDateTime.now();
        String timeStr = now.format(formatter);
        
        // 2. 获取Redis中的自增序号
        Long increment = redisTemplate.opsForValue().increment(GOODS_NO_KEY);
        if (increment == null) {
            // 如果是第一次，设置初始值为1
            increment = 1L;
            redisTemplate.opsForValue().set(GOODS_NO_KEY, increment);
        }
        
        // 3. 拼接订单编号
        // 将自增序号格式化为8位，不足前面补0
        String sequence = String.format("%06d", increment);
        
        return timeStr + sequence;
    }
} 
