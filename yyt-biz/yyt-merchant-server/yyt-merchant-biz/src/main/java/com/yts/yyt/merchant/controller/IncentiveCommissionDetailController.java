package com.yts.yyt.merchant.controller;



import com.yts.yyt.common.swagger.base.BaseController;
import com.yts.yyt.merchant.service.IncentiveCommissionDetailService;
import com.google.common.net.HttpHeaders;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@AllArgsConstructor
@RequestMapping("/incentive/commission/detail")
@Tag(description = "IncentiveCommissionDetailController", name = "佣金计算配置实例")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class IncentiveCommissionDetailController extends BaseController {
    
    private final IncentiveCommissionDetailService incentiveCommissionDetailService;

}

