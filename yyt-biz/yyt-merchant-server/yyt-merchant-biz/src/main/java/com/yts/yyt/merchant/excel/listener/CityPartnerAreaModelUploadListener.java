package com.yts.yyt.merchant.excel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.yts.yyt.merchant.excel.model.CityPartnerAreaModel;

import java.util.ArrayList;
import java.util.List;

public class CityPartnerAreaModelUploadListener extends AnalysisEventListener<CityPartnerAreaModel> {
	private final List<CityPartnerAreaModel> areaList = new ArrayList<>();

	// 每解析一行数据会调用此方法
	@Override
	public void invoke(CityPartnerAreaModel area, AnalysisContext analysisContext) {
		areaList.add(area);
	}

	// 所有数据解析完成后调用此方法
	@Override
	public void doAfterAllAnalysed(AnalysisContext analysisContext) {
	}

	public List<CityPartnerAreaModel> getAreaList() {
		return areaList;
	}
}
