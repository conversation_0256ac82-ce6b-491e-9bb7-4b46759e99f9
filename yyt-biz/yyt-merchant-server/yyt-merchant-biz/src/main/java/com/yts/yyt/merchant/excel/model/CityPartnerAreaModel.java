package com.yts.yyt.merchant.excel.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

@Data
@ColumnWidth(30)
public class CityPartnerAreaModel {

	/**
	 * 省份
	 */
	@ExcelProperty(value = "省份", index = 0)
	private String province;
	/**
	 * 城市
	 */
	@ExcelProperty(value = "城市", index = 1)
	private String city;
	/**
	 * 每月上新基础件数
	 */
	@ExcelProperty(value = "每月上新基础件数", index = 2)
	private Integer baseCondition;

}