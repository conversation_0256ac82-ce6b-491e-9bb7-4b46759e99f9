package com.yts.yyt.merchant.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yts.yyt.common.data.datascope.YytBaseMapper;
import com.yts.yyt.merchant.api.entity.CityPartner;
import com.yts.yyt.merchant.api.vo.CityPartnerVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface CityPartnerMapper extends YytBaseMapper<CityPartner> {

	Page<CityPartnerVO> pageList(Page page , @Param(Constants.WRAPPER) Wrapper wrapper);

}

