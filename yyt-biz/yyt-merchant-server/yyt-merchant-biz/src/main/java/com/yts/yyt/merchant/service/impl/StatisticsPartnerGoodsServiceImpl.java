package com.yts.yyt.merchant.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xxl.job.core.context.XxlJobHelper;
import com.yts.yyt.common.core.constant.enums.DeletedEnum;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.merchant.api.dto.PartnerDailyStatisticsDTO;
import com.yts.yyt.merchant.api.dto.PartnerUpdatedDailyCountDTO;
import com.yts.yyt.merchant.api.dto.StatisticsPartnerGoodsDTO;
import com.yts.yyt.merchant.api.dto.StatisticsPartnerGoodsExportDTO;
import com.yts.yyt.merchant.api.entity.StatisticsPartnerGoods;
import com.yts.yyt.merchant.api.vo.StatisticsPartnerGoodsVO;
import com.yts.yyt.merchant.mapper.StatisticsPartnerGoodsMapper;
import com.yts.yyt.merchant.service.StatisticsPartnerGoodsService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 合伙人藏品统计Service实现
 *
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StatisticsPartnerGoodsServiceImpl extends ServiceImpl<StatisticsPartnerGoodsMapper, StatisticsPartnerGoods> implements StatisticsPartnerGoodsService {

	private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

	@Override
	public IPage<StatisticsPartnerGoodsVO> queryStatisticsPage(StatisticsPartnerGoodsDTO dto) {
		Page<StatisticsPartnerGoodsVO> page = new Page<>(dto.getCurrent(), dto.getSize());
		IPage<StatisticsPartnerGoodsVO> voiPage = baseMapper.queryStatisticsPage(page, dto);

		if (voiPage.getRecords().isEmpty()) {
			return voiPage;
		}

		// 处理统计数据和渠道经理信息
		processStatisticsRecords(voiPage.getRecords());

		return voiPage;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public R<Boolean> executeStatisticsTask(String dateParam) {
		try {
			log.info("开始执行合伙人藏品统计任务，日期：{}", dateParam);
			XxlJobHelper.log("开始执行合伙人藏品统计任务，日期：{}", dateParam);

			// 获取统计数据
			List<PartnerDailyStatisticsDTO> partnerStatistics = baseMapper.statisticsPartnerMonthData(dateParam);
			if (partnerStatistics.isEmpty()) {
				log.info("没有需要统计的合伙人数据");
				XxlJobHelper.log("没有需要统计的合伙人数据");
				return R.ok(true);
			}

			// 按合伙人ID分组并处理数据
			processPartnerStatistics(partnerStatistics, dateParam);

			log.info("合伙人藏品统计任务执行完成，日期：{}", dateParam);
			XxlJobHelper.log("合伙人藏品统计任务执行完成，日期：{}", dateParam);
			return R.ok(true);
		} catch (Exception e) {
			log.error("执行合伙人藏品统计任务异常，日期：{}", dateParam, e);
			XxlJobHelper.log("执行合伙人藏品统计任务异常，日期：{}，异常信息：{}", dateParam, e.getMessage());
			return R.failed("执行统计任务失败：" + e.getMessage());
		}
	}

	/**
	 * 处理合伙人统计数据
	 *
	 * @param partnerStatistics 合伙人统计数据
	 * @param dateParam         日期参数
	 */
	private void processPartnerStatistics(List<PartnerDailyStatisticsDTO> partnerStatistics, String dateParam) {
		// 按合伙人ID分组
		Map<Long, List<PartnerDailyStatisticsDTO>> partnerDataMap = partnerStatistics.stream()
				.collect(Collectors.groupingBy(PartnerDailyStatisticsDTO::getPartnerId));

		log.info("需要统计的合伙人数量：{}", partnerDataMap.size());
		XxlJobHelper.log("需要统计的合伙人数量：{}", partnerDataMap.size());

		// 解析日期参数
		String[] dateParts = dateParam.split("-");
		String year = dateParts[0];
		String month = dateParts[1];

		// 处理每个合伙人的数据
		partnerDataMap.forEach((partnerId, dailyData) -> {
			log.info("开始统计合伙人[{}]的数据", partnerId);
			XxlJobHelper.log("开始统计合伙人[{}]的数据", partnerId);

			// 更新统计数据
			updatePartnerStatistics(partnerId, year, month, dailyData);

			log.info("合伙人[{}]的数据统计完成", partnerId);
			XxlJobHelper.log("合伙人[{}]的数据统计完成", partnerId);
		});
	}

	/**
	 * 更新合伙人统计数据
	 *
	 * @param partnerId 合伙人ID
	 * @param year      年份
	 * @param month     月份
	 * @param dailyData 每日数据
	 */
	private void updatePartnerStatistics(Long partnerId, String year, String month, List<PartnerDailyStatisticsDTO> dailyData) {
		// 查询是否已存在记录
		StatisticsPartnerGoods statistics = getExistingStatistics(partnerId, year, month);

		// 解析并更新统计数据
		Map<String, Integer> dataMap = parseExistingData(statistics);

		// 将PartnerDailyStatisticsDTO列表转换为Map格式
		List<Map<String, Object>> convertedDailyData = dailyData.stream()
				.map(dto -> {
					Map<String, Object> map = new HashMap<>();
					map.put("day", dto.getDay());
					map.put("count", dto.getCount());
					return map;
				})
				.collect(Collectors.toList());

		int totalCount = updateStatisticsData(dataMap, convertedDailyData);

		// 保存或更新统计记录
		saveOrUpdateStatistics(statistics, partnerId, year, month, dataMap, totalCount);
	}

	@Override
	public R<Integer> getMonthTotalCount(StatisticsPartnerGoodsDTO dto) {
		// 调用Mapper的SQL查询方法
		Integer totalCount = baseMapper.getMonthTotalCount(dto);
		return R.ok(totalCount);
	}

	@Override
	public void exportStatistics(StatisticsPartnerGoodsExportDTO dto, HttpServletResponse response) {
		log.info("开始导出合伙人藏品统计数据，参数: {}", dto);

		ExcelWriter excelWriter = null;
		try {
			// 设置响应头
			prepareExportResponse(response);

			// 查询并处理统计数据
			log.info("查询统计数据");
			List<StatisticsPartnerGoodsVO> voList = baseMapper.queryStatisticsForExport(dto);
			if (voList == null) {
				log.warn("查询结果为null");
				voList = new ArrayList<>();
			}

			log.info("查询到{}条记录", voList.size());
			processStatisticsRecords(voList);

			// 按照藏品上传数量倒序排序
			voList.sort((a, b) -> b.getCounts() != null && a.getCounts() != null
				? b.getCounts().compareTo(a.getCounts())
				: 0);
			log.info("已按照藏品上传数量倒序排序");

			// 确定当月天数
			int daysInMonth = 31; // 默认31天
			if (StringUtils.hasText(dto.getYear()) && StringUtils.hasText(dto.getMonth())) {
				daysInMonth = getDaysInMonth(dto.getYear(), dto.getMonth());
			}
			log.info("当月天数: {}", daysInMonth);

			// 创建动态表头
			List<List<String>> headList = createDynamicHeadList(daysInMonth);

			// 转换数据为动态列格式
			List<List<Object>> dataList = convertToDynamicDataList(voList, daysInMonth);

			log.info("开始写入Excel，列数: {}", headList.size());

			// 使用动态表头和数据列表导出
			excelWriter = EasyExcel.write(response.getOutputStream())
					.registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
					.head(headList)
					.build();

			WriteSheet writeSheet = EasyExcel.writerSheet("合伙人藏品统计").build();
			excelWriter.write(dataList, writeSheet);

			log.info("Excel导出完成");
		} catch (IOException e) {
			log.error("导出过程中发生IO异常: {}", e.getMessage(), e);
			handleExportException(response, e);
		} catch (Exception e) {
			log.error("导出过程中发生未预期异常: {}", e.getMessage(), e);
			handleExportException(response, new RuntimeException("导出过程中发生未预期异常", e));
		} finally {
			// 确保excelWriter被正确关闭
			if (excelWriter != null) {
				try {
					excelWriter.finish();
					log.info("ExcelWriter已关闭");
				} catch (Exception e) {
					log.error("关闭ExcelWriter时发生异常: {}", e.getMessage(), e);
				}
			}
		}
	}

	/**
	 * 创建动态表头
	 *
	 * @param daysInMonth 当月天数
	 * @return 动态表头列表
	 */
	private List<List<String>> createDynamicHeadList(int daysInMonth) {
		List<List<String>> headList = new ArrayList<>();

		// 基础列
		headList.add(Collections.singletonList("序号"));
		headList.add(Collections.singletonList("合伙人名称"));
		headList.add(Collections.singletonList("区域名称"));
		headList.add(Collections.singletonList("渠道经理"));
		headList.add(Collections.singletonList("年份"));
		headList.add(Collections.singletonList("月份"));

		// 动态添加日期列 - 根据当月天数
		for (int i = 1; i <= daysInMonth; i++) {
			headList.add(Collections.singletonList(i + "日"));
		}

		// 添加合计列
		headList.add(Collections.singletonList("每月上传藏品总数"));

		return headList;
	}

	/**
	 * 将VO列表转换为动态数据列表
	 *
	 * @param voList      VO列表
	 * @param daysInMonth 当月天数
	 * @return 动态数据列表
	 */
	private List<List<Object>> convertToDynamicDataList(List<StatisticsPartnerGoodsVO> voList, int daysInMonth) {
		List<List<Object>> dataList = new ArrayList<>();
		int rowIndex = 1;
		for (StatisticsPartnerGoodsVO vo : voList) {
			List<Object> rowData = new ArrayList<>();

			// 基础字段
			rowData.add(rowIndex++); // 序号
			rowData.add(vo.getPartnerName()); // 合伙人名称
			rowData.add(vo.getRegionName()); // 区域名称
			rowData.add(vo.getChannelManager()); // 渠道经理
			rowData.add(vo.getYear()); // 年份
			rowData.add(vo.getMonth()); // 月份

			// 动态添加每日数据
			Map<String, Integer> dailyData = vo.getDailyData();
			if (dailyData != null) {
				for (int i = 1; i <= daysInMonth; i++) {
					Integer dayValue = dailyData.get(String.valueOf(i));
					rowData.add(dayValue != null ? dayValue : 0);
				}
			} else {
				// El没有每日数据，填充0
				for (int i = 1; i <= daysInMonth; i++) {
					rowData.add(0);
				}
			}

			// 添加合计
			rowData.add(vo.getCounts()); // 每月上传藏品总数

			dataList.add(rowData);
		}

		return dataList;
	}

	/**
	 * 处理统计记录，包括解析日期数据
	 *
	 * @param records 统计记录列表
	 */
	private void processStatisticsRecords(List<StatisticsPartnerGoodsVO> records) {
		if (records.isEmpty()) {
			return;
		}

		// 处理每条记录，准备月份数据和解析JSON
		records.forEach(this::processDailyData);

		// 注意：渠道经理名称(channelManager)已在SQL查询中通过city_partner表的create_by字段直接获取
		// 无需额外设置或请求远程服务
	}

	/**
	 * 处理单条记录的每日数据
	 *
	 * @param vo 统计记录
	 */
	private void processDailyData(StatisticsPartnerGoodsVO vo) {
		// 使用TreeMap临时排序，确保按照日期数字顺序
		TreeMap<Integer, Integer> tempMap = new TreeMap<>();
		int daysInMonth = getDaysInMonth(vo.getYear(), vo.getMonth());

		for (int i = 1; i <= daysInMonth; i++) {
			tempMap.put(i, 0);
		}

		// 如果有数据，则覆盖对应天数的值
		if (vo.getData() != null && !vo.getData().toString().isEmpty()) {
			try {
				Map<String, Object> dataFromDb = JSON.parseObject(vo.getData().toString(), Map.class);
				dataFromDb.forEach((key, value) -> {
					if (value instanceof Number) {
						try {
							int day = Integer.parseInt(key);
							tempMap.put(day, ((Number) value).intValue());
						} catch (NumberFormatException e) {
							log.warn("无法解析日期键: {}", key);
						}
					}
				});
			} catch (Exception e) {
				log.error("解析data字段失败: {}", e.getMessage());
			}
		}

		// 将排序后的数据复制到LinkedHashMap中
		LinkedHashMap<String, Integer> dailyDataMap = new LinkedHashMap<>();
		tempMap.forEach((day, count) -> dailyDataMap.put(String.valueOf(day), count));

		vo.setDailyData(dailyDataMap);
	}

	/**
	 * 准备导出响应
	 *
	 * @param response HTTP响应对象
	 */
	private void prepareExportResponse(HttpServletResponse response) {
		// 设置正确的内容类型和字符编码
		response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
		response.setCharacterEncoding("utf-8");

		// 生成文件名
		String fileName = "合伙人藏品统计_" + LocalDateTime.now().format(DATE_FORMATTER);

		try {
			// 进行URL编码，确保文件名在各种浏览器中都有效
			fileName = java.net.URLEncoder.encode(fileName, "UTF-8");
			// 针对IE浏览器的特殊处理
			fileName = fileName.replaceAll("\\+", "%20");
		} catch (UnsupportedEncodingException e) {
			log.warn("文件名编码失败: {}", e.getMessage());
		}

		// 设置下载头，指定下载文件类型和文件名
		response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
		// 禁用缓存
		response.setHeader("Cache-Control", "no-store");
		response.setHeader("Pragma", "no-cache");
	}

	/**
	 * 处理导出异常
	 *
	 * @param response HTTP响应对象
	 * @param e        异常
	 */
	private void handleExportException(HttpServletResponse response, Exception e) {
		log.error("导出合伙人藏品统计数据失败", e);
		try {
			// 如果响应已经提交，则无法再做处理
			if (response.isCommitted()) {
				log.error("响应已提交，无法处理异常");
				return;
			}

			// 重置响应
			response.reset();
			// 设置响应类型为JSON
			response.setContentType("application/json");
			response.setCharacterEncoding("utf-8");
			// 设置状态码为500
			response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
			// 防止浏览器缓存错误信息
			response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
			response.setHeader("Pragma", "no-cache");

			// 构造错误信息，只包含关键信息，不暴露太多细节
			String errorMsg = "导出失败: " + (e.getMessage() != null ? e.getMessage() : "未知错误");
			// 写入错误信息
			response.getWriter().println("{\"code\":500,\"message\":\"" + errorMsg + "\"}");
			// 刷新并关闭输出流
			response.getWriter().flush();
		} catch (IOException ex) {
			log.error("处理导出异常时发生IO错误", ex);
		} catch (Exception ex) {
			log.error("处理导出异常时发生未预期错误", ex);
		}
	}

	/**
	 * 获取已存在的统计记录
	 *
	 * @param partnerId 合伙人ID
	 * @param year      年份
	 * @param month     月份
	 * @return 统计记录，不存在则返回null
	 */
	private StatisticsPartnerGoods getExistingStatistics(Long partnerId, String year, String month) {
		LambdaQueryWrapper<StatisticsPartnerGoods> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(StatisticsPartnerGoods::getPartnerId, partnerId)
				.eq(StatisticsPartnerGoods::getYear, year)
				.eq(StatisticsPartnerGoods::getMonth, month)
				.eq(StatisticsPartnerGoods::getDelFlag, DeletedEnum.UN_DELETE.getType());

		return this.getOne(wrapper);
	}

	/**
	 * 解析已有的统计数据
	 *
	 * @param statistics 统计记录，可为null
	 * @return 解析后的数据Map
	 */
	private Map<String, Integer> parseExistingData(StatisticsPartnerGoods statistics) {
		Map<String, Integer> dataMap = new HashMap<>();

		// 如果已存在记录且有数据，解析已有的数据
		if (statistics != null && StringUtils.hasText(statistics.getData())) {
			try {
				dataMap = JSON.parseObject(statistics.getData(), Map.class);
			} catch (Exception e) {
				log.error("解析已有数据异常", e);
			}
		}

		return dataMap;
	}

	/**
	 * 更新统计数据
	 *
	 * @param dataMap   原有数据Map
	 * @param dailyData 新统计的数据
	 * @return 更新后的总数
	 */
	private int updateStatisticsData(Map<String, Integer> dataMap, List<Map<String, Object>> dailyData) {
		// 更新数据
		for (Map<String, Object> data : dailyData) {
			String dayKey = String.valueOf(data.get("day"));
			int count = Integer.parseInt(data.get("count").toString());
			dataMap.put(dayKey, count);
		}

		// 重新计算总数
		return dataMap.values().stream().mapToInt(Integer::intValue).sum();
	}

	/**
	 * 更新统计数据
	 *
	 * @param dataMap   原有数据Map
	 * @param dailyData 新统计的数据
	 * @return 更新后的总数
	 */
	private int updateStatisticsDataForRecalculate(Map<String, Integer> dataMap, List<Map<String, Object>> dailyData) {
		// 更新数据
		for (Map<String, Object> data : dailyData) {
			String dayKey = String.valueOf(data.get("day"));
			int count = Integer.parseInt(data.get("count").toString());
			int oldCount = dataMap.get(dayKey);
			int newCount = oldCount - count;
			if(newCount > 0){
				dataMap.put(dayKey, newCount);
			} else {
				dataMap.remove(dayKey);
			}
		}

		// 重新计算总数
		return dataMap.values().stream().mapToInt(Integer::intValue).sum();
	}

	/**
	 * 保存或更新统计记录
	 *
	 * @param statistics 已存在的统计记录，可为null
	 * @param partnerId  合伙人ID
	 * @param year       年份
	 * @param month      月份
	 * @param dataMap    数据Map
	 * @param totalCount 总数
	 */
	private void saveOrUpdateStatistics(StatisticsPartnerGoods statistics, Long partnerId, String year, String month,
										Map<String, Integer> dataMap, int totalCount) {
		String jsonData = JSON.toJSONString(dataMap);

		if (statistics == null) {
			// 不存在则新增
			createNewStatistics(partnerId, year, month, jsonData, totalCount);
		} else {
			// 存在则更新
			updateExistingStatistics(statistics, jsonData, totalCount);
		}
	}

	/**
	 * 创建新的统计记录
	 *
	 * @param partnerId  合伙人ID
	 * @param year       年份
	 * @param month      月份
	 * @param jsonData   JSON格式的数据
	 * @param totalCount 总数
	 */
	private void createNewStatistics(Long partnerId, String year, String month, String jsonData, int totalCount) {
		StatisticsPartnerGoods statistics = new StatisticsPartnerGoods();
		statistics.setPartnerId(partnerId);
		statistics.setYear(year);
		statistics.setMonth(month);
		statistics.setData(jsonData);
		statistics.setCounts(totalCount);
		statistics.setCreateTime(LocalDateTime.now());
		statistics.setUpdateTime(LocalDateTime.now());
		statistics.setDelFlag(DeletedEnum.UN_DELETE.getType());

		// 设置区域ID，从合伙人表中获取
		Long regionId = this.baseMapper.getPartnerRegionId(partnerId);
		statistics.setRegion(regionId);

		this.save(statistics);
	}

	/**
	 * 更新已存在的统计记录
	 *
	 * @param statistics 统计记录
	 * @param jsonData   JSON格式的数据
	 * @param totalCount 总数
	 */
	private void updateExistingStatistics(StatisticsPartnerGoods statistics, String jsonData, int totalCount) {
		statistics.setData(jsonData);
		statistics.setCounts(totalCount);
		statistics.setUpdateTime(LocalDateTime.now());
		this.updateById(statistics);
	}

	/**
	 * 获取指定年月的天数
	 *
	 * @param year  年份
	 * @param month 月份
	 * @return 该月的天数
	 */
	private int getDaysInMonth(String year, String month) {
		try {
			int y = Integer.parseInt(year);
			int m = Integer.parseInt(month);

			YearMonth yearMonth = YearMonth.of(y, m);
			return yearMonth.lengthOfMonth();
		} catch (Exception e) {
			log.error("获取月份天数失败，使用默认值31: {}", e.getMessage());
			return 31; // 默认返回31天
		}
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public void recalculateYesterdayUpdatedGoods() {
		log.info("开始重新统计昨天更新的商品数据");
		XxlJobHelper.log("开始重新统计昨天更新的商品数据");

		// 获取昨天的日期
		LocalDate yesterday = LocalDate.now().minusDays(1);
		String yesterdayStr = yesterday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

		// 直接查询昨天更新的商品统计数据
		List<PartnerUpdatedDailyCountDTO> updatedGoodsStatistics = baseMapper.queryUpdatedGoodsInfo(yesterdayStr);

		if (updatedGoodsStatistics.isEmpty()) {
			log.info("没有需要重新统计的数据");
			XxlJobHelper.log("没有需要重新统计的数据");
			return;
		}

		log.info("需要重新统计的合伙人数量：{}", updatedGoodsStatistics.size());
		XxlJobHelper.log("需要重新统计的合伙人数量：{}", updatedGoodsStatistics.size());

		// 根据partnerId进行分组
		Map<Long, List<PartnerUpdatedDailyCountDTO>> map = updatedGoodsStatistics.stream().collect(Collectors.groupingBy(PartnerUpdatedDailyCountDTO::getPartnerId));

		for (Map.Entry<Long, List<PartnerUpdatedDailyCountDTO>> entry : map.entrySet()) {
			Long partnerId = entry.getKey();
			List<PartnerUpdatedDailyCountDTO> value = entry.getValue();

			log.info("开始重新统计合伙人[{}]的数据，涉及日期数量：{}", partnerId, value.size());
			XxlJobHelper.log("开始重新统计合伙人[{}]的数据，涉及日期数量：{}", partnerId, value.size());
			// 根据日期进行分组
			Map<LocalDateTime, List<PartnerUpdatedDailyCountDTO>> map1 = value.stream().collect(Collectors.groupingBy(PartnerUpdatedDailyCountDTO::getCreateDate));
			for (Map.Entry<LocalDateTime, List<PartnerUpdatedDailyCountDTO>> dateEntry : map1.entrySet()) {
				LocalDateTime createDate = dateEntry.getKey();
				List<PartnerUpdatedDailyCountDTO> dailyData = dateEntry.getValue();

				String year = String.valueOf(createDate.getYear());
				String month = String.format("%02d", createDate.getMonthValue());
				String day = String.format("%02d", createDate.getDayOfMonth());
				String dateStr = year + "-" + month + "-" + day;

				log.info("重新统计合伙人[{}]在[{}]的数据", partnerId, dateStr);
				XxlJobHelper.log("重新统计合伙人[{}]在[{}]的数据", partnerId, dateStr);

				// 查询是否已存在记录
				StatisticsPartnerGoods statistics = getExistingStatistics(partnerId, year, month);

				// 解析并更新统计数据
				Map<String, Integer> dataMap = parseExistingData(statistics);

				// 将PartnerDailyStatisticsDTO列表转换为Map格式
				List<Map<String, Object>> convertedDailyData = dailyData.stream()
						.map(dto -> {
							Map<String, Object> dataUpdateMap = new HashMap<>();
							dataUpdateMap.put("day", dto.getCreateDate().getDayOfMonth());
							dataUpdateMap.put("count", dto.getCount());
							return dataUpdateMap;
						})
						.collect(Collectors.toList());
				int totalCount = updateStatisticsDataForRecalculate(dataMap, convertedDailyData);

				// 保存或更新统计记录
				saveOrUpdateStatistics(statistics, partnerId, year, month, dataMap, totalCount);

				log.info("合伙人[{}]在[{}]的数据重新统计完成", partnerId, dateStr);
				XxlJobHelper.log("合伙人[{}]在[{}]的数据重新统计完成", partnerId, dateStr);
			}

		}

		log.info("昨天更新的商品数据重新统计完成");
		XxlJobHelper.log("昨天更新的商品数据重新统计完成");
	}
}
