package com.yts.yyt.merchant.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.net.HttpHeaders;
import com.yts.yyt.admin.api.vo.MerchantPageVO;
import com.yts.yyt.common.core.dto.IdDTO;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.core.validation.UrlValidation;
import com.yts.yyt.common.idempotent.annotation.Idempotent;
import com.yts.yyt.common.log.annotation.SysLog;
import com.yts.yyt.common.security.annotation.HasPermission;
import com.yts.yyt.common.security.annotation.Inner;
import com.yts.yyt.common.security.util.SecurityUtils;
import com.yts.yyt.merchant.api.dto.*;
import com.yts.yyt.merchant.api.entity.CityPartner;
import com.yts.yyt.merchant.api.entity.MerchantEntity;
import com.yts.yyt.merchant.api.dto.UserRegisterSetUserIdDTO;
import com.yts.yyt.merchant.api.vo.MerchantActivationInfoVO;
import com.yts.yyt.merchant.api.vo.MerchantDetailVO;
import com.yts.yyt.merchant.api.vo.MerchantInfoDetailVO;
import com.yts.yyt.merchant.service.HuifuMerchantService;
import com.yts.yyt.merchant.service.MerchantService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/merchant")
@Tag(description = "MerchantController", name = "商家信息表")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class MerchantController {

	private final MerchantService merchantService;
	private final HuifuMerchantService huifuMerchantService;

	/**
	 * 分页查询所有数据
	 *
	 * @param page     分页对象
	 * @param dto 查询实体
	 * @return 所有数据
	 */
	@GetMapping("/page")
	@Operation(summary = "分页查询 权限: merchant_page", description = "分页查询")
	@HasPermission("merchant_page")
	public R<IPage<MerchantPageVO>> page(@ParameterObject Page<MerchantEntity> page, @ParameterObject MerchantQueryDTO dto) {
		return R.ok(this.merchantService.pageQuery(page, dto));
	}

	/**
	 * 通过主键查询单条数据
	 *
	 * @param id 主键
	 * @return 单条数据
	 */
	@GetMapping("/{id}")
	@Operation(summary = "通过主键查询单条数据 权限: merchant_detail", description = "通过主键查询单条数据")
	@HasPermission("merchant_detail")
	public R<MerchantInfoDetailVO> selectOne(@PathVariable Long id) {
		MerchantInfoDetailVO vo = this.merchantService.detail(id);
		return R.ok(vo);
	}

	/**
	 * 查询店铺详情
	 *
	 * @param dto 请求参数
	 * @return 单条数据
	 */
	@PostMapping("/detail")
	@Operation(summary = "查询店铺详情 买家视角: 传id值获取  卖家视角: 不用传,有token即可", description = "查询店铺详情 买家视角: 传id值获取  卖家视角: 不用传,有token即可")
	@Inner(false)
	public R<MerchantDetailVO> detail(@RequestBody IdDTO dto) {
		return R.ok(this.merchantService.detail(dto.getId()));
	}

	/**
	 * 新增数据
	 *
	 * @param dto 实体对象
	 * @return 新增结果
	 */
	@PostMapping
	@Operation(summary = "新增数据 权限: merchant_add", description = "新增数据")
	@SysLog("商家管理新增数据")
	@HasPermission("merchant_add")
	@UrlValidation
	public R<Boolean> add(@RequestBody @Validated MerchantAddDTO dto) {
		return R.ok(this.merchantService.add(dto));
	}

	/**
	 * 商家入驻
	 * @param dto
	 * @return
	 */
	@PostMapping("/merchantSettled")
	@Operation(summary = "商家入驻", description = "商家入驻")
	@SysLog("商家入驻")
	@Idempotent(key = "T(com.yts.yyt.common.security.util.SecurityUtils).getUser.getId()", expireTime = 3)
	public R<Boolean> merchantSettlement(@RequestBody @Validated MerchantSettledDTO dto) {
		return R.ok(this.merchantService.merchantSettlement(dto));
	}

	/**
	 * 商家入驻 被拒绝后修改重新提交
	 */
	@PostMapping("/merchantSettledUpdate")
	@Operation(summary = "商家入驻 被拒绝后修改重新提交", description = "商家入驻 被拒绝后修改重新提交")
	@SysLog("商家入驻重新提交审核")
	@Idempotent(key = "T(com.yts.yyt.common.security.util.SecurityUtils).getUser.getId()", expireTime = 3)
	public R<Boolean> merchantSettledUpdate(@RequestBody @Validated MerchantSettledUpdateDTO dto) {
		return R.ok(this.merchantService.merchantSettledUpdate(dto));
	}

	/**
	 * 后台-审核
	 */
	@PostMapping("/audit")
	@Operation(summary = "后台-审核商家入驻 权限: merchant_audit", description = "后台-审核商家入驻 权限: merchant_audit")
	@SysLog("后台审核商家入驻")
	@HasPermission("merchant_audit")
	@Idempotent(key = "T(com.yts.yyt.common.security.util.SecurityUtils).getUser.getId()", expireTime = 3)
	public R<Boolean> audit(@RequestBody @Validated MerchantAuditDTO dto) {
		return R.ok(this.merchantService.audit(dto));
	}

	/**
	 * 修改数据
	 *
	 * @param dto 实体对象
	 * @return 修改结果
	 */
	@PutMapping
	@Operation(summary = "商家管理修改数据 权限: merchant_update", description = "商家管理修改数据")
	@SysLog("商家管理修改数据")
	@HasPermission("merchant_update")
	public R<Boolean> update(@RequestBody MerchantModifyDTO dto) {
		return R.ok(this.merchantService.updateEntity(dto));
	}

	/**
	 * 移动端-修改店铺数据
	 */
	@PostMapping("/update")
	@Operation(summary = "移动端-修改店铺数据", description = "移动端-修改店铺数据")
	@SysLog("移动端修改店铺数据")
	@Idempotent(key = "T(com.yts.yyt.common.security.util.SecurityUtils).getUser.getId()", expireTime = 3)
	@UrlValidation
	public R<Boolean> update(@RequestBody @Validated MerchantUpdateDTO dto) {
		return R.ok(this.merchantService.update(dto));
	}

	/**
	 * 发送账号
	 */
	@GetMapping("/sendAccount")
	@Operation(summary = "发送账号", description = "发送账号")
	@SysLog("商家管理管理发送账号")
	@HasPermission("merchant_send")
	public R<Boolean> sendAccount(@RequestParam Long id) {
		this.merchantService.sendAccount(id);
		return R.ok();
	}

	/**
	 * 查询合伙人下的商家
	 */
	@GetMapping("/getMerchant")
	@Operation(summary = "查询合伙人下属商家", description = "根据合伙人id查询合伙人下的商家")
	public R<List<MerchantEntity>> getMerchantByPartnerSysUserId() {
		return R.ok(this.merchantService.getMerchantByPartnerSysUserId(SecurityUtils.getUser().getId()));
	}

//	/**
//	 * 删除数据
//	 *
//	 * @param idList 主键结合
//	 * @return 删除结果
//	 */
//	@DeleteMapping
//	@Operation(summary = "删除数据 权限: merchant_delete", description = "删除数据")
//	@SysLog("商家管理删除数据")
//	@HasPermission("merchant_delete")
//	public R<Boolean> delete(@RequestParam("idList") List<Long> idList) {
//		return R.ok(this.merchantService.lambdaUpdate().set(MerchantEntity::getDelFlag, DeletedEnum.DELETED.getType()).in(MerchantEntity::getId,idList).update());
//	}

    /**
     * Feign 根据id获取商户合伙人信息
     * @param idDTO
     * @return
     */
    @Inner
    @PostMapping("/getPartnerByMerchantId")
    @SysLog("根据id获取商户合伙人信息")
    R<CityPartnerAndMerchantDTO> getPartnerByMerchantId(@RequestBody IdDTO idDTO){
        return R.ok(this.merchantService.getPartnerByMerchantId(Long.valueOf(idDTO.getId())));
    }
    /**
     * Feign 根据系统用户id获取商户合伙人信息
     * @param idDTO
     * @return
     */
    @Inner
    @PostMapping("/getPartnerBySysUserId")
    @SysLog("根据系统用户id获取商户合伙人信息")
    R<CityPartnerAndMerchantDTO> getPartnerBySysUserId(@RequestBody IdDTO idDTO){
        return R.ok(this.merchantService.getPartnerBySysUserId(Long.valueOf(idDTO.getId())));
    }

    /**
     * Feign 根据区域id获取合伙人信息
     * @param idDTO
     * @return
     */
    @Inner
    @PostMapping("/getPartnerByRegionId")
    @SysLog("根据区域id获取合伙人信息")
    R<List<CityPartner>> getPartnerByRegionId(@RequestBody IdDTO idDTO){
        return R.ok(this.merchantService.getPartnerByRegionId(Long.valueOf(idDTO.getId())));
    }
    /**
     * Feign 根据移动端用户id获取商户信息
     */
    @Inner
    @PostMapping("/getMerchantByUserId")
    @SysLog("根据移动端用户id获取商户信息")
    R<MerchantDTO> getMerchantByUserId(@RequestBody IdDTO idDTO){
        return R.ok(this.merchantService.getMerchantDTOByUserId(Long.valueOf(idDTO.getId())));
    }

	/**
	 * Feign 移动端注册时,查询是否有合伙人账号或者商家账号,若有则将移动端id设置到该账号user_id中
	 * @param dto 请求参数: 移动端用户id and 手机号
	 * @return 成功与否
	 */
    @Inner
    @PostMapping("/bindMobileUserToAccounts")
    @SysLog("绑定移动端用户到合伙人或商家")
    R<Boolean> bindMobileUserToAccounts(@RequestBody UserRegisterSetUserIdDTO dto){
        return R.ok(this.merchantService.bindMobileUserToAccounts(dto));
    }

	/**
	 * 根据系统用户ID获取商户ID列表
	 * @param sysUserId 系统用户ID
	 * @return 商户ID列表
	 */
	@Inner
	@GetMapping("/getMerchantIdsBySysUserId")
	@SysLog("根据系统用户ID获取商户ID列表")
	public R<List<Long>> getMerchantIdsBySysUserId(@RequestParam Long sysUserId) {
		return R.ok(this.merchantService.getMerchantIdsBySysUserId(sysUserId));
	}

	/**
	 * 商家进件激活
	 * @param dto 商家ID dto
	 * @return 激活结果
	 */
	@PostMapping("/activate")
	@Operation(summary = "商家进件激活 权限: merchant_activate", description = "商家进件激活 参数为商家id,放字段id上")
	@SysLog("商家进件激活")
	public R<Boolean> activateMerchant(@RequestBody IdDTO dto) {
		return R.ok(this.huifuMerchantService.activateMerchant(Long.parseLong(dto.getId())));
	}

	/**
	 * 检查商家激活信息是否填写完整
	 * @return 商家激活信息VO
	 */
	@PostMapping("/checkActivationInfo")
	@Operation(summary = "检查商家激活信息是否填写完整", description = "检查商家激活信息是否填写完整")
	public R<MerchantActivationInfoVO> checkActivationInfo() {
		return R.ok(this.huifuMerchantService.checkActivationInfo());
	}
}

