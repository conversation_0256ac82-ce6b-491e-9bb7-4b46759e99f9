package com.yts.yyt.merchant.mq.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@AllArgsConstructor
public class DepositOrderTransferMqDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long orderId;
    private BigDecimal amount;
    private Long userId;

    public DepositOrderTransferMqDTO() {
    }
}
