package com.yts.yyt.merchant.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yts.yyt.merchant.api.dto.IncentiveCommissionLogAddDTO;
import com.yts.yyt.merchant.api.entity.IncentiveCommissionLog;

public interface IncentiveCommissionLogService extends IService<IncentiveCommissionLog> {
    /**
     * 保存(付款登记)
     * @param dto
     * @return
     */
    boolean saveLog(IncentiveCommissionLogAddDTO dto,byte type);
}

