package com.yts.yyt.merchant.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yts.yyt.merchant.api.dto.*;
import com.yts.yyt.merchant.api.entity.MerchantDepositOrderEntity;
import com.yts.yyt.merchant.api.vo.MerchantDepositOrderListVO;
import com.yts.yyt.merchant.api.vo.MerchantDepositOrderPayParamVO;
import com.yts.yyt.merchant.api.vo.MerchantDepositOrderVO;
import com.yts.yyt.merchant.api.vo.UserDepositRepayAmountVO;
import com.yts.yyt.merchant.mq.dto.DepositOrderTransferMqDTO;

import java.math.BigDecimal;

public interface MerchantDepositOrderService extends IService<MerchantDepositOrderEntity> {

    /**
     * 获取支付参数
     * @param dto 入参
     * @return MerchantDepositOrderPayParamDTO
     */
    MerchantDepositOrderPayParamVO getPayParam(MerchantDepositPayDTO dto);

    /**
     * 获取消保金补缴金额
     * @return UserDepositRepayAmountVO
     */
    UserDepositRepayAmountVO getRepayAmount(String depositKey);

    /**
     * 消保金支付回调
     * @return Boolean
     */
    Boolean payDepositCallBack(MerchantDepositOrderPayCallbackDTO dto);

    /**
     * 消保金退回订单回调
     * @param dto
     * @return
     */
    Boolean depositRefundCallBack(MerchantDepositOrderRefundCallbackDTO dto);
    /**
     * 列表查询
     * @param dto
     * @return
     */
    Page<MerchantDepositOrderVO> page(MerchantDepositOrderQueryDTO dto);


    /**
     * 转出消保金
     * @param amount 转出金额
     * @param userId 用户ID
     * @return 转出结果
     */
    Boolean transferDeposit(BigDecimal amount, Long userId);

    /**
     *  消保金转出三方支付
     * @param dto
     * @return
     */
    Boolean tradeAcctpayment(DepositOrderTransferMqDTO dto);

    /**
     *  消保金转出业务逻辑
     * @param orderId
     * @param amount
     */
    void doTransferDepositBusiness(Long orderId, BigDecimal amount);

    /**
     * 消保金订单列表查询
     * @param dto 查询条件
     * @return 分页结果
     */
    Page<MerchantDepositOrderListVO> listMerchantDepositOrders(MerchantDepositOrderListQueryDTO dto);
}

