package com.yts.yyt.distribution.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yts.yyt.admin.api.entity.SysDictItem;
import com.yts.yyt.admin.api.feign.RemoteDictService;
import com.yts.yyt.common.core.util.IDS;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.security.util.SecurityUtils;
import com.yts.yyt.distribution.api.dto.DistLotApplyAddDTO;
import com.yts.yyt.distribution.api.dto.DistLotAuditDTO;
import com.yts.yyt.distribution.api.dto.DistLotInfoApplyDTO;
import com.yts.yyt.distribution.api.dto.DistLotInfoAuditDTO;
import com.yts.yyt.distribution.api.dto.DistLotInfoBatchDTO;
import com.yts.yyt.distribution.api.dto.DistLotInfoPageDTO;
import com.yts.yyt.distribution.api.dto.DistLotInfoUpdateDTO;
import com.yts.yyt.distribution.api.enums.AuditStatusEnum;
import com.yts.yyt.distribution.api.enums.DistEnableStatusEnum;
import com.yts.yyt.distribution.api.enums.HasSharedStatusEnum;
import com.yts.yyt.distribution.api.enums.WaitAuditStatusEnum;
import com.yts.yyt.distribution.api.exception.DistributionErrorEnum;
import com.yts.yyt.distribution.api.exception.DistributionException;
import com.yts.yyt.distribution.api.vo.DistLotInfoExportVO;
import com.yts.yyt.distribution.api.vo.DistLotInfoPageVO;
import com.yts.yyt.distribution.api.vo.DistLotInfoStatisticsVO;
import com.yts.yyt.distribution.entity.DistLotDistApplyEntity;
import com.yts.yyt.distribution.mapper.DistLotInfoMapper;
import com.yts.yyt.distribution.entity.DistLotInfoEntity;
import com.yts.yyt.distribution.mq.dto.LotStatusSyncMqDTO;
import com.yts.yyt.distribution.service.DistLotInfoService;
import com.yts.yyt.distribution.service.DistLotDistApplyService;
import com.yts.yyt.distribution.service.DistLotDistAuditService;
import com.yts.yyt.distribution.service.BaseRemoteService;
import com.yts.yyt.merchant.api.enums.DictEnum;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.seata.spring.annotation.GlobalTransactional;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 商品分销管理列表(DistLotInfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-20 10:13:59
 */
@Slf4j
@Service("DistLotInfoService")
@RequiredArgsConstructor
public class DistLotInfoServiceImpl extends ServiceImpl<DistLotInfoMapper, DistLotInfoEntity> implements DistLotInfoService {

    private final DistLotDistApplyService distLotDistApplyService;
    private final DistLotDistAuditService distLotDistAuditService;
    private final BaseRemoteService baseRemoteService;
    private final RemoteDictService remoteDictService;

    @Override
    public IPage<DistLotInfoPageVO> pageQuery(DistLotInfoPageDTO dto) {
        // 应用权限过滤
        applyPermissionFilter(dto);
        Page<DistLotInfoEntity> page = new Page<>(dto.getCurrent(), dto.getSize());
        return baseMapper.pageQuery(page, dto);
    }

    /**
     * 查询配置字典，是否允许查询全部藏品数据
     */
    public Boolean getSelectRoleData() {
        boolean selectBool = false;
        R<List<SysDictItem>> dict =
                remoteDictService.getInnerDictByType(DictEnum.SELECT_GOODS_ROLE.getCode());
        List<Long> roleIds = SecurityUtils.getRoleIds();
        List<SysDictItem> dictItemList = dict.getData();
        for (SysDictItem sysDictItem : dictItemList) {
            for (Long roleId : roleIds) {
                if (roleId.equals(Long.valueOf(sysDictItem.getItemValue()))) {
                    selectBool = true;
                    break;
                }
            }
        }
        return selectBool;
    }

    /**
     * 应用权限过滤，如果不是管理员等角色，则只能查看相关商户的藏品数据
     * 
     * @param dto 查询参数DTO，会被修改添加merchantIds过滤条件
     */
    private void applyPermissionFilter(DistLotInfoPageDTO dto) {
        if (!getSelectRoleData()) {
            // 根据登录用户查询商户信息
            List<Long> merchantIds = baseRemoteService.getMerchantIdsBySysUserId(SecurityUtils.getUser().getId());
            if (CollUtil.isEmpty(merchantIds)) {
                log.info("【applyPermissionFilter】未查询到相关商户信息，用户ID：{}", SecurityUtils.getUser().getId());
                // 设置一个不存在的商户ID，确保查询结果为空
                dto.setMerchantIds(List.of(-1L));
                return;
            }
            dto.setMerchantIds(merchantIds);
            log.debug("【applyPermissionFilter】应用商户权限过滤，商户ID列表：{}", merchantIds);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean batchOperation(DistLotInfoBatchDTO dto) {
        // 1. 更新本地分销状态
        int updateCount = baseMapper.batchUpdateDistStatus(dto.getLotIds(), dto.getOperation());
        if (updateCount <= 0) {
            log.error("批量操作拍品分销状态失败，操作类型：{}，拍品数量：{}，请稍后重试", 
                    dto.getOperation(), dto.getLotIds().size());
            throw DistributionException.build(DistributionErrorEnum.OPERATION_FAIL);
        }
        log.info("批量操作拍品分销状态完成，更新{}条记录", updateCount);
        
        // 2. 通过feign调用远程服务更新goods_lot_info表中的分销状态
        Boolean remoteResult = baseRemoteService.updateGoodsLotDistState(dto.getLotIds(), dto.getOperation());
        if (!remoteResult) {
            log.error("远程更新拍品分销状态失败，拍品ID列表：{}，操作类型：{}", dto.getLotIds(), dto.getOperation());
            throw DistributionException.build(DistributionErrorEnum.REMOTE_CALL_ERROR);
        }
        log.info("远程更新拍品分销状态成功，拍品数量：{}，操作类型：{}", dto.getLotIds().size(), dto.getOperation());
        
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean auditDistribution(DistLotInfoAuditDTO dto) {
        // 1. 参数校验和数据准备
        AuditContext context = validateAndPrepareAudit(dto);
        // 2. 处理审批流程
        processAuditFlow(context);
        // 3. 更新本地状态
        updateLocalDistributionStatus(context);
        log.info("拍品分销审批完成，拍品ID：{}，审批结果：{}", dto.getLotId(), dto.getAuditResult());
        return true;
    }

    /**
     * 校验参数并准备审批上下文
     */
    private AuditContext validateAndPrepareAudit(DistLotInfoAuditDTO dto) {
        // 校验审批结果是否有效
        if (!AuditStatusEnum.isValid(dto.getAuditResult())) {
            log.error("无效的审批结果：{}", dto.getAuditResult());
            throw DistributionException.build(DistributionErrorEnum.PARAM_ERROR);
        }
        // 查询拍品分销管理实体
        DistLotInfoEntity entity = this.getOne(new LambdaQueryWrapper<DistLotInfoEntity>().eq(DistLotInfoEntity::getLotId, dto.getLotId()));
        if (entity == null) {
            log.error("拍品不存在，ID：{}", dto.getLotId());
            throw DistributionException.build(DistributionErrorEnum.GOODS_LOT_NOT_FOUND);
        }
        // 查找待审批的申请记录
        DistLotDistApplyEntity applyEntity = findPendingApply(dto.getLotId());
        return new AuditContext(dto, entity, applyEntity);
    }

    /**
     * 查找待审批的申请记录
     */
    private DistLotDistApplyEntity findPendingApply(Long lotId) {
        LambdaQueryWrapper<DistLotDistApplyEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DistLotDistApplyEntity::getLotId, lotId)
                   .orderByDesc(DistLotDistApplyEntity::getCreateTime)
                   .last("LIMIT 1");
        DistLotDistApplyEntity applyEntity = distLotDistApplyService.getOne(queryWrapper);
        if (applyEntity == null) {
            throw DistributionException.build(DistributionErrorEnum.APPLY_NOT_FOUND);
        }
        return applyEntity;
    }

    /**
     * 处理审批流程
     */
    private void processAuditFlow(AuditContext context) {
        DistLotAuditDTO auditDTO = new DistLotAuditDTO();
        auditDTO.setApplyId(context.getApplyEntity().getId());
        auditDTO.setStatus(context.getDto().getAuditResult());
        boolean auditResult = distLotDistAuditService.auditApply(auditDTO);
        if (!auditResult) {
            throw DistributionException.build(DistributionErrorEnum.AUDIT_FAILED);
        }
    }

    /**
     * 更新本地分销状态
     */
    private void updateLocalDistributionStatus(AuditContext context) {
        String action = context.getApplyEntity().getAction();
        DistLotInfoEntity entity = context.getEntity();
        entity.setDistEnable(determineDistStatus(action,context.getDto().getAuditResult()));
        entity.setWaitAudit(WaitAuditStatusEnum.NO_WAIT_AUDIT.getStatus());
        entity.setUpdateTime(LocalDateTime.now());
        boolean result = this.updateById(entity);
        if (!result) {
            throw DistributionException.build(DistributionErrorEnum.AUDIT_FAILED);
        }
    }

    /**
     * 根据申请动作确定拍品分销状态
     * 如果通过取消分销,则拍品分销状态为取消分销
     * 如果通过恢复分销,则拍品分销状态为开启分销
     * 如果拒绝取消分销,则拍品分销状态为开启分销
     * 如果拒绝恢复分销,则拍品分销状态为取消分销
     *
     * @param action      申请动作：cancel=取消分销 recovery=恢复分销
     * @param auditResult 审批结果：1=通过 2=不通过 
     * @return 分销状态：0=取消分销 1=开启分销
     */
    private Integer determineDistStatus(String action, String auditResult) {
        if ("cancel".equals(action) && AuditStatusEnum.APPROVE.getCode().equals(auditResult)) {
            return DistEnableStatusEnum.NOT_DIST.getStatus(); // 取消分销
        } else if ("recovery".equals(action) && AuditStatusEnum.APPROVE.getCode().equals(auditResult)) {
            return DistEnableStatusEnum.DIST_ENABLED.getStatus(); // 恢复分销
        } else if ("cancel".equals(action) && AuditStatusEnum.REJECT.getCode().equals(auditResult)) {
            return DistEnableStatusEnum.DIST_ENABLED.getStatus(); // 拒绝取消分销
        } else if ("recovery".equals(action) && AuditStatusEnum.REJECT.getCode().equals(auditResult)) {
            return DistEnableStatusEnum.NOT_DIST.getStatus(); // 拒绝恢复分销
        } else {
            log.error("未知的申请动作: {}", action);
            throw DistributionException.build(DistributionErrorEnum.PARAM_ERROR);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean applyDistribution(DistLotInfoApplyDTO dto) {
        log.info("开始批量申请分销，拍品数量：{}，申请动作：{}", dto.getLotIds().size(), dto.getAction());
        // 1. 批量验证拍品是否存在
        List<DistLotInfoEntity> entities = this.list(new LambdaQueryWrapper<DistLotInfoEntity>()
            .in(DistLotInfoEntity::getLotId, dto.getLotIds()));
        if (entities.isEmpty()) {
            log.error("所有拍品都不存在，无法进行分销申请");
            throw DistributionException.build(DistributionErrorEnum.GOODS_LOT_NOT_FOUND);
        }
        // 获取存在的拍品ID列表
        List<Long> existingLotIds = entities.stream().map(DistLotInfoEntity::getLotId).toList();
        log.info("实际处理拍品数量：{}", existingLotIds.size());
        // 2. 批量创建申请记录
        for (Long lotId : existingLotIds) {
            // 创建申请记录
            DistLotApplyAddDTO applyDTO = new DistLotApplyAddDTO();
            applyDTO.setLotId(lotId);
            applyDTO.setAction(dto.getAction());
            // 提交申请记录
            distLotDistApplyService.submitApply(applyDTO);
        }
        // 3. 批量更新拍品的待审批状态
        LocalDateTime updateTime = LocalDateTime.now();
        entities.forEach(entity -> {
            entity.setWaitAudit(WaitAuditStatusEnum.WAIT_AUDIT.getStatus());
            entity.setUpdateTime(updateTime);
        });
        boolean updateResult = this.updateBatchById(entities);
        if (!updateResult) {
            log.error("批量更新拍品待审批状态失败，拍品数量：{}", entities.size());
            throw DistributionException.build(DistributionErrorEnum.APPLY_FAILED);
        }
        log.info("批量申请分销完成，请求总数：{}，实际处理：{}", dto.getLotIds().size(), existingLotIds.size());
        return true;
    }

    @Override
    public List<DistLotInfoExportVO> exportList(DistLotInfoPageDTO dto) {
        log.info("导出拍品分销管理列表开始，查询条件：{}", dto);
        // 设置分页参数为获取所有数据
        dto.setCurrent(1L);
        dto.setSize(Long.MAX_VALUE);
        // 分页查询所有数据
        IPage<DistLotInfoPageVO> result = this.pageQuery(dto);
        // 转换为导出VO格式
        List<DistLotInfoExportVO> exportData = result.getRecords().stream().map(item -> {
            DistLotInfoExportVO vo = new DistLotInfoExportVO();
            // 复制基本属性
            vo.setLotName(item.getLotName());
            vo.setLotCode(item.getLotCode());
            vo.setMerchantName(item.getMerchantName());
            vo.setSalePrice(item.getSalePrice());
            vo.setLinkViewCnt(item.getLinkViewCnt());
            return vo;
        }).collect(Collectors.toList());
        log.info("导出拍品分销管理列表成功，共{}条记录", exportData.size());
        return exportData;
    }

    @Override
    public DistLotInfoStatisticsVO getStatistics(DistLotInfoPageDTO dto) {
        log.info("获取拍品分销状态统计开始，查询条件：{}", dto);
        // 应用权限过滤
        applyPermissionFilter(dto);
        DistLotInfoStatisticsVO statistics = baseMapper.getStatistics(dto);
        log.info("获取拍品分销状态统计完成：{}", statistics);
        return statistics;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncLotStatus(LotStatusSyncMqDTO dto) {
        log.info("开始同步拍品状态，拍品ID：{}，同步类型：{}，状态：{}", dto.getLotId(), dto.getSyncType(), dto.getLotState());
        LotStatusSyncMqDTO.SyncType syncType = LotStatusSyncMqDTO.SyncType.getByCode(dto.getSyncType());
        if (syncType == null) {
            log.error("未知的同步类型：{}", dto.getSyncType());
            return;
        }
        switch (syncType) {
            case STATE_UPDATE:
                handleStateUpdate(dto);
                break;
            case LOT_ADD:
                handleLotAdd(dto);
                break;
            default:
                log.warn("不支持的同步类型：{}", syncType);
        }
        log.info("拍品状态同步完成，拍品ID：{}，同步类型：{}", dto.getLotId(), syncType.getDesc());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLotInfo(DistLotInfoUpdateDTO dto) {
        log.info("开始更新拍品分销信息，拍品数量：{}", dto.getLotIds().size());
        // 构建批量更新条件
        LambdaUpdateWrapper<DistLotInfoEntity> updateWrapper = new LambdaUpdateWrapper<DistLotInfoEntity>()
                .in(DistLotInfoEntity::getLotId, dto.getLotIds())
                .set(dto.getSalePrice() != null, DistLotInfoEntity::getSalePrice, dto.getSalePrice())
                .set(dto.getLotName() != null && !dto.getLotName().trim().isEmpty(), DistLotInfoEntity::getLotName, dto.getLotName())
                .set(DistLotInfoEntity::getUpdateTime, LocalDateTime.now());
        // 执行批量更新
        boolean result = this.update(updateWrapper);
        if (!result) {
            log.warn("批量更新拍品分销信息失败，拍品数量：{}", dto.getLotIds().size());
        } else {
            log.info("批量更新拍品分销信息成功，拍品数量：{}", dto.getLotIds().size());
        }
        return result;
    }

    /**
     * 处理拍品上下架状态更新
     */
    private void handleStateUpdate(LotStatusSyncMqDTO dto) {
        LambdaUpdateWrapper<DistLotInfoEntity> updateWrapper = new LambdaUpdateWrapper<DistLotInfoEntity>()
                .eq(DistLotInfoEntity::getLotId, dto.getLotId())
                .set(DistLotInfoEntity::getLotState, dto.getLotState())
                .set(DistLotInfoEntity::getDistEnable, dto.getDistEnable());
        this.update(updateWrapper);
        // 同步到ES
        syncToES(dto.getLotId());
        log.info("拍品上下架状态更新完成，拍品ID：{}，状态：{}", dto.getLotId(), dto.getLotState());
    }

    /**
     * 处理新增拍品
     */
    private void handleLotAdd(LotStatusSyncMqDTO dto) {
        // 检查是否已存在
        DistLotInfoEntity existingEntity = this.getOne(new LambdaQueryWrapper<DistLotInfoEntity>()
                .eq(DistLotInfoEntity::getLotId, dto.getLotId()));
        if (existingEntity != null) {
            log.info("拍品已存在，跳过新增，拍品ID：{}", dto.getLotId());
            return;
        }
        DistLotInfoEntity entity = new DistLotInfoEntity();
        entity.setId(IDS.uniqueID());
        entity.setLotId(dto.getLotId());
        entity.setLotName(dto.getLotName());
        entity.setLotCode(dto.getLotCode());
        entity.setLotImg(dto.getLotImg());
        entity.setMerchantId(dto.getMerchantId());
        entity.setSalePrice(dto.getSalePrice());
        entity.setLotState(dto.getLotState());
        entity.setDistEnable(dto.getDistEnable() != null ? dto.getDistEnable() : DistEnableStatusEnum.NOT_DIST.getStatus());
        entity.setHasShared(HasSharedStatusEnum.NOT_SHARED.getStatus());
        entity.setWaitAudit(WaitAuditStatusEnum.NO_WAIT_AUDIT.getStatus());
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        this.save(entity);
        log.info("新增拍品完成，拍品ID：{}，状态：{}", dto.getLotId(), dto.getLotState());
    }

    /**
     * 同步到ES
     */
    private void syncToES(Long lotId) {
      // todo 同步到ES
    }

    /**
     * 审批上下文类，封装审批过程中需要的数据
     */
    @Getter
    private static class AuditContext {
        private final DistLotInfoAuditDTO dto;
        private final DistLotInfoEntity entity;
        private final DistLotDistApplyEntity applyEntity;
        @Setter
        private Integer distEnable;

        public AuditContext(DistLotInfoAuditDTO dto, DistLotInfoEntity entity, DistLotDistApplyEntity applyEntity) {
            this.dto = dto;
            this.entity = entity;
            this.applyEntity = applyEntity;
        }
    }

}

