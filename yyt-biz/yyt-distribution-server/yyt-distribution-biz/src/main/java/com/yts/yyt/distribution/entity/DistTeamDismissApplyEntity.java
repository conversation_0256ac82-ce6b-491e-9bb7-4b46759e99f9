package com.yts.yyt.distribution.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@TableName("dist_team_dismiss_apply")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "团队解散申请表")
public class DistTeamDismissApplyEntity  extends Model<DistTeamDismissApplyEntity>  {

    /**
     * ID（雪花ID）
     */
    @Schema(description="ID（雪花ID）")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 团队 ID，对应 dist_team.id
     */
    @Schema(description="团队 ID，对应 dist_team.id")
    private Long teamId;

    /**
     * 团长用户 ID
     */
    @Schema(description="团长用户 ID")
    private Long leaderId;

    /**
     * 申请解散原因
     */
    @Schema(description="申请解散原因")
    private String dismissReason;

    /**
     * 审核状态
     * @see com.yts.yyt.distribution.api.constants.TeamDismissStatusEnum
     */
    @Schema(description="审核状态")
    private String status;

    @Schema(description="申请金额")
    private BigDecimal amount;

    /**
     * 申请提交时间
     */
    @Schema(description="申请提交时间")
    private LocalDateTime applyTime;

    @Schema(description="创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description="更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Schema(description="逻辑删除：0 正常 1 删除")
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String delFlag;

}

