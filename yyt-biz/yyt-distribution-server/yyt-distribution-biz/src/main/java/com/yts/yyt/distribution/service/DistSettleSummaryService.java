package com.yts.yyt.distribution.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yts.yyt.distribution.api.dto.CommissionSettlementPageDTO;
import com.yts.yyt.distribution.api.vo.CommissionSettlementPageVO;
import com.yts.yyt.distribution.api.vo.CommissionSettlementSummaryVO;
import com.yts.yyt.distribution.entity.DistSettleSummaryEntity;

/**
 * 分销员累计业绩汇总表服务接口
 *
 * <AUTHOR>
 * @since 2025-01-29
 */
public interface DistSettleSummaryService extends IService<DistSettleSummaryEntity> {

    /**
     * 分页查询业绩结算
     *
     * @param page 分页参数
     * @param dto 查询条件
     * @return 分页结果
     */
    IPage<CommissionSettlementPageVO> pageQuery(Page<CommissionSettlementPageVO> page, CommissionSettlementPageDTO dto);

    /**
     * 获取业绩结算合计数据
     *
     * @param dto 查询条件
     * @return 合计数据
     */
    CommissionSettlementSummaryVO getSummary(CommissionSettlementPageDTO dto);

    /**
     * 执行定时结算任务
     * 从 dist_order_commission 表聚合数据到 dist_settle_summary 表
     *
     * @return 处理的分销员数量
     */
    int executeSettleTask();
}

