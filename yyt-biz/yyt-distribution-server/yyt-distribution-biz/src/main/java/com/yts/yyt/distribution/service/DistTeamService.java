package com.yts.yyt.distribution.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yts.yyt.distribution.api.dto.TeamManagePageDTO;
import com.yts.yyt.distribution.api.dto.TeamStatusUpdateDTO;
import com.yts.yyt.distribution.api.vo.TeamManageExportVO;
import com.yts.yyt.distribution.api.vo.TeamManageVO;
import com.yts.yyt.distribution.entity.DistTeamEntity;

/**
 * 分销团队主表(DistTeam)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-17 17:57:18
 */
public interface DistTeamService extends IService<DistTeamEntity> {

    /**
     * 根据用户ID获取启用的团队信息
     * @param userId
     * @return
     */
    DistTeamEntity getEnableTeamByUserId(Long userId);

    /**
     * 团队管理分页查询
     */
    IPage<TeamManageVO> pageTeamManage(TeamManagePageDTO dto);

    /**
     * 团队管理导出
     */
    List<TeamManageExportVO> exportTeamManage(TeamManagePageDTO dto);

    /**
     * 更新团队状态
     */
    boolean updateTeamStatus(TeamStatusUpdateDTO dto);
}

