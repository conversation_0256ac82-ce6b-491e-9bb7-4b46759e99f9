package com.yts.yyt.distribution.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yts.yyt.common.core.constant.CommonConstants;
import com.yts.yyt.common.core.constant.DelFlagConstants;
import com.yts.yyt.distribution.api.constants.TeamStatusEnum;
import com.yts.yyt.distribution.api.dto.DistLevelRuleQueryDTO;
import com.yts.yyt.distribution.api.enums.DistRoleEnum;
import com.yts.yyt.distribution.api.vo.DistLevelRuleVO;
import com.yts.yyt.distribution.api.vo.DistUserLevelVO;
import com.yts.yyt.distribution.entity.DistTeamEntity;
import com.yts.yyt.distribution.entity.DistTeamMemberEntity;
import com.yts.yyt.distribution.entity.DistTeamStatisticsEntity;
import com.yts.yyt.distribution.entity.DistUserLevelEntity;
import com.yts.yyt.distribution.mapper.DistUserLevelMapper;
import com.yts.yyt.distribution.service.*;
import io.swagger.models.auth.In;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 用户等级表(DistUserLevel)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-17 17:57:18
 */
@Service("distUserLevelService")
@RequiredArgsConstructor
@Slf4j
public class DistUserLevelServiceImpl extends ServiceImpl<DistUserLevelMapper, DistUserLevelEntity> implements DistUserLevelService {

    private final DistTeamMemberService distTeamMemberService;
	private final DistLevelRuleCacheService distLevelRuleCacheService;
	private final DistTeamStatisticsService teamStatisticsService;
	private final DistTeamService distTeamService;
    @Override
    public DistUserLevelVO getUserLevelByUserId(Long userId) {
        //查询用户角色
        String role = distTeamMemberService.getUserDistributionRole(userId);
        //查询用户等级
        return getUserLevelByUserIdAndRole(userId, role);
    }

    @Override
    public DistUserLevelVO getUserLevelByUserIdAndRole(Long userId, String role) {
        DistUserLevelEntity entity = getUserLevel(userId, role);
        if (ObjUtil.isNull(entity)) {
            return null;
        }
        DistUserLevelVO vo = BeanUtil.copyProperties(entity, DistUserLevelVO.class);
        // 查询个人佣金比例
        BigDecimal personalCommissionRate = getPersonalCommissionRate(role, entity.getLevelCode());
        vo.setPersonalCommissionRate(personalCommissionRate);
        return vo;
    }

	@Override
	public DistUserLevelEntity getAndUpgradeUserLevel(Long userId, String role,Long teamId){
		upgradeUserLevel(userId, role, teamId);
		return getUserLevel(userId, role);
	}

	@Override
	public void upgradeUserLevel(Long userId, String role,Long teamId) {
		log.info("[计算用户等级],userId:{},role:{},teamId:{}", userId, role, teamId);
		// 配置规则
		List<DistLevelRuleVO> rules = distLevelRuleCacheService.queryList();
		DistTeamEntity team = distTeamService.getById(teamId);
		// 计算团队等级
		DistLevelRuleVO teamRule = calTeamRule(teamId,rules,team.getUserId());
		log.info("[计算团队等级] ------>>>teamId:{},rules:{},teamRule:{}", teamId,rules, teamRule);
//		if(!TeamStatusEnum.NORMAL.getStatus().equals(team.getStatus())){
//			log.info("[计算用户等级] 过滤团长等级计算，团长状态非正常，订单ID:{}", teamId);
//		}else {
//		}
		DistUserLevelEntity teamLevel = getUserLevel(team.getUserId(), DistRoleEnum.LEADER.getCode());
		log.info("[计算团队等级] ------>>> 结果：teamLevel:{}",  teamLevel);
		if(!ObjectUtil.equals(teamLevel.getLevelCode(),teamRule.getLevelCode())) {
			teamLevel.setLevelCode(teamRule.getLevelCode());
			teamLevel.setLevelName(teamRule.getLevelName());
			updateById(teamLevel);
		}
		// 计算用户等级
		if(role.equals(DistRoleEnum.MEMBER.getCode())) {
			DistLevelRuleVO userRule = calUserRule(teamId,userId,rules);
			log.info("[计算用户等级] 计算用户等级,userId:{},role:{},teamId:{},userRule:{}", userId, role, teamId, JSON.toJSON(userRule));
			DistUserLevelEntity userLevel = getUserLevel(userId, role);
			if(!ObjUtil.equals(userRule.getLevelCode(),userLevel.getLevelCode())){
				userLevel.setLevelCode(userRule.getLevelCode());
				userLevel.setLevelName(userRule.getLevelName());
				updateById(userLevel);
			}
		}
	}


	@Override
	public void getOrCreateUserLevel(Long userId, String role) {
		LambdaQueryWrapper<DistUserLevelEntity> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(DistUserLevelEntity::getUserId, userId)
				.eq(DistUserLevelEntity::getRole, role)
				.eq(DistUserLevelEntity::getDelFlag, CommonConstants.STATUS_NORMAL);
		DistUserLevelEntity entity = this.getOne(wrapper);
		if (entity != null) {
			return;
		}
		// 不存在则创建
		entity = new DistUserLevelEntity()
				.setUserId(userId)
				.setRole(role)
				.setLevelCode(1)
				.setLevelName("初级")
				.setDelFlag(CommonConstants.STATUS_NORMAL);
		this.save(entity);
	}

	private DistUserLevelEntity getUserLevel(Long userId, String role) {
		return getOne(new LambdaQueryWrapper<>(DistUserLevelEntity.class)
				.eq(DistUserLevelEntity::getUserId, userId)
				.eq(DistUserLevelEntity::getRole, role));
	}

	/**
	 * 计算用户等级
	 * @param teamId
	 * @param userId
	 * @param rules
	 * @return
	 */
	private DistLevelRuleVO calUserRule(Long teamId,Long userId,List<DistLevelRuleVO> rules){
		List<DistLevelRuleVO> filteredLevels = rules.stream()
				.filter(rule -> !DistRoleEnum.LEADER.getCode().equalsIgnoreCase(rule.getRole()))
				.sorted(Comparator.comparingInt(DistLevelRuleVO::getLevelCode))
				.collect(Collectors.toList());
		// 计算成员等级
		DistTeamMemberEntity teamMember = distTeamMemberService.getByTeamIdAndUserId(teamId, userId);
		log.info("[计算用户等级] 获取成员等级,结果：gvm:{} ,filteredLevels：{}",  teamMember.getGmv() ,JSON.toJSONString(filteredLevels));

		for (int i = filteredLevels.size() - 1; i >= 0; i--) {
			DistLevelRuleVO rule = filteredLevels.get(i);
			BigDecimal minGmv = Optional.ofNullable(rule.getMinGmv()).orElse(BigDecimal.ZERO);
			BigDecimal maxGmv = (i < filteredLevels.size() - 1)
					? Optional.ofNullable(filteredLevels.get(i + 1).getMinGmv()).orElse(new BigDecimal(Double.MAX_VALUE))
					: new BigDecimal(Double.MAX_VALUE);

			boolean gmvInRange = teamMember.getGmv().compareTo(minGmv) >= 0;
			log.info("[计算用户等级] 获取成员等级,结果：gvm:{} ,gmvInRange：{} ,rule:{}",  teamMember.getGmv() ,gmvInRange,JSON.toJSONString(rule));
			if (gmvInRange) {
				return rule;
			}
		}
		return filteredLevels.get(0);
	}

	/**
	 *
	 * @param teamId
	 * @param rules
	 * @param leaderUserId
	 * @return
	 */
	private DistLevelRuleVO calTeamRule(Long teamId,List<DistLevelRuleVO> rules,Long leaderUserId){
		// 过滤掉 role = "member" 的规则项
		List<DistLevelRuleVO> filteredLevels = rules.stream()
				.filter(rule -> !DistRoleEnum.MEMBER.getCode().equalsIgnoreCase(rule.getRole()))
				.sorted(Comparator.comparingInt(DistLevelRuleVO::getLevelCode))
				.collect(Collectors.toList());

		Integer teamMemberCount = distTeamMemberService.getTeamMemberCountByTeamId(teamId);
		DistTeamStatisticsEntity teamStat = teamStatisticsService.getByTeamId(teamId);
		DistTeamMemberEntity teamLeader = distTeamMemberService.getByTeamIdAndUserId(teamId, leaderUserId);
		BigDecimal teamGmv = teamStat.getTeamGmv();
		log.info("[计算团队等级] ------>>>teamId:{},teamMemberCount:{},teamGmv:{},teamLeader:{},levels:{}", teamId, teamMemberCount, teamGmv, teamLeader, filteredLevels);

		for (int i = filteredLevels.size() - 1; i >= 0; i--) {
			DistLevelRuleVO rule = filteredLevels.get(i);
			BigDecimal minGmv = Optional.ofNullable(rule.getMinGmv()).orElse(BigDecimal.ZERO);
			BigDecimal maxGmv = (i < filteredLevels.size() - 1)
					? Optional.ofNullable(filteredLevels.get(i + 1).getMinGmv()).orElse(new BigDecimal(Double.MAX_VALUE))
					: new BigDecimal(Double.MAX_VALUE);

//			boolean gmvInRange = teamGmv.compareTo(minGmv) >= 0 && teamGmv.compareTo(maxGmv) <= 0;
			boolean gmvInRange = teamGmv.compareTo(minGmv) >= 0 ;
			log.info("[判断等级] ---> rule:{}, minGmv:{}, maxGmv:{}, gmvInRange:{}", rule, minGmv, maxGmv, gmvInRange);
			boolean membersMet = teamMemberCount >= Optional.ofNullable(rule.getMinMembers()).orElse(0);
			log.info("[判断等级] ---> rule:{}, membersMet:{} ,teamMemberCount:{}", rule, membersMet,teamMemberCount);
			boolean personalGmvMet = teamLeader.getGmv().compareTo(Optional.ofNullable(rule.getMinPersonalGmv()).orElse(BigDecimal.ZERO)) >= 0;
			log.info("[判断等级] ---> rule:{}, personalGmvMet:{} ,teamLeader.getGmv()():{}", rule, personalGmvMet,teamLeader.getGmv());

			log.info("[判断等级] ---> rule:{}, minGmv:{}, maxGmv:{}, gmvInRange:{}, membersMet:{}, personalGmvMet:{}",
					rule, minGmv, maxGmv, gmvInRange, membersMet, personalGmvMet);

			if (gmvInRange && membersMet && personalGmvMet) {
				return rule;
			}
		}
		return filteredLevels.get(0);
	}

	/**
	 * 根据角色和等级代码查询个人佣金比例
	 * 
	 * @param role 角色（leader/member）
	 * @param levelCode 等级代码
	 * @return 个人佣金比例，如果未找到则返回0
	 */
	private BigDecimal getPersonalCommissionRate(String role, Integer levelCode) {
		List<DistLevelRuleVO> rules = distLevelRuleCacheService.queryList();
		Optional<DistLevelRuleVO> matchedRule = rules.stream()
				.filter(rule -> role.equals(rule.getRole()) && levelCode.equals(rule.getLevelCode()))
				.findFirst();
		if (matchedRule.isPresent()) {
			BigDecimal rate = matchedRule.get().getPersonalCommissionRate();
			return rate != null ? rate : BigDecimal.ZERO;
		}
		log.warn("未找到匹配的佣金规则，角色：{}，等级代码：{}", role, levelCode);
		return BigDecimal.ZERO;
	}
}

