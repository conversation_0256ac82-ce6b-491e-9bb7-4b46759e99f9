package com.yts.yyt.distribution.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yts.yyt.common.core.constant.CommonConstants;
import com.yts.yyt.common.core.constant.DelFlagConstants;
import com.yts.yyt.common.core.util.IDS;
import com.yts.yyt.distribution.api.constants.CacheConstants;
import com.yts.yyt.distribution.api.dto.DistTeamConfigAddDTO;
import com.yts.yyt.distribution.api.vo.DistTeamConfigVO;
import com.yts.yyt.distribution.entity.DistTeamConfigEntity;
import com.yts.yyt.distribution.mapper.DistTeamConfigMapper;
import com.yts.yyt.distribution.service.DistTeamConfigService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 分销团队配置表(DistTeamConfig)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-17 17:57:17
 */
@Service("distTeamConfigService")
public class DistTeamConfigServiceImpl extends ServiceImpl<DistTeamConfigMapper, DistTeamConfigEntity> implements DistTeamConfigService {

    @Override
    public DistTeamConfigEntity getConfig() {
        return this.getOne(new LambdaQueryWrapper<>());
    }

    @Override
    @Cacheable(value = CacheConstants.TEAM_CONFIT, key = "'all'", unless = "#result == null")
    public DistTeamConfigVO queryDistTeamConfig() {
        LambdaQueryWrapper<DistTeamConfigEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DistTeamConfigEntity::getDelFlag, CommonConstants.STATUS_NORMAL);
        wrapper.orderByDesc(DistTeamConfigEntity::getCreateTime);
        DistTeamConfigEntity one = this.getOne(wrapper);
        if (ObjUtil.isNull(one)) {
            return null;
        }
        return BeanUtil.copyProperties(one, DistTeamConfigVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = CacheConstants.TEAM_CONFIT, key = "'all'", beforeInvocation = true)
    public boolean update(DistTeamConfigAddDTO dto) {
        // 1. 逻辑删除所有旧数据
        this.lambdaUpdate()
                .set(DistTeamConfigEntity::getDelFlag, CommonConstants.STATUS_DEL)
                .eq(DistTeamConfigEntity::getDelFlag, CommonConstants.STATUS_NORMAL)
                .update();

        // 2. 保存新数据
        DistTeamConfigEntity entity = new DistTeamConfigEntity();
        BeanUtils.copyProperties(dto, entity);
        entity.setId(IDS.uniqueID());
        entity.setDelFlag(CommonConstants.STATUS_NORMAL);

        return this.save(entity);
    }

}

