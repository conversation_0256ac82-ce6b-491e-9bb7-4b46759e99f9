package com.yts.yyt.distribution.event.listener;

import com.yts.yyt.distribution.event.LotStatisticsEvent;
import com.yts.yyt.distribution.service.DistLotStatisticsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.context.event.EventListener;
import javax.annotation.PostConstruct;

/**
 * 拍品统计事件监听器
 *
 * <AUTHOR>
 * @since 2025-01-28
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LotStatisticsEventListener {
    
    private final DistLotStatisticsService distLotStatisticsService;
    
    @PostConstruct
    public void init() {
        log.info("拍品统计事件监听器初始化完成");
    }
    
    /**
     * 监听拍品统计事件，直接更新统计数据
     *
     * @param event 拍品统计事件
     */
    @EventListener
    public void handleLotStatistics(LotStatisticsEvent event) {
        log.info("拍品统计事件监听，分享短码：{}，统计类型：{}", event.getShortCode(), event.getType());
        try {
            // 根据统计类型直接调用相应的更新方法
            switch (event.getType()) {
                case SHARE:
                    distLotStatisticsService.incrementShareCount(event.getShortCode());
                    log.info("拍品分享统计更新成功，分享短码：{}", event.getShortCode());
                    break;
                case CLICK:
                    distLotStatisticsService.incrementClickCount(event.getShortCode());
                    log.info("拍品点击统计更新成功，分享短码：{}", event.getShortCode());
                    break;
                default:
                    log.warn("未知的统计类型：{}", event.getType());
            }
        } catch (Exception e) {
            log.error("处理拍品统计事件失败，分享短码：{}，统计类型：{}，错误信息：{}", 
                    event.getShortCode(), event.getType(), e.getMessage(), e);
        }
    }
} 