package com.yts.yyt.distribution.service.impl;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yts.yyt.distribution.api.constants.TeamStatusEnum;
import com.yts.yyt.distribution.api.enums.DistRoleEnum;
import com.yts.yyt.distribution.api.enums.TeamMemberStatusEnum;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yts.yyt.common.core.constant.DelFlagConstants;
import com.yts.yyt.distribution.api.constants.TeamStatusEnum;
import com.yts.yyt.distribution.api.dto.DistMyTeamPageDTO;
import com.yts.yyt.distribution.api.dto.TeamMemberPageDTO;
import com.yts.yyt.distribution.api.enums.TeamMemberStatusEnum;
import com.yts.yyt.distribution.api.vo.DistMyTeamVO;
import com.yts.yyt.distribution.api.vo.DistUserGmvVO;
import com.yts.yyt.distribution.api.vo.DistUserTeamInfoVO;
import com.yts.yyt.distribution.api.vo.TeamMemberVO;
import com.yts.yyt.distribution.entity.DistTeamEntity;
import com.yts.yyt.distribution.entity.DistTeamMemberEntity;
import com.yts.yyt.distribution.mapper.DistTeamMemberMapper;
import com.yts.yyt.distribution.service.DistTeamMemberService;
import com.yts.yyt.distribution.service.DistTeamService;

import cn.hutool.core.util.ObjUtil;
import lombok.AllArgsConstructor;

/**
 * 团队成员关系表(DistTeamMember)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-17 17:57:18
 */
@Service("distTeamMemberService")
@AllArgsConstructor
public class DistTeamMemberServiceImpl extends ServiceImpl<DistTeamMemberMapper, DistTeamMemberEntity> implements DistTeamMemberService {

    private final DistTeamService distTeamService;

    @Override
    public IPage<TeamMemberVO> pageTeamMember(TeamMemberPageDTO dto) {
        Page<?> page = new Page<>(dto.getCurrent(), dto.getSize());
		DistTeamEntity team = distTeamService.getById(dto.getTeamId());
		return baseMapper.pageTeamMember(page, dto, team.getUserId());
    }

    @Override
    public IPage<DistMyTeamVO> pageMyTeam(Page<?> page, Long teamId, DistMyTeamPageDTO dto) {
        return baseMapper.pageMyTeam(page, teamId, dto);
    }

    @Override
    public DistUserGmvVO getGmvByUserId(Long userId) {
        DistTeamMemberEntity entity = getActiveTeamMember(userId);
        if (ObjUtil.isNull(entity)) {
            return null;
        }
        return new DistUserGmvVO().setGmv(entity.getGmv());
    }

    @Override
    public DistUserTeamInfoVO getUserDistributionTeamInfo(Long userId) {
        DistUserTeamInfoVO vo = new DistUserTeamInfoVO();
        // 获取用户的团队成员信息
        DistTeamMemberEntity member = getActiveTeamMember(userId);
        if (member == null) {
            // 用户不属于任何团队
            vo.setRole("").setTeamStatus(null);
        } else {
            // 获取团队信息
            DistTeamEntity team = distTeamService.getById(member.getTeamId());
            // 设置团队状态
            vo.setTeamStatus(team != null ? team.getStatus() : null);
            // 设置角色：如果团队已解散则角色为空，否则返回用户角色
            boolean isTeamDissolved = team != null && TeamStatusEnum.DISSOLVED.getStatus().equals(team.getStatus());
            vo.setRole(isTeamDissolved ? "" : (member.getRole() != null ? member.getRole() : ""));
        }
        return vo;
    }

    @Override
    public String getUserDistributionRole(Long userId) {
        DistTeamMemberEntity member = getActiveTeamMember(userId);
        if (member != null && member.getRole() != null && !member.getRole().trim().isEmpty()) {
            return member.getRole();
        }
        return ""; // 如果没有找到角色，返回空字符串
    }

    @Override
    public Long getUserTeamId(Long userId) {
        DistTeamMemberEntity member = getActiveTeamMember(userId);
        return member != null ? member.getTeamId() : null;
    }

    @Override
    public Integer getTeamMemberCountByTeamId(Long teamId) {
        // 根据团队ID查询成员数量
        Long count = this.count(new LambdaQueryWrapper<>(DistTeamMemberEntity.class)
                        .eq(DistTeamMemberEntity::getTeamId, teamId)
                        .eq(DistTeamMemberEntity::getRole, DistRoleEnum.MEMBER.getCode())
                        .eq(DistTeamMemberEntity::getStatus, TeamMemberStatusEnum.NORMAL.getStatus()));

        return count != null ? count.intValue() : 0;
    }

    @Override
    public DistTeamMemberEntity getByTeamIdAndUserId(Long teamId, Long userId) {
        return this.getOne(new LambdaQueryWrapper<>(DistTeamMemberEntity.class)
                .eq(DistTeamMemberEntity::getTeamId, teamId)
                .eq(DistTeamMemberEntity::getUserId, userId)
                .eq(DistTeamMemberEntity::getDelFlag, DelFlagConstants.NORMAL));
    }

    /**
     * 根据用户ID获取正常状态的团队成员信息
     *
     * @param userId 用户ID
     * @return 团队成员实体
     */
    private DistTeamMemberEntity getActiveTeamMember(Long userId) {
        LambdaQueryWrapper<DistTeamMemberEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DistTeamMemberEntity::getUserId, userId)
                    .eq(DistTeamMemberEntity::getStatus, TeamMemberStatusEnum.NORMAL.getStatus());
        return this.getOne(queryWrapper);
    }

}

