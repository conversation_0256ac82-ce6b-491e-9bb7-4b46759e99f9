package com.yts.yyt.distribution.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yts.yyt.common.data.datascope.YytBaseMapper;
import com.yts.yyt.distribution.api.dto.CommissionSettlementPageDTO;
import com.yts.yyt.distribution.api.vo.CommissionSettlementPageVO;
import com.yts.yyt.distribution.api.vo.CommissionSettlementSummaryVO;
import com.yts.yyt.distribution.entity.DistSettleSummaryEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 分销员累计业绩汇总表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-01-29
 */
@Mapper
public interface DistSettleSummaryMapper extends YytBaseMapper<DistSettleSummaryEntity> {

    /**
     * 分页查询业绩结算
     *
     * @param page 分页参数
     * @param dto 查询条件
     * @return 分页结果
     */
    IPage<CommissionSettlementPageVO> pageQuery(Page<CommissionSettlementPageVO> page, @Param("dto") CommissionSettlementPageDTO dto);

    /**
     * 获取业绩结算合计数据
     *
     * @param dto 查询条件
     * @return 合计数据
     */
    CommissionSettlementSummaryVO getSummary(@Param("dto") CommissionSettlementPageDTO dto);

    /**
     * 从订单佣金表聚合数据到汇总表
     *
     * @return 影响行数
     */
    int aggregateFromOrderCommission();
}

