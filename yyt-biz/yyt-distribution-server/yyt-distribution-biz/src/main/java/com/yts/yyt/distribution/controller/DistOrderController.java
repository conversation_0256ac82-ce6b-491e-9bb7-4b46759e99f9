package com.yts.yyt.distribution.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.net.HttpHeaders;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.excel.annotation.ResponseExcel;
import com.yts.yyt.common.excel.annotation.Sheet;
import com.yts.yyt.common.security.annotation.HasPermission;
import com.yts.yyt.common.security.util.SecurityUtils;
import com.yts.yyt.common.swagger.base.BaseController;
import com.yts.yyt.distribution.api.dto.DistMyGmvPageDTO;
import com.yts.yyt.distribution.api.dto.DistOrderPageDTO;
import com.yts.yyt.distribution.api.vo.DistMyGmvVO;
import com.yts.yyt.distribution.api.vo.DistOrderPageVO;
import com.yts.yyt.distribution.api.vo.DistOrderStatusCountVO;
import com.yts.yyt.distribution.service.DistOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 分销订单表(DistOrder)表控制层
 *
 * <AUTHOR>
 * @since 2025-06-17 17:57:18
 */
@RestController
@AllArgsConstructor
@RequestMapping("/dist/order")
@Tag(name = "分销订单管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class DistOrderController extends BaseController {

    private final DistOrderService distOrderService;

    /**
     * 管理后台-分销订单跟踪-分页查询
     */
    @Operation(summary = "管理后台-分销订单跟踪-分页查询")
    @PostMapping("/page")
    public R<IPage<DistOrderPageVO>> pageQuery(@RequestBody @Validated DistOrderPageDTO dto) {
        return R.ok(distOrderService.pageQuery(dto));
    }

    /**
     * 管理后台-分销订单跟踪-导出
     */
    @Operation(summary = "管理后台-分销订单跟踪-导出", description = "分销订单跟踪-导出，权限：dist_order_export")
    @HasPermission("dist_order_export")
    @PostMapping("/export")
    @ResponseExcel(
        name = "分销订单跟踪", sheets = @Sheet(sheetName = "分销订单跟踪列表"), exclude = {"sharerId", "teamLeaderId", "buyerId","status"}
    )
    public List<DistOrderPageVO> exportList(@RequestBody @Validated DistOrderPageDTO dto) {
        // 验证时间范围是否超过一年
        if (dto.getStartTime().plusYears(1).isBefore(dto.getEndTime())) {
            throw new IllegalArgumentException("导出时间范围不能超过一年");
        }
        dto.setCurrent(1L);
        dto.setSize(Long.MAX_VALUE);
        return distOrderService.pageQuery(dto).getRecords();
    }

    /**
     * 移动端-我的GMV分页查询
     */
    @PostMapping("/myGmv/page")
    @Operation(summary = "移动端-我的GMV分页查询", description = "我的GMV分页查询")
    public R<IPage<DistMyGmvVO>> pageTeamGmv(@Validated @RequestBody DistMyGmvPageDTO dto) {
        Long userId = SecurityUtils.getUser().getId();
        return R.ok(distOrderService.pageMyGmv(userId, dto));
    }

    /**
     * 管理后台-分销订单状态统计
     */
    @PostMapping("/count")
    @Operation(summary = "管理后台-分销订单状态统计", description = "根据查询条件统计各状态的订单数量")
    public R<DistOrderStatusCountVO> getStatusCount(@RequestBody @Validated DistOrderPageDTO dto) {
        return R.ok(distOrderService.getStatusCount(dto));
    }

}

