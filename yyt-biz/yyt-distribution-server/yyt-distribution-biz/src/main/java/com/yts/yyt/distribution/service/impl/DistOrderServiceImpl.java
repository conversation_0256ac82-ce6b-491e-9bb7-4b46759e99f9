package com.yts.yyt.distribution.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yts.yyt.common.alert.annotation.AlertException;
import com.yts.yyt.common.core.constant.enums.DeletedEnum;
import com.yts.yyt.common.file.service.ImageUrlService;
import com.yts.yyt.distribution.api.constants.TeamStatusEnum;
import com.yts.yyt.distribution.api.dto.DistLevelRuleQueryDTO;
import com.yts.yyt.distribution.api.dto.DistMyGmvPageDTO;
import com.yts.yyt.distribution.api.dto.DistOrderPageDTO;
import com.yts.yyt.distribution.api.dto.DistTeamGmvPageDTO;
import com.yts.yyt.distribution.api.enums.DistOrderCommissionStatusEnum;
import com.yts.yyt.distribution.api.enums.DistOrderStatusEnum;
import com.yts.yyt.distribution.api.enums.DistRoleEnum;
import com.yts.yyt.distribution.api.exception.DistributionErrorEnum;
import com.yts.yyt.distribution.api.exception.DistributionException;
import com.yts.yyt.distribution.api.vo.*;
import com.yts.yyt.distribution.entity.*;
import com.yts.yyt.distribution.mapper.DistOrderMapper;
import com.yts.yyt.distribution.mq.dto.OrderMqDTO;
import com.yts.yyt.distribution.service.*;
import com.yts.yyt.order.api.enums.OrderEnum;
import com.yts.yyt.user.api.constant.TradeDescConstants;
import com.yts.yyt.user.api.constant.enums.AccountEnum;
import com.yts.yyt.user.api.dto.AccountOperateDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ObjectUtils;
import org.apache.seata.spring.annotation.GlobalTransactional;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 分销订单表(DistOrder)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-17 17:57:18
 */
@Slf4j
@Service("distOrderService")
@AllArgsConstructor
public class DistOrderServiceImpl extends ServiceImpl<DistOrderMapper, DistOrderEntity> implements DistOrderService {

    private final DistLotShareRecordService distLotShareRecordService;
    private final DistLevelRuleService distLevelRuleService;
    private final DistTeamMemberService distTeamMemberService;
    private final DistUserLevelService distUserLevelService;
	private final BaseRemoteService baseRemoteService;
    private final DistOrderCommissionService distOrderCommissionService;
	private final ImageUrlService imageUrlService;
    private final DistTeamService teamService;
    private final DistTeamStatisticsService distTeamStatisticsService;

    @Override
    public IPage<DistOrderPageVO> pageQuery(DistOrderPageDTO dto) {
        log.info("分页查询分销订单开始，查询条件：{}", dto);
        IPage<DistOrderPageVO> result = this.baseMapper.pageQuery(new Page<>(dto.getCurrent(), dto.getSize()), dto);
        if (CollUtil.isEmpty(result.getRecords())) {
            return result;
        }
        // 查询用户信息
        processUserInfo(result.getRecords());
        log.info("查询结果数量：{}", result.getRecords().size());
        return result;
    }

    private void processUserInfo(List<DistOrderPageVO> list) {
        Set<Long> userIds = new HashSet<>();
        for (DistOrderPageVO record : list) {
            userIds.add(record.getBuyerId());
            userIds.add(record.getSharerId());
            userIds.add(record.getTeamLeaderId());
        }
        List<DistOrderUserInfoVO> userInfoList = this.baseMapper.queryOrderUserInfoByUserIds(userIds);
        Map<Long, DistOrderUserInfoVO> userMap = userInfoList.stream().collect(Collectors.toMap(DistOrderUserInfoVO::getUserId, item -> item, (o1, o2) -> o1));
        for (DistOrderPageVO record : list) {
            DistOrderUserInfoVO userInfo = userMap.get(record.getBuyerId());
            if (userInfo != null) {
                record.setBuyerName(userInfo.getName());
                record.setBuyerPhone(userInfo.getPhone());
            }
            record.setSharerName(ObjectUtil.isNull(record.getSharerId()) ? "" : userMap.get(record.getSharerId()).getName());
            record.setTeamLeader(ObjectUtil.isNull(record.getTeamLeaderId()) ? "" : userMap.get(record.getTeamLeaderId()).getName());
            record.setOrderSource(OrderEnum.OrderSource.getDescByType(Integer.parseInt(record.getOrderSource())));
            record.setPayType(OrderEnum.OrderPayType.getDescByType(Integer.parseInt(record.getPayType())));
            record.setStatusName(DistOrderStatusEnum.getDescByStatus(record.getStatus()));
        }
    }

    @Override
    public List<DistOrderPageVO> exportList(DistOrderPageDTO dto) {
        log.info("开始导出分销订单列表，参数：{}", dto);
        // 查询数据
        List<DistOrderPageVO> list = this.baseMapper.pageQuery(dto);
        if (CollUtil.isEmpty(list)) {
            throw new IllegalArgumentException("暂无数据可导出");
        }
        // 处理用户信息
        processUserInfo(list);
        return list;
    }

    @Override
    public IPage<DistTeamGmvVO> pageTeamGmv(Long userId, DistTeamGmvPageDTO dto) {
        IPage<DistTeamGmvVO> page = this.baseMapper.pageTeamGmv(new Page<>(dto.getCurrent(), dto.getSize()), userId, dto);
        // 处理缩略图
        if (CollUtil.isNotEmpty(page.getRecords())) {
            for (DistTeamGmvVO vo : page.getRecords()) {
                if (vo.getLotImage() != null) {
                    vo.setLotImage(imageUrlService.convertImageUrl(vo.getLotImage()).getSmallUrl());
                }
            }
        }
        return page;
    }

    @Override
    public IPage<DistMyGmvVO> pageMyGmv(Long userId, DistMyGmvPageDTO dto) {
        return this.baseMapper.pageMyGmv(new Page<>(dto.getCurrent(), dto.getSize()), userId, dto);
    }

    @Override
    public DistOrderStatusCountVO getStatusCount(DistOrderPageDTO dto) {
        DistOrderStatusCountVO result = this.baseMapper.getStatusCount(dto);
        log.info("状态统计完成，总数量：{}", result.getTotalCount());
        return result;
    }

    @Override
    @AlertException(value = "订单GMV计算异常", modules = "订单状态变更")
    @Transactional(rollbackFor = Exception.class)
    @GlobalTransactional(rollbackFor = Exception.class)
    @Lock4j(keys = {"#dto.orderId"})
    public boolean changeOrderStatusAndCalcuateGmv(OrderMqDTO dto){
        Integer status = Integer.parseInt(dto.getStatus());
        if(Objects.equals(DistOrderStatusEnum.BEEN_PAY.getStatus(), status)){
            calculateOrderGmv(dto);
        }else if(Objects.equals(DistOrderStatusEnum.BEEN_FINISH.getStatus(), status)){
            confirmOrderGmv(dto);
        }else if(Objects.equals(DistOrderStatusEnum.BEEN_REFUND.getStatus(), status)){
            deductOrderGmv(dto);
        }else {
            DistOrderEntity order = getOrderByOrderId(dto.getOrderId());
            if(order == null){
                log.info("[订单GMV计算] 订单不存在，状态不作变更，订单ID：{}",dto.getOrderId());
                return true;
            }
            order.setStatus(status);
            updateById(order);
        }
        return true;
    }

    private void calculateOrderGmv(OrderMqDTO dto) {
        log.info("[订单GMV计算]----------------->>> 开始计算订单GMV，dto:{}",dto);
        // 分享记录
        DistLotShareRecordEntity shareRecord = getShareRecord(dto.getShareCode());
        DistTeamEntity team = teamService.getById(shareRecord.getTeamId());
        if(team == null || ObjectUtils.equals(team.getStatus(), TeamStatusEnum.DISSOLVED.getStatus())) {
            log.info("[订单GMV计算] 订单所属团队已解散，不计GVM，订单ID：{}",dto.getOrderId());
            return;
        }
        DistOrderEntity order = createAndSaveOrder(dto, shareRecord);
        // 成员 TODO 成员状态
        DistTeamMemberEntity member = distTeamMemberService.getByTeamIdAndUserId(shareRecord.getTeamId(), shareRecord.getUserId());
        // 用户等级
        DistUserLevelEntity userLevel = distUserLevelService.getAndUpgradeUserLevel(shareRecord.getUserId(), member.getRole(),shareRecord.getTeamId());
        // 佣金计算规则
        DistLevelRuleVO levelRule = distLevelRuleService.queryByLevelCodeAndRole(new DistLevelRuleQueryDTO().setLevelCode(userLevel.getLevelCode()).setRole(member.getRole()));
        log.info("[订单GMV计算] 佣金计算规则：levelRule:{},shareRecord:{},member:{},userLevel:{}", levelRule, shareRecord,member,userLevel);
        // 计算并生成佣金
        BigDecimal commissionAmt = levelRule.getPersonalCommissionRate().multiply(order.getAmount()).divide(new BigDecimal("100"), 2, RoundingMode.DOWN);
        DistOrderCommissionEntity orderCommission;
        if(commissionAmt.compareTo(BigDecimal.ZERO) > 0){
            orderCommission = createAndSaveOrderCommission(order.getId(), shareRecord.getUserId(), member.getRole(), userLevel, levelRule.getId(), commissionAmt);
            operateAccount(shareRecord.getUserId(),commissionAmt,orderCommission.getId(),TradeDescConstants.ORDER_PAY_DEST_MEMBER_COMMISSION,1);
        }
        if(DistRoleEnum.MEMBER.getCode().equals(member.getRole())) {
            commissionAmt = levelRule.getLeaderCommissionRate().multiply(order.getAmount()).divide(new BigDecimal("100"), 2, RoundingMode.DOWN);
            if(commissionAmt.compareTo(BigDecimal.ZERO) > 0){
                // 计算团长等级佣金
                if(!ObjectUtils.equals(TeamStatusEnum.NORMAL.getStatus(),team.getStatus())){
                    log.info("[订单GMV计算] 过滤团长等级佣金计算，团长状态非正常，订单ID:{}", dto.getOrderId());
                }else {
                    orderCommission = createAndSaveOrderCommission(order.getId(), team.getUserId(), DistRoleEnum.LEADER.getCode(), userLevel, levelRule.getId(), commissionAmt);
                    operateAccount(team.getUserId(),commissionAmt,orderCommission.getId(),TradeDescConstants.ORDER_PAY_DEST_TEAM_COMMISSION,1);
                }
            }
        }
        distUserLevelService.upgradeUserLevel(shareRecord.getUserId(), member.getRole(),shareRecord.getTeamId());
        log.info("[订单GMV计算]----------------->>> 订单GMV计算完成，订单ID:{}", dto.getOrderId());
    }

    private void deductOrderGmv(OrderMqDTO dto) {
        log.info("[扣减GMV]----------------->>> 开始，dto:{}", dto);
        DistOrderEntity order = getOrderByOrderId(dto.getOrderId());
        if(order == null || DistOrderStatusEnum.BEEN_FINISH.getStatus().equals(order.getStatus())
                         || DistOrderStatusEnum.BEEN_REFUND.getStatus().equals(order.getStatus())) {
            log.info("[扣减GMV] 订单不存在或状态异常，dto:{},order:{}", dto,order);
            return;
        }
        order.setStatus(DistOrderStatusEnum.BEEN_REFUND.getStatus());
        updateById(order);
        // 更新佣金明细状态
        List<DistOrderCommissionEntity> commissions = distOrderCommissionService.selectByOrderId(order.getId());
        distOrderCommissionService.updateStatusByOrderId(order.getId(), DistOrderCommissionStatusEnum.INVALID.getStatus());
        // 扣减账户金额
        for (DistOrderCommissionEntity commission : commissions) {
            operateAccount(commission.getUserId(),commission.getCommissionAmt(),commission.getId(),TradeDescConstants.ORDER_REDUCE_DEST_MEMBER_COMMISSION,2);
        }
    }
    private void confirmOrderGmv(OrderMqDTO dto) {
        log.info("[确认GMV]----------------->>> 订单GMV确认，dto:{}", dto);
        DistOrderEntity order = getOrderByOrderId(dto.getOrderId());
        if(order == null || DistOrderStatusEnum.BEEN_FINISH.getStatus().equals(order.getStatus())
                || DistOrderStatusEnum.BEEN_REFUND.getStatus().equals(order.getStatus())) {
            log.info("[确认GMV] 订单不存在或状态异常，dto:{},order:{}", dto,order);
            return;
        }
        order.setStatus(DistOrderStatusEnum.BEEN_FINISH.getStatus());
        updateById(order);
        // 更新累计个人&团队GMV
        DistTeamMemberEntity member = distTeamMemberService.getByTeamIdAndUserId(order.getTeamId(), order.getSharerId());
        member.setGmv(member.getGmv().add(order.getAmount()));
        distTeamMemberService.updateById(member);
        DistTeamEntity team = teamService.getById(order.getTeamId());
        // 佣金记录
        List<DistOrderCommissionEntity> commissions = distOrderCommissionService.selectByOrderId(order.getId());
        if(!TeamStatusEnum.NORMAL.getStatus().equals(team.getStatus()) || !DistRoleEnum.MEMBER.getCode().equals(member.getRole())){
            log.info("[确认GMV] 团队状态非正常或非成员角色，不计算团队GMV，订单ID:{}", dto.getOrderId());
        }else {
            List<DistOrderCommissionEntity> filterCommissions = commissions.stream()
                    .filter(commission -> DistRoleEnum.MEMBER.getCode().equals(commission.getRole()))
                    .collect(Collectors.toList());
            // 更新团队GMV
            if(CollectionUtil.isNotEmpty(filterCommissions)){
                log.info("[确认GMV] 团队GMV计算，订单ID:{},commissions:{}", dto.getOrderId(), JSON.toJSONString(filterCommissions));
                distTeamStatisticsService.calculateAndSaveTeamStatistics(order.getTeamId(), order.getAmount(),filterCommissions.get(0).getCommissionAmt());
            }
        }
        // 升级用户等级
        distUserLevelService.upgradeUserLevel(member.getUserId(), member.getRole(), order.getTeamId());
        // 解冻余额
        // 扣减账户金额
        for (DistOrderCommissionEntity commission : commissions) {
            operateAccount(commission.getUserId(),commission.getCommissionAmt(),commission.getId(),TradeDescConstants.ORDER_COMPLETE_DEST_COMMISSION,3);
        }
    }

    @Override
    public DistOrderEntity getOrderByOrderId(Long orderId) {
        return lambdaQuery().eq(DistOrderEntity::getOrderId, orderId)
                .eq(DistOrderEntity::getDelFlag, DeletedEnum.UN_DELETE.getType()).one();
    }

    private DistOrderCommissionEntity createAndSaveOrderCommission(Long distOrderId, Long userId,
                                                                   String role,DistUserLevelEntity userLevel,
                                                                   Long ruleId, BigDecimal commissionAmt){
        DistOrderCommissionEntity orderCommission = new DistOrderCommissionEntity();
        orderCommission.setOrderId(distOrderId)
                .setUserId(userId)
                .setRole(role)
                .setLevelCode(userLevel.getLevelCode())
                .setLevelName(userLevel.getLevelName())
                .setRuleId(ruleId)
                .setCommissionAmt(commissionAmt)
                .setStatus(DistOrderCommissionStatusEnum.NORMAL.getStatus());
        distOrderCommissionService.save(orderCommission);
        return  orderCommission;
    }
    private DistOrderEntity createAndSaveOrder(OrderMqDTO dto,DistLotShareRecordEntity shareRecord) {
        DistOrderEntity order = new DistOrderEntity();
        order.setLotId(shareRecord.getLotId())
                .setImg(dto.getMainImg())
                .setName(dto.getGoodsName())
                .setOrderId(dto.getOrderId())
                .setOrderNo(dto.getOrderNo())
                .setMerchantId(dto.getMerchantId())
                .setBuyerId(dto.getBuyUserId())
                .setSharerId(shareRecord.getUserId())
                .setTeamId(shareRecord.getTeamId())
                .setAmount(dto.getAmount())
                .setStatus(DistOrderStatusEnum.BEEN_PAY.getStatus())
                .setOrderSource(dto.getOrderSource())
                .setPayType(dto.getPayType())
                .setPayTime(dto.getPayTime());
        save(order);
        return order;
    }

    /**
     *
     * @param userId
     * @param amount
     * @param businessId
     * @param businessTradeType
     * @param tag 1-增加冻结 2-减冻结 3-解冻
     */
    private void operateAccount(Long userId, BigDecimal amount,Long businessId,String businessTradeType,Integer tag){
        AccountOperateDTO dto = new AccountOperateDTO();
        dto.setUserId(userId);
        dto.setAccountType(AccountEnum.AccountTypeEnum.DISTRIBUTION_ACCOUNT.getType());
        dto.setAmount(amount);
        dto.setBusinessId(String.valueOf(businessId));
        dto.setTradeDesc(businessTradeType);
        dto.setBusinessTradeDesc(TradeDescConstants.ORDER_PAY_DIST_COMMISSION);
        dto.setBusinessType(AccountEnum.BusinessTypeEnum.DISTRIBUTION);
        if(tag == 1){
            baseRemoteService.addAndFreezeBalance(dto);
        }else if(tag == 2 ) {
            baseRemoteService.reduceFreezeBalance(dto);
        }else if(tag == 3){
            baseRemoteService.unfreezeBalance(dto);
        }
    }
    private DistLotShareRecordEntity getShareRecord(String shareCode) {
        DistLotShareRecordEntity shareRecord = distLotShareRecordService.getShareRecordByShortCode(shareCode);
        if(shareRecord == null) {
            log.error("分享码不存在，订单ID:{}", shareCode);
            throw DistributionException.build(DistributionErrorEnum.SHARE_CODE_NOT_EXIST);
        }
        return shareRecord;
    }



}

