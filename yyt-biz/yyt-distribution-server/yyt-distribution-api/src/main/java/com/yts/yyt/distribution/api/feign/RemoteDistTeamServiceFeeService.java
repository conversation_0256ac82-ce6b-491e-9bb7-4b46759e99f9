package com.yts.yyt.distribution.api.feign;

import com.yts.yyt.common.core.constant.ServiceNameConstants;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.feign.annotation.NoToken;
import com.yts.yyt.distribution.api.dto.DistTeamServiceFeePayCallbackDTO;
import com.yts.yyt.distribution.api.dto.DistTeamServiceFeePayDTO;
import com.yts.yyt.distribution.api.dto.DistTeamServiceFeeRefundCallbackDTO;
import com.yts.yyt.distribution.api.vo.DistTeamServiceFeePayParamVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(contextId = "remoteDistTeamServiceFeeService", value = ServiceNameConstants.DISTRIBUTION_SERVER,path = "/team/fee")
public interface RemoteDistTeamServiceFeeService {

    /**
     * 获取团长服务费的支付参数
     * @param dto
     * @return
     */
    @NoToken
    @PostMapping("/getPayParam")
    R<DistTeamServiceFeePayParamVO> getPayParam(@RequestBody DistTeamServiceFeePayDTO dto);

    /**
     * 团长服务费支付回调
     * @param dto
     * @return
     */
    @NoToken
    @PostMapping("/payBack")
    R<Boolean> payBack(@RequestBody DistTeamServiceFeePayCallbackDTO dto);


    /**
     * 消保金支付回调
     * @param dto
     * @return
     */
    @NoToken
    @PostMapping("/refund/payBack")
    R<Boolean> refundPayBack(@RequestBody DistTeamServiceFeeRefundCallbackDTO dto);
}
