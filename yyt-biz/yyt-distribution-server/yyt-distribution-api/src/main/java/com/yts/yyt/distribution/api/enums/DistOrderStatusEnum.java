package com.yts.yyt.distribution.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DistOrderStatusEnum {
    BEEN_PAY(0,"已支付"),
    BEEN_SHIP(1,"已发货"),
    BEEN_SIGN(2,"已签收"),
    BEEN_FINISH(3,"已完成"),
    BEEN_REFUND(4,"已退款"),

    ;


    private Integer status;
    private String desc;

    /**
     * 根据 status 获取 desc
     */
    public static String getDescByStatus(int status) {
        for (DistOrderStatusEnum item : DistOrderStatusEnum.values()) {
            if (item.getStatus() == status) {
                return item.getDesc();
            }
        }
        return "--";
    }
}
