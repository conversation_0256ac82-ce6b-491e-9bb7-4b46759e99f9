package com.yts.yyt.distribution.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 计算佣金请求DTO
 */
@Data
@Schema(description = "计算佣金请求")
public class CalculateCommissionDTO {

	/**
	 * 拍品id
	 */
	@NotNull(message = "拍品id不能为空")
	@Schema(description = "拍品id", requiredMode = Schema.RequiredMode.REQUIRED)
	private Long lotId;

	/**
	 * 用户id
	 */
	@NotNull(message = "用户id不能为空")
	@Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED)
	private Long userId;

	/**
	 * 销售价格
	 */
	@NotNull(message = "销售价格不能为空")
	@Schema(description = "销售价格", requiredMode = Schema.RequiredMode.REQUIRED)
	private BigDecimal salePrice;

} 