package com.yts.yyt.distribution.api.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum TeamDismissStatusEnum {

    //  通过=approve 拒绝=reject
    APPROVE("approve", "通过"),
    REJECT("reject", "拒绝"),
    PENDING("pending", "待审核"),
    ;

    private String status;
    private String desc;

    public static String getDescBySatus(String status) {
        for (TeamDismissStatusEnum value : TeamDismissStatusEnum.values()) {
            if (value.status.equals(status)) {
                return value.desc;
            }
        }
        return null;
    }

}
