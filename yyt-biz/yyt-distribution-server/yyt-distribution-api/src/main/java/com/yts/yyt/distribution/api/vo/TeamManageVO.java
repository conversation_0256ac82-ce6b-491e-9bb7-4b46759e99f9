package com.yts.yyt.distribution.api.vo;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class TeamManageVO {
    @Schema(description = "团队ID")
    private Long teamId;

    @Schema(description = "团队名称")
    private String teamName;

    @Schema(description = "团长")
    private String leaderName;

    @Schema(description = "团长电话")
    private String leaderPhone;

	@Schema(description = "团长等级code")
	private String leaderLevelCode;

    @Schema(description = "团长等级")
    private String leaderLevelName;

    @Schema(description = "团员数量")
    private Integer memberCount;

    @Schema(description = "团队累计GMV")
    private BigDecimal teamGmv;

    @Schema(description = "团队累计已售商品数")
    private Integer productSoldCount;

    @Schema(description = "团队累计佣金")
    private BigDecimal commissionTotal;

    @Schema(description = "组团时间")
    private LocalDateTime createTime;

    @Schema(description = "团队服务费")
    private BigDecimal serviceFee;

    @Schema(description = "状态")
    private Integer status;
} 
