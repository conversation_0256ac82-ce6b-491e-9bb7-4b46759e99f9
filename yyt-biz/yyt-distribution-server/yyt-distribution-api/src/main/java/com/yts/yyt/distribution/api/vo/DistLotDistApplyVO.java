package com.yts.yyt.distribution.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 拍品分销启/停申请详情VO
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
@Schema(description = "拍品分销启/停申请详情VO")
public class DistLotDistApplyVO {

    /**
     * ID（雪花ID）
     */
    @Schema(description = "ID（雪花ID）")
    private Long id;

    /**
     * 拍品 lot 主键（goods_lot_info.id）
     */
    @Schema(description = "拍品 lot 主键（goods_lot_info.id）")
    private Long lotId;

    /**
     * 拍品名称快照
     */
    @Schema(description = "拍品名称快照")
    private String lotName;

    /**
     * 申请人类型：商家=merchant 合伙人=partner
     */
    @Schema(description = "申请人类型：商家=merchant 合伙人=partner")
    private String applicantType;

    /**
     * 申请人 ID
     */
    @Schema(description = "申请人 ID")
    private Long applicantId;

    /**
     * 申请人名称
     */
    @Schema(description = "申请人名称")
    private String applicantName;

    /**
     * 取消分销=cancel 恢复分销=recovery
     */
    @Schema(description = "取消分销=cancel 恢复分销=recovery")
    private String action;

    /**
     * 申请原因
     */
    @Schema(description = "申请原因")
    private String applyReason;

    /**
     * 申请提交时间
     */
    @Schema(description = "申请提交时间")
    private LocalDateTime applyTime;

    /**
     * 审核记录列表
     */
    @Schema(description = "审核记录列表")
    private List<DistLotDistAuditVO> auditList;
}