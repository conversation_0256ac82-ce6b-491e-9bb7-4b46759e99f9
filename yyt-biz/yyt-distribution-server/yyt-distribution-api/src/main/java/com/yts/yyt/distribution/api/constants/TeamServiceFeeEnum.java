package com.yts.yyt.distribution.api.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

public interface TeamServiceFeeEnum {

    @AllArgsConstructor
    @Getter
    enum OrderType {
        PAY("PAY","支付"),
        REFUND("REFUND","退款"),

        ;

        private String type;
        private String desc;
    }

    @AllArgsConstructor
    @Getter
    enum PayStatus {
        CANCEL("CANCEL", "已取消"),
        PENDING("PENDING", "待支付"),
        PROCESSING("PROCESSING", "支付中"),
        BEEN_PAY("BEEN_PAY", "已支付"),
        FAIL("FAIL", "支付失败"),
        ;
        private String payStatus;
        private String desc;
    }

    @AllArgsConstructor
    @Getter
    enum RetStatusEnum{
        // 未退回
        RET_STATUS_NOT_RETURNED(0, "未退回"),
        // 退回处理中
        RET_STATUS_PROCESSING(1, "退回处理中"),
        // 已退回
        RET_STATUS_RETURNED(2, "已退回"),
        // 退回失败
        RET_STATUS_RETURN_FAIL(3, "退回失败"),
        ;
        private Integer retStatus;
        private String retDesc;
    }

}
