package com.yts.yyt.distribution.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 拍品分销管理状态统计VO
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@Schema(description = "拍品分销管理状态统计VO")
public class DistLotInfoStatisticsVO {

    /**
     * 全部数量
     */
    @Schema(description = "全部数量")
    private Long totalCount;

    /**
     * 分销中数量
     */
    @Schema(description = "分销中数量")
    private Long distributingCount;

    /**
     * 非分销数量
     */
    @Schema(description = "非分销数量")
    private Long nonDistributingCount;

    /**
     * 已分享数量
     */
    @Schema(description = "已分享数量")
    private Long sharedCount;

    /**
     * 未分享数量
     */
    @Schema(description = "未分享数量")
    private Long notSharedCount;

    /**
     * 待审批数量
     */
    @Schema(description = "待审批数量")
    private Long pendingCount;
} 