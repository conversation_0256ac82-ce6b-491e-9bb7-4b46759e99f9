package com.yts.yyt.distribution.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@Schema(description = "用户团费信息VO")
public class DistTeamServiceFeeListVO {

    @Schema(description = "团队ID")
    private Long teamId;

    @Schema(description = "团队名称")
    private String teamName;

    @Schema(description = "缴纳的团费金额（元）")
    private BigDecimal serviceFee;

    /**
     * @see com.yts.yyt.distribution.api.constants.TeamStatusEnum
     */
    @Schema(description = "团队状态")
    private Integer status;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

} 