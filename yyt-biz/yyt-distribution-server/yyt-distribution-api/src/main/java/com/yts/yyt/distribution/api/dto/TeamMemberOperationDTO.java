package com.yts.yyt.distribution.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 团队成员操作DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "团队成员操作DTO")
public class TeamMemberOperationDTO {

    /**
     * 团队ID
     */
    @NotNull(message = "团队ID不能为空")
    @Schema(description = "团队ID", required = true)
    private Long teamId;

    /**
     * 是否新团
     */
    @Schema(description = "是否新团", required = true)
    private Boolean isNewTeam;

    /**
     * 操作类型：1-加入团队，2-离开团队
     * @see com.yts.yyt.distribution.api.enums.TeamMemberOperationEnum
     */
    @NotNull(message = "操作类型不能为空")
    @Schema(description = "操作类型：1-加入团队，2-离开团队", required = true)
    private Integer operationType;
} 