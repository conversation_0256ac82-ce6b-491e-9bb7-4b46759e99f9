package com.yts.yyt.distribution.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(description = "团长服务费预生成订单")
@Accessors(chain = true)
public class DistTeamServiceFeePrePayVO implements Serializable {
    public final long serialVersionUID = 1L;


    public static final String PAYBACK = "DIST_TEAM_SERVICE_FEE_PAYBACK";

    @Schema(description = "订单ID")
    private Long orderId;

    @Schema(description = "交易金额")
    private BigDecimal amount;

    @Schema(description = "判断是否需要支付 无需支付场景下直接成功")
    private Boolean needPay = true;

}
