package com.yts.yyt.distribution.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "分销等级&佣金规则表-查询")
public class DistLevelRuleQueryDTO {

    /**
     * 角色类型：团长=leader 团员=member
     */
    @Schema(description="团长=leader 团员=member")
    @NotNull(message = "角色类型不能为空")
    private String role;
    /**
     * 等级序号：1=初级 2=中级 3=高级 …
     */
    @Schema(description="等级序号：1=初级 2=中级 3=高级 …")
    private Integer levelCode;
}

