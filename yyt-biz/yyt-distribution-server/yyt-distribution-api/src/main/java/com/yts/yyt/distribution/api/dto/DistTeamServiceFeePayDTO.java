package com.yts.yyt.distribution.api.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

@Data
public class DistTeamServiceFeePayDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long orderId;

    private Long userId;

    private String payChannel;

    private String teamName;
}
