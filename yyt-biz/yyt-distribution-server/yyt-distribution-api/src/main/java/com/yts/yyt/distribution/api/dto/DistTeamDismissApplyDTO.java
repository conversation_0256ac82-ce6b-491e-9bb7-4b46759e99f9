package com.yts.yyt.distribution.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 团队解散申请DTO
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
@Accessors(chain = true)
@Schema(description = "团队解散申请DTO")
public class DistTeamDismissApplyDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 申请解散原因
     */
    @Schema(description = "申请解散原因")
    @NotBlank(message = "申请解散原因不能为空")
    private String dismissReason;
} 