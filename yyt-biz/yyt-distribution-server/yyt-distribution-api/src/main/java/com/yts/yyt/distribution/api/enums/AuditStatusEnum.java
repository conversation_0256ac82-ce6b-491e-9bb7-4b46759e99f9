package com.yts.yyt.distribution.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 审核状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AuditStatusEnum {

    /**
     * 审核通过
     */
    APPROVE("approve", "审核通过"),

    /**
     * 审核拒绝
     */
    REJECT("reject", "审核拒绝");

    /**
     * 状态编码
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 通过编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static AuditStatusEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (AuditStatusEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 判断code是否有效
     *
     * @param code 编码
     * @return 是否有效
     */
    public static boolean isValid(String code) {
        return getByCode(code) != null;
    }

    /**
     * 是否为通过
     *
     * @param code 编码
     * @return 是否为通过
     */
    public static boolean isApprove(String code) {
        return APPROVE.getCode().equals(code);
    }

    /**
     * 是否为拒绝
     *
     * @param code 编码
     * @return 是否为拒绝
     */
    public static boolean isReject(String code) {
        return REJECT.getCode().equals(code);
    }
} 