package com.yts.yyt.goods.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "商品信息查询")
public class SearchStoreGoodsInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    private Long id;

	/**
	 * 状态 pre_appraisal 待鉴定、wait_appraisal 鉴定中、fail_appraisal 鉴定失败、pre_publicity 待公示、publicity 公示中
	 * @see com.yts.yyt.goods.api.enums.StoreGoodsInfoEnum.State
	 */
	@Schema(description="状态 pre_appraisal 待鉴定、wait_appraisal 鉴定中、fail_appraisal 鉴定失败、pre_publicity 待公示、publicity 公示中、completed 已公示、auditing 审核中")
	private String state;

    /**
     * 藏品名称
     */
    @Schema(description="藏品名称")
    private String name;

    @Schema(description = "店铺名")
    private String shopName;
    
    /**
     * 权属
     */
    @Schema(description="权属")
    private String ownership;

    /**
     * 商户id
     */
    @Schema(description="商户id")
    private String merchantId;

    /**
     * 商户ids
     */
    @Schema(description="商户ids", hidden = true)
    private List<Long> merchantIds;

    /**
     * 联系人手机号
     */
    @Schema(description="联系人手机号")
    private String merchantContactPhone;

    /**
     * 商品编码
     */
    @Schema(description="商品编码")
    private String goodsNo;

    /**
     *  添加 开始时间
     */
    @Schema(description = "开始时间")
    private String startCreateTime;

    /**
     * 添加结束时间
     */
    @Schema(description = "结束时间")
    private String endCreateTime;

    /**
     *  鉴定开始时间
     */
    @Schema(description = "鉴定开始时间")
    private String startAppraisalTime;

    /**
     * 鉴定结束时间
     */
    @Schema(description = "鉴定结束时间")
    private String endAppraisalTime;

	@Schema(description="公示状态 pre_publicity 待公示、publicity 公示中", hidden = true)
	private String publicityState;

}
