package com.yts.yyt.goods.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@Schema(description = "佣金统计任务数据")
public class CommissionStatisticsProcessVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 合伙人ID
     */
    @Schema(description = "合伙人ID")
    private Long cityPartnerId;

    /**
     * 商户ID
     */
    @Schema(description = "商户ID")
    private Long merchantId;

    /**
     * 合伙人基础总佣金
     */
    @Schema(description = "合伙人基础总佣金")
    private BigDecimal partnerCommissionBase;

    /**
     * 合伙人专项总佣金
     */
    @Schema(description = "合伙人专项总佣金")
    private BigDecimal partnerCommissionSpecial;

    /**
     * 商户基础总佣金
     */
    @Schema(description = "商户基础总佣金")
    private BigDecimal merchantCommissionBase;

    /**
     * 专项佣金商品数量
     */
    @Schema(description = "专项佣金商品数量")
    private Long specialGoodsCount;

    /**
     * 基础佣金数据
     */
    @Schema(description = "基础佣金数据")
    private List<CommissionStatisticsVO> baseCommissionSub;
} 