package com.yts.yyt.goods.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 功能 商品投诉举报
 * 创建于 2025/3/24 10:25
 * <AUTHOR>
 */
@Data
@TableName("goods_feedback")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "商品投诉举报")
public class GoodsFeedbackEntity extends Model<GoodsFeedbackEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 用户id
	*/
    @Schema(description="用户id")
    private Long userId;

	/**
	 * 商品ID
	 */
	@Schema(description="商品ID")
	private Long goodsId;

	/**
	* 举报标题
	*/
    @Schema(description="举报标题")
    private String title;

	/**
	* 举报人姓名
	*/
    @Schema(description="举报人姓名")
    private String username;

	/**
	* 举报人电话
	*/
    @Schema(description="举报人电话")
    private String mobile;

	/**
	* 举报内容
	*/
    @Schema(description="举报内容")
    private String feedbackContent;

	/**
	* 证明图片列表
	*/
    @Schema(description="证明图片列表")
    private String feedbackImgs;

	/**
	* 证明文件列表
	*/
    @Schema(description="证明文件列表")
    private String feedbackFiles;

	/**
	* 身份证正面
	*/
    @Schema(description="身份证正面")
    private String idCardFront;

	/**
	 * 身份证背面
	 */
	@Schema(description="身份证背面")
	private String idCardBack;

	/**
	 * 状态(见字典goods_feedback_status)
	 * @see com.yts.yyt.goods.api.enums.GoodsFeedbackStatusEnum
	 */
	@Schema(description="状态(见字典goods_feedback_status)")
	private String status;

	/**
	 * 审核结果
	 */
	@Schema(description="审核结果")
	private String auditRemarks;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 删除标记，0未删除，1已删除
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标记，0未删除，1已删除")
    private String delFlag;
}