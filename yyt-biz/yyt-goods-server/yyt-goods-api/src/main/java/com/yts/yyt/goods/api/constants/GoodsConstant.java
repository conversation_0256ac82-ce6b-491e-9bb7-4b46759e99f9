package com.yts.yyt.goods.api.constants;

import java.math.BigDecimal;

/**
 * @Description: redis存储
 * <AUTHOR>
 * @date 2025/1/17
 */
public class GoodsConstant {
	/**
	 * 公示周期常量
	 */
    public final static BigDecimal publicity_amount=new BigDecimal("500000");
    public final static String publicity_7="7";
    public final static String publicity_15="15";
    public final static String publicity_2="2";
    public final static String publicity_period="publicity_period";
    public final static String publicity_period_fifty_under="publicity_period_fifty_under";
    public final static String publicity_period_fifty_above="publicity_period_fifty_above";
    public final static String publicity_period_fix="publicity_period_fix";

	/**
	 * 商品导入字典配置项
	 */
	public final static String goods_import_count_limit="goods_import_count_limit";
	public final static String main_img_key="main_img_key";
	public final static String detail_img_key="detail_img_key";
	/**
	 * 藏品cos目录
	 */
	public final static String store_goods_dir="storeGoods";
	/**
	 * 图片工作流id
	 */
	public final static String IMG_WORKFLOW_ID="IMG_WORKFLOW_ID";

	/**
	 *  系统参数KEY
	 */
	//二维码URL
	public final static String GOODS_QR_CODE_URL = "GOODS_QR_CODE_URL";
	//佣金计算日期分割
	public final static String GOODS_COMMISSION_CALC_DATE_SPLIT = "GOODS_COMMISSION_CALC_DATE_SPLIT";

	/**
	 *  视为鉴定失败的年代-字典配置key
	 */
	public final static String GOODS_FAIL_AGE = "GOODS_FAIL_AGE";
}
