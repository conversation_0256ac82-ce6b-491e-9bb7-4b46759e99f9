package com.yts.yyt.goods.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(description = "商品交易记录查询查询")
public class SearchGoodsTransactionRecordDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "商品id")
    @NotNull(message = "商品id不能为空")
    private Long goodsId;
}
