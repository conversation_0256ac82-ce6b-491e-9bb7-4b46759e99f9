package com.yts.yyt.goods.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: 拍品相关枚举
 *
 */
public class GoodsLotInfoEnum {

    /**
     * @Description: 拍品状态枚举
     */
    @Getter
    public  enum State{
		WAIT_SELLING("wait_selling","待上架"),
        SELLING("selling","销售中"),
        LOCKING("locking","锁定中"),
        SOLD("sold","已销售");
        private String code;
        private String desc;
        State(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
        /**
         * @Description: 通过code获取枚举
         */
        public static State getByCode(String code) {
            for (State state : State.values()) {
                if (state.getCode().equals(code)) {
                    return state;
                }
            }
            return null;
        }

        public static State getByDesc(String desc) {
            for (State state : State.values()) {
                if (state.getDesc().equals(desc)) {
                    return state;
                }
            }
            return null;
        }

    }

    @Getter
    @AllArgsConstructor
    public enum DistEnableEnum {

        /**
         * 未分销
         */
        NO_DIST(0, "未分销"),

        /**
         * 分销中
         */
        DIST(1, "分销中");

        /**
         * 类型编码
         */
        private final Integer code;

        /**
         * 类型描述
         */
        private final String desc;

    }

}
