package com.yts.yyt.goods.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(description = "商品审核入参")
public class GoodsAuditDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "藏品id")
	@NotNull(message = "藏品id不能不能未空")
    private Long id;

    /**
     * @see com.yts.yyt.goods.api.enums.StoreGoodsInfoEnum.AuditStatus
     * 审核状态 通过=approve 拒绝=reject
     */
    @NotBlank(message = "审核状态不能为空")
    @Schema(description = "审核状态 通过=approve 拒绝=reject")
    private String status;

}
