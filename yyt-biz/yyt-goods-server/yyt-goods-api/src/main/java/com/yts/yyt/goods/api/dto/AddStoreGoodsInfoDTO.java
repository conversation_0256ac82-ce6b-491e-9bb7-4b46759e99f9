package com.yts.yyt.goods.api.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yts.yyt.common.core.validation.UrlValidation;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@Schema(description = "新增商品信息")
public class AddStoreGoodsInfoDTO  implements Serializable {
    
    @Schema(description = "主键")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @Schema(description = "名称")
//    @NotBlank(message = "商品名称不能为空")
    @Size(max = 30, message = "商品名称不能超过30个字符")
    private String name;

    /**
     * 商品年代
     */
    @Schema(description="商品年代")
    private String goodsEra;
    /**
     * 商品品相
     */
    @Schema(description="商品品相")
    private String goodsCondition;
    /**
     * 商品尺寸
     */
    @Schema(description="商品尺寸")
    private String goodsSize;

    @Schema(description = "商品主图")
    @UrlValidation
    private List<ImageUrlDTO> mainImageList;

    @Schema(description = "商品缩略图")
    @UrlValidation
    private String previewImage;

    @Schema(description = "商品背景图")
    @UrlValidation
    private String backgroundImage;

    @Schema(description = "销售价格")
    private BigDecimal salePrice;

    @Schema(description = "库存数量(默认1)")
    private Integer stockQuantity=1;



    @Schema(description = "排序")
    private Integer sort;

    /**
     *  产地
     */
    @Schema(description=" 产地")
    private String placeOfOrigin;
    /**
     *  藏品描述
     */
    @Schema(description="藏品描述")
    private String description;

    /**
     * 权属
     */
    @Schema(description="权属")
    private String ownership;


    /**
     * 商户id
     */
    @Schema(description="商户id")
    private Long merchantId;

    /**
     * 商品编码
     */
    @Schema(description="商品编码")
    private String goodsNo;

    

}
