package com.yts.yyt.goods.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(description = "商品服务返回")
public class StoreGoodsConutVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 全部
     */
    @Schema(description="全部")
    Integer allCount;
    
    /**
     * 待鉴定
     */
    @Schema(description="待鉴定")
    Integer preAppraisalCount;

    /**
     * 鉴定中
     */
    @Schema(description="鉴定中")
    Integer waitAppraisalCount;

	/**
	 * 鉴定失败
	 */
	@Schema(description="鉴定失败")
	Integer failAppraisalCount;

    /**
     * 待公示
     */
    @Schema(description="待公示")
    Integer prePublicityCount;

    /**
     * 公示中
     */
    @Schema(description="公示中")
    Integer publicityCount;


}
