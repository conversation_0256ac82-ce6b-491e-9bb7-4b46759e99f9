package com.yts.yyt.goods.api.dto;

import lombok.Data;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

@Data
public class GoodsFootmarkDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 用户id
     */
    @Schema(description = "用户Id",hidden = true)
    private Long userId;

    /**
     * 商品id
     */
    @NotNull(message = "拍品Id不能为空")
    @Schema(description = "拍品Id")
    private Long lotId;
}
