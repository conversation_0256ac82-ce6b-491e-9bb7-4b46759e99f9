package com.yts.yyt.goods.api.dto;

import com.yts.yyt.common.core.entity.BasePage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "查询商品")
public class GoodsSearchV2DTO extends BasePage {

	@Schema(description = "查询的关键字")
	private String searchKey;

    @Schema(description = "品相")
    private List<String> goodsConditions;

    @Schema(description = "年代")
    private List<String> goodsEras;

    @Schema(description = "当前价格最小")
    private BigDecimal salePriceMin;

    @Schema(description = "当前价格最大")
    private BigDecimal salePriceMax;

    @Schema(description = "价格排序(1 正序 -1 倒序)")
    private Integer currentPriceSort;

    @Schema(description = "状态")
    private String state;

    @Schema(description = "时间戳（单位：秒）")
    private Long timestamp;

    @Schema(description = "最后一条数据的排序值（第一页不传，翻页时传服务端返回的）")
    private Object[] sortValues;
}
