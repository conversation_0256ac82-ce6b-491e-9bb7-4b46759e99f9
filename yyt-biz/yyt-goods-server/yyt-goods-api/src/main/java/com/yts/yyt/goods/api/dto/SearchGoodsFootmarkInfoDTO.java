package com.yts.yyt.goods.api.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class SearchGoodsFootmarkInfoDTO {

    /**
     * 商品id
     *
     */
    private Long goodsId;

	/**
	 * 商品id
	 *
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long lotId;


	/**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品主图
     */
    private String mainImage;

    /**
     * 视频
     */
    private String explanatoryVideo;

    /**
     * 商品现价
     */
    private BigDecimal currentPrice;

    /**
     * 商品类型(first 首发 consignment寄售)
     */
    private String goodsType;

    /**
     * 访问日期
     */
    private LocalDate accessDate;



}
