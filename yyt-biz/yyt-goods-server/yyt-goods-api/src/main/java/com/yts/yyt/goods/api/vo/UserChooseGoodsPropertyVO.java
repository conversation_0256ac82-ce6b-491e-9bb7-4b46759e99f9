package com.yts.yyt.goods.api.vo;

import com.yts.yyt.goods.api.entity.GoodsCategoryEntity;
import com.yts.yyt.goods.api.entity.GoodsPropertyEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 功能 用户可选的商品属性
 * 配合字典：USER_INTEREST_TYPE 使用
 * 创建于 2025/3/13 15:38
 * <AUTHOR>
 */
@Data
@Schema(description = "用户可选的商品属性")
public class UserChooseGoodsPropertyVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 窑口(获取商品二级分类数据)
     */
    @Schema(description = "窑口")
    private List<GoodsCategoryEntity> goodsKilnList;

    /**
     * 年代
     */
    @Schema(description = "年代")
    private List<GoodsPropertyEntity> goodsEraList;

    /**
     * 颜色
     */
    @Schema(description = "颜色")
    private List<GoodsPropertyEntity> goodsColorList;

    /**
     * 花纹
     */
    @Schema(description = "花纹")
    private List<GoodsPropertyEntity> goodsPatternList;

    /**
     * 器形
     */
    @Schema(description = "器形")
    private List<GoodsPropertyEntity> goodsShapeList;

}
