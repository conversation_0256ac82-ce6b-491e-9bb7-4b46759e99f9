package com.yts.yyt.goods.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: 商品相关枚举
 * <AUTHOR>
 * @date 2025/1/6
 */
public class StoreGoodsInfoEnum {

    /**
     * @Description: 商品状态枚举
     * <AUTHOR>
     * @date 2025/1/6
     */
    @Getter
    public  enum State{
        PRE_APPRAISAL("pre_appraisal","待鉴定"),
        WAIT_APPRAISAL("wait_appraisal","鉴定中"),
        FAIL_APPRAISAL("fail_appraisal","鉴定失败"),
		PRE_PUBLICITY("pre_publicity","待公示"),
		PUBLICITY("publicity","公示中"),
        //审核中
        AUDITING("auditing","审核中"),
        //已公示/已完成
		COMPLETED("completed","已完成"),
        ;
        private String code;
        private String desc;
        State(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
        /**
         * @Description: 通过code获取枚举
         * <AUTHOR>
         * @date 2025/1/8
         */
        public static State getByCode(String code) {
            for (State state : State.values()) {
                if (state.getCode().equals(code)) {
                    return state;
                }
            }
            return null;
        }

        public static State getByDesc(String desc) {
            for (State state : State.values()) {
                if (state.getDesc().equals(desc)) {
                    return state;
                }
            }
            return null;
        }
    }
    /**
     * @Description: 商品状态枚举
     * <AUTHOR>
     * @date 2025/1/6
     */
    @Getter
    public  enum Type{
        FIRST("first","首发"),
        CONSIGNMENT("consignment","寄售"),
        BUY_BACK("buy_back","回购"),
        AUCTION("auction","竞拍"),
        STORE("store","店铺"),
        ;
        private String code;
        private String desc;
        Type(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    /**
     * @Description: 商品展示枚举
     * <AUTHOR>
     * @date 2025/1/6
     */
    @Getter
    public enum ShowTag{
        AUCTION ("auction","商城竞拍"),
        CONSIGNMENT("consignment","商城寄售"),
        SELF_OPERATED("self_operated","商城自营"),
        STORE("store","店铺销售"),
        ;
        private String code;
        private String desc;
        ShowTag(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

		public static ShowTag getByDesc(String desc) {
			for (ShowTag state : ShowTag.values()) {
				if (state.getDesc().equals(desc)) {
					return state;
				}
			}
			return null;
		}
    }

    @Getter
    public enum AppraisalState{
        APPRAISAL_FAILED(-1, "鉴定失败"),
        PRE_APPRAISAL(0, "待鉴定"),
        APPRAISAL_SUCCESS(1, "鉴定成功"),
        ;
        private Integer code;
        private String desc;

        AppraisalState(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    @AllArgsConstructor
    @Getter
    public enum AuditStatus {

        //  通过=approve 拒绝=reject
        APPROVE("approve", "通过"),
        REJECT("reject", "拒绝")
        ;

        private String status;
        private String desc;

    }
}
