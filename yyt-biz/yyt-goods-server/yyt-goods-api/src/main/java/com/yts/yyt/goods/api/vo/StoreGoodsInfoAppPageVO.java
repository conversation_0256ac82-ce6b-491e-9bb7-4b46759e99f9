package com.yts.yyt.goods.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Schema(description = "公示拍品查询返回")
@NoArgsConstructor
public class StoreGoodsInfoAppPageVO {

    @Schema(description = "拍品列表")
    private List<StoreGoodsInfoAppVO> records;

    @Schema(description = "随机因子")
    private Long seed;

    @Schema(description = "最后一条数据的排序值")
    private Object[] sortValues;

    @Schema(description = "时间戳")
    private Long timestamp;

    public StoreGoodsInfoAppPageVO(List<StoreGoodsInfoAppVO> records) {
        this.records = records;
    }
}
