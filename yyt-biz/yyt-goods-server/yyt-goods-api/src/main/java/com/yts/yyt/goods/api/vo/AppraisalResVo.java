package com.yts.yyt.goods.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(description = "域鉴鉴定回调结果VO")
public class AppraisalResVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 第三方鉴定id
     */
    @Schema(description = "第三方鉴定id")
    private String outAppraisalId;

    /**
     * 鉴定估值
     */
    @Schema(description = "鉴定估值")
    private BigDecimal appraisePrice;
    
    /**
     * 鉴定时间
     */
    @Schema(description = "鉴定时间")
    private LocalDateTime appraisalTime;

    /**
     * 鉴定备注
     */
    @Schema(description = "鉴定备注")
    private String appraisalRemark;

    /**
     * 专家回复断代
     */
    @Schema(description = "专家回复断代")
    private String appraisalEra;

    /**
     * 是否官窑 0-否; 1-是
     */
    @Schema(description="是否官窑 0-否; 1-是")
    private String appraisalGuanWare;

    /**
     * 专家编号
     */
    @Schema(description = "专家编号")
    private String appraisalExpertNum;

    /**
     * 专家名称
     */
    @Schema(description = "专家名称")
    private String appraisalExpertName;
}
