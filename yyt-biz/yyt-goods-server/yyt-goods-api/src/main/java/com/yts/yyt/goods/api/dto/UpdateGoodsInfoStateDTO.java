package com.yts.yyt.goods.api.dto;

import com.yts.yyt.goods.api.enums.GoodsInfoEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Schema(description = "更新商品信息")
public class UpdateGoodsInfoStateDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "商品id")
    private Long id;

    /**@see GoodsInfoEnum.State */
    @Schema(description = "状态: selling 销售中, sold 已销售, in_warehouse 仓库中")
    private GoodsInfoEnum.State state;

    /**@see GoodsInfoEnum.ShowTag */
    @Schema(description = "商品展示标签(竞拍 auction 寄售 consignment 自营self_operated)")
    private GoodsInfoEnum.ShowTag showTag;

    @Schema(description = "当前价格")
    private BigDecimal currentPrice;


}
