package com.yts.yyt.goods.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "检查商品结果")
public class CheckGoodsVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "商品年代")
    private Boolean all;

    @Schema(description = "商品ids")
    private List<Long> ids;
}
