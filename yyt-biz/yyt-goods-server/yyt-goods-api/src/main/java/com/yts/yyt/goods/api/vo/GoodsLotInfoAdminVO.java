package com.yts.yyt.goods.api.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "商品服务返回")
public class GoodsLotInfoAdminVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @Schema(description = "商品名称")
    private String name;

    @Schema(description = "藏品id")
    private String goodsId;

    @Schema(description = "商品编码")
    private String goodsNo;

    @Schema(description = "商品主图")
    private String mainImage;

    @Schema(description = "商品主图List")
    private List mainImageList;

	/**
	 * 主图第一张： _ms
	 */
	@Schema(description = "商品主图-第一张  ")
	private String imgUrl;

    @Schema(description = "销售价")
    private BigDecimal salePrice;

	/**@see com.yts.yyt.goods.api.enums.GoodsLotInfoEnum.State */
	@Schema(description = "状态: wait_selling 待上架, selling 销售中, sold 已销售, in_warehouse 仓库中")
	private String state;

	@Schema(description = "商品年代")
    private String goodsEra;

	@Schema(description = "店铺id")
    private Long merchantId;

	@Schema(description = "店铺名称")
	private String shopName;

	/**
	 * 店铺类型
	 */
	@Schema(description = "店铺类型")
	private Integer shopType;

	/**
	 * 店铺类型
	 */
	@Schema(description = "店铺类型名称")
	private String shopTypeName;

    /**
     * 店铺头像
     */
    @Schema(description = "店铺头像")
    private String shopAvatar;

    /**
     * 店铺联系人姓名
     */
    @Schema(description = "店铺联系人姓名")
    private String contactPersonName;

    /**
     * 店铺联系电话
     */
    @Schema(description = "店铺联系电话")
    private String merchantContactPhone;

    /**
     * 添加时间
     */
    @Schema(description = "添加时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

	/**
	 * 鉴定状态
	 */
	@Schema(description="鉴定状态 1-鉴定成功")
	private String appraisalState;

	@Schema(description = "激活状态： 3-激活成功，其他-未激活")
	private Integer activeStatus ;

	@Schema(description = "分销状态： 1-分销中，0-未分销")
	private Integer distEnable;

	@Schema(description = "分销审核状态 1:待审核 0:否")
	private Integer waitAudit;

}
