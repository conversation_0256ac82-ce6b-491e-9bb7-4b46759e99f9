package com.yts.yyt.goods.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(description = "商品标签查询")
public class GoodsLabelDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "排序方式(1 倒序 0 正序)")
    private Integer sortType;
}
