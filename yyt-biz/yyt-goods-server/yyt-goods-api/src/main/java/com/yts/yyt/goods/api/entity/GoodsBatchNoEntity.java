package com.yts.yyt.goods.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品批次号管理
 *
 * <AUTHOR>
 * @date 2025-01-10 16:48:47
 */
@Data
@TableName("goods_batch_no")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "商品批次号管理")
public class GoodsBatchNoEntity extends Model<GoodsBatchNoEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键")
    private Long id;

	/**
	* 批次号
	*/
    @Schema(description="批次号")
    private String batchNo;

	/**
	* 商品数量
	*/
    @Schema(description="商品数量")
	@TableField(exist = false)
    private Integer number;

	/**
	* 限制人群
	*/
    @Schema(description="限制人群")
    private String limitCode;

	/**
	* 描述
	*/
    @Schema(description="描述")
    private String description;
}