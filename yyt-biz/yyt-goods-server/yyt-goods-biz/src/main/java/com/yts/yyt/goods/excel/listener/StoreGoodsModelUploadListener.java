package com.yts.yyt.goods.excel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.yts.yyt.goods.excel.model.StoreGoodsModel;


import java.util.ArrayList;
import java.util.List;

public class StoreGoodsModelUploadListener extends AnalysisEventListener<StoreGoodsModel> {
    private List<StoreGoodsModel> goodsList = new ArrayList<>();



    // 每解析一行数据会调用此方法
    @Override
    public void invoke(StoreGoodsModel userData, AnalysisContext analysisContext) {
        goodsList.add(userData);
    }

    // 所有数据解析完成后调用此方法
    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
    }

    public List<StoreGoodsModel> getUserList() {
        return goodsList;
    }
}
