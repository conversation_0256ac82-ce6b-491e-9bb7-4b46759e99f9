package com.yts.yyt.goods.task;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yts.yyt.goods.antchain.service.AntChainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class GoodsUpChainTask {

    @Autowired
    private AntChainService antChainService;

    @XxlJob("GOODS_UP_CHAIN_HANDLE")
    public ReturnT<String> upChain(String param) {
        antChainService.consumerPendingOfLock();
        return  ReturnT.SUCCESS;
    }
}
