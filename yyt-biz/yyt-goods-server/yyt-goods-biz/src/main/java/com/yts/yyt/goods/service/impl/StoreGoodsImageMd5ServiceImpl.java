package com.yts.yyt.goods.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yts.yyt.goods.api.entity.StoreGoodsImageMd5Entity;
import com.yts.yyt.goods.mapper.StoreGoodsImageMd5Mapper;
import com.yts.yyt.goods.service.StoreGoodsImageMd5Service;
import org.springframework.stereotype.Service;

/**
 * 商品主图MD5信息Service实现类
 */
@Service
public class StoreGoodsImageMd5ServiceImpl extends ServiceImpl<StoreGoodsImageMd5Mapper, StoreGoodsImageMd5Entity> implements StoreGoodsImageMd5Service {

    @Override
    public Boolean isExistByMainImageMd5(String mainImageMd5) {
        LambdaQueryWrapper<StoreGoodsImageMd5Entity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StoreGoodsImageMd5Entity::getMainImageMd5, mainImageMd5)
                .eq(StoreGoodsImageMd5Entity::getDelFlag, 0);
        Long ct = this.count(wrapper);
        return ct > 0;
    }

    @Override
    public Boolean removeByGoodsId(Long goodsId) {
        LambdaQueryWrapper<StoreGoodsImageMd5Entity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StoreGoodsImageMd5Entity::getStoreGoodsId, goodsId)
                .eq(StoreGoodsImageMd5Entity::getDelFlag, 0);
        return remove(wrapper);
    }
}