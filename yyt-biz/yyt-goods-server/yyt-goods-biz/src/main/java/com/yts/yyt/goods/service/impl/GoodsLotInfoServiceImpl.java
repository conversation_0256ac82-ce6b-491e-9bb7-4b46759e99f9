package com.yts.yyt.goods.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yts.yyt.admin.api.feign.RemoteParamService;
import com.yts.yyt.admin.api.utils.PageUtil;
import com.yts.yyt.common.core.dto.IdDTO;
import com.yts.yyt.common.core.util.IDS;
import com.yts.yyt.common.es.EsService;
import com.yts.yyt.common.es.config.EsIndexEnum;
import com.yts.yyt.common.es.model.GoodsLotModel;
import com.yts.yyt.common.file.service.ImageUrlService;
import com.yts.yyt.common.rocketmq.config.EnvAwareRocketMQTemplate;
import com.yts.yyt.common.security.util.SecurityUtils;
import com.yts.yyt.distribution.api.vo.CalculateCommissionVO;
import com.yts.yyt.goods.api.constants.GoodsConstant;
import com.yts.yyt.goods.api.dto.*;
import com.yts.yyt.goods.api.entity.GoodsLotInfo;
import com.yts.yyt.goods.api.entity.StoreGoodsInfo;
import com.yts.yyt.goods.api.enums.GoodsLotInfoEnum;
import com.yts.yyt.goods.api.exception.GoodsBizErrorEnum;
import com.yts.yyt.goods.api.exception.GoodsException;
import com.yts.yyt.goods.api.vo.*;
import com.yts.yyt.goods.event.LotStatusChangeEvent;
import com.yts.yyt.goods.helper.ListTool;
import com.yts.yyt.goods.mapper.GoodsLotInfoMapper;
import com.yts.yyt.goods.mq.dto.LotStatusSyncMqDTO;
import com.yts.yyt.goods.service.*;
import com.yts.yyt.merchant.api.dto.MerchantDTO;
import com.yts.yyt.merchant.api.enums.MerchantShopTypeEnum;
import com.yts.yyt.user.api.constant.enums.HuifuAccountStatus;
import com.yts.yyt.user.api.vo.UserHuifuAccountQueryVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.common.lucene.search.function.CombineFunction;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.index.query.functionscore.FunctionScoreQueryBuilder;
import org.elasticsearch.index.query.functionscore.RandomScoreFunctionBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("goodsLotInfoService")
@Slf4j
@RequiredArgsConstructor
public class GoodsLotInfoServiceImpl extends ServiceImpl<GoodsLotInfoMapper, GoodsLotInfo>
 implements GoodsLotInfoService {

	private final BaseRemoteService baseRemoteService;
	@Lazy
	private final StoreGoodsInfoService storeGoodsInfoService;
    private final ImageUrlService imageUrlService;
	private final GoodsImageInfoService goodsImageInfoService;
	private final EsService esService;
	private final GoodsTraceabilityPreService goodsTraceabilityPreService;
	private final EnvAwareRocketMQTemplate envAwareRocketMQTemplate;
	private final GoodsCommissionRecordService goodsCommissionRecordService;
	private final ApplicationEventPublisher eventPublisher;
	private final RemoteParamService remoteParamService;

	/**
	 * 客户端-首页-分页查询
	 * @param search search
	 * @return res
	 */
	@Override
	public List<GoodsLotInfoAppVO> pageForSearch(GoodsSearchDTO search) {
		//构建查询条件
		QueryWrapper<GoodsLotInfo> wrapper = new QueryWrapper<>();
		wrapper.eq("a.state", StrUtil.isBlank(search.getState()) ? GoodsLotInfoEnum.State.SELLING.getCode() : search.getState())
				.eq("a.del_flag", '0')
				.and(StringUtils.isNotBlank(search.getSearchKey()), w -> w.like("a.name", search.getSearchKey())
						.or().eq("a.goods_no", search.getSearchKey())
						.or().like("m.shop_name", search.getSearchKey()))
				.in(ListTool.isNotEmpty(search.getGoodsConditions()), "a.goods_condition", search.getGoodsConditions())
				.in(ListTool.isNotEmpty(search.getGoodsEras()), "tyga.age_name", search.getGoodsEras())
				.ge(ObjUtil.isNotNull(search.getSalePriceMin()), "a.sale_price", search.getSalePriceMin())  //前端展示的：销售价格，和前端保持一致
				.le(ObjUtil.isNotNull(search.getSalePriceMax()), "a.sale_price", search.getSalePriceMax())
		;
		//只展示已经激活的商户的拍品
		wrapper.isNotNull("m.huifu_id");
		//只能查询元末明初以后的数据
		wrapper.eq("tyga.status", 1);
		//排序
		if(search.getCurrentPriceSort()!=null){
			if(search.getCurrentPriceSort()>0){
				wrapper.orderByAsc("a.sale_price");
			}else{
				wrapper.orderByDesc("a.sale_price");
			}
		}
		wrapper.groupBy("a.id");
		wrapper.orderByDesc("a.id");
		wrapper.orderByDesc("a.sort"); //数字越大，越考前
		//查询
		List<GoodsLotInfoAppVO> list = this.baseMapper.listForSearch(PageUtil.toPage(search), wrapper);
		if (ListTool.isEmpty(list)){
			return Collections.emptyList();
		}
		//处理主图
		list.parallelStream().forEach(goods -> {
			if (StrUtil.isBlank(goods.getMainImage())) {
				return;
			}
			goods.setImgUrl(imageUrlService.convertImageUrl(goods.getMainImage()).getSmallWebpUrl());
			goods.setShopAvatar(imageUrlService.convertImageUrl(goods.getShopAvatar()).getSmallUrl());
			// goods.setMainImage(imageUrlService.convertImageUrl(goods.getMainImage()).getOriginUrl());

		});
		return list;
	}

	public GoodsLotInfoAppPageVO pageForSearchV2(GoodsSearchV2DTO search) {
		log.info("【GoodsLotInfoServiceImpl.pageForSearchV2】 拍品分页查询，params：{}", JSONObject.toJSONString(search));

		if (StringUtils.isBlank(search.getSearchKey())) {
			return new GoodsLotInfoAppPageVO(Collections.emptyList());
		}

		Long timestamp = search.getTimestamp();
		if (ObjUtil.isNull(timestamp)) {
			timestamp = System.currentTimeMillis() / 1000;
		}

		SearchRequest searchRequest = new SearchRequest(EsIndexEnum.GOODS_LOT.getIndexName());
		searchRequest.preference(String.valueOf(timestamp));

		SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
		BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
		boolQuery.must(
				QueryBuilders.boolQuery()
						.should(QueryBuilders.matchQuery("name", search.getSearchKey()))
						.should(
								QueryBuilders.matchQuery("name", search.getSearchKey())
										.operator(Operator.AND)
										.analyzer("ik_smart")
										.boost(1.5F)
						)
						.should(QueryBuilders.matchQuery("shopName", search.getSearchKey()).boost(0.8F))
						.should(QueryBuilders.termQuery("shopName.raw", search.getSearchKey()))
						.should(QueryBuilders.termQuery("goodsNo", search.getSearchKey()))
		);

		if (ListTool.isNotEmpty(search.getGoodsConditions())) {
			boolQuery.filter(QueryBuilders.termsQuery("goodsCondition", search.getGoodsConditions()));
		}

		if (ListTool.isNotEmpty(search.getGoodsEras())) {
			boolQuery.filter(QueryBuilders.termsQuery("goodsEra", search.getGoodsEras()));
		}

		RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("salePrice");
		if (ObjUtil.isNotNull(search.getSalePriceMin())) {
			rangeQueryBuilder.gte(search.getSalePriceMin());
		}
		if (ObjUtil.isNotNull(search.getSalePriceMax())) {
			rangeQueryBuilder.lte(search.getSalePriceMax());
		}
		if (ObjUtil.isNotNull(search.getSalePriceMin()) || ObjUtil.isNotNull(search.getSalePriceMax())) {
			boolQuery.filter(rangeQueryBuilder);
		}

		// 防止重复数据，比如翻页时新上架了藏品
//		boolQuery.filter(QueryBuilders.rangeQuery("createTime").lte(timestamp));

		// 只查询出售中的
		boolQuery.filter(QueryBuilders.termQuery("state", GoodsLotInfoEnum.State.SELLING.getCode()));

		if (ObjUtil.isNotNull(search.getCurrentPriceSort())) {
			if (search.getCurrentPriceSort() > 0) {
				sourceBuilder.sort("salePrice", SortOrder.ASC);
			} else {
				sourceBuilder.sort("salePrice", SortOrder.DESC);
			}
		}
		sourceBuilder.sort("_score", SortOrder.DESC);
		sourceBuilder.sort("id", SortOrder.DESC);

		// 上次排序的值
		if (ArrayUtil.isNotEmpty(search.getSortValues())) {
			sourceBuilder.searchAfter(search.getSortValues());
		}

		sourceBuilder.size(search.getSize().intValue());
		sourceBuilder.query(boolQuery);
		searchRequest.source(sourceBuilder);
		SearchResponse searchResponse = esService.search(searchRequest);
		if (searchResponse == null || searchResponse.status().getStatus() != 200) {
			return new GoodsLotInfoAppPageVO(Collections.emptyList());
		}

		GoodsLotInfoAppPageVO page = getGoodsLotPage(searchResponse);
		page.setTimestamp(timestamp);
		log.info("【pageForSearchV2】 拍品分页查询结果size：{}", page.getRecords().size());
		return page;
	}

	@Override
	public GoodsLotInfoAppPageVO pageForRecommendV1(GoodsRecommendV1DTO dto) {
		log.info("【GoodsLotInfoServiceImpl.pageForRecommendV1】 拍品分页查询，params：{}", JSONObject.toJSONString(dto));

		SearchRequest searchRequest = new SearchRequest(EsIndexEnum.GOODS_LOT.getIndexName());
		SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();

		Long seed = dto.getSeed();
		Object[] sortValues = dto.getSortValues();
		if (ObjUtil.isNull(seed) || ArrayUtil.isEmpty(sortValues)) {
			seed = Math.abs(RandomUtil.randomLong());
			sortValues = new Object[0];
		}

		BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
		if (ListTool.isNotEmpty(dto.getGoodsConditions())) {
			boolQuery.filter(QueryBuilders.termsQuery("goodsCondition", dto.getGoodsConditions()));
		}

		if (ListTool.isNotEmpty(dto.getGoodsEras())) {
			boolQuery.filter(QueryBuilders.termsQuery("goodsEra", dto.getGoodsEras()));
		}

		RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("salePrice");
		if (ObjUtil.isNotNull(dto.getSalePriceMin())) {
			rangeQueryBuilder.gte(dto.getSalePriceMin());
		}
		if (ObjUtil.isNotNull(dto.getSalePriceMax())) {
			rangeQueryBuilder.lte(dto.getSalePriceMax());
		}
		if (ObjUtil.isNotNull(dto.getSalePriceMin()) || ObjUtil.isNotNull(dto.getSalePriceMax())) {
			boolQuery.filter(rangeQueryBuilder);
		}

		// 只查询出售中的
		boolQuery.filter(QueryBuilders.termQuery("state", GoodsLotInfoEnum.State.SELLING.getCode()));

		FunctionScoreQueryBuilder functionScoreQuery = QueryBuilders.functionScoreQuery(
				boolQuery,
				new RandomScoreFunctionBuilder().seed(seed).setField("goodsId")
		);
		functionScoreQuery.boostMode(CombineFunction.REPLACE);

		// 上次排序的值
		if (ArrayUtil.isNotEmpty(sortValues)) {
			sourceBuilder.searchAfter(sortValues);
		}
//		sourceBuilder.sort("sort", SortOrder.DESC);
		sourceBuilder.sort("_score", SortOrder.DESC);
		sourceBuilder.sort("id", SortOrder.DESC);

		sourceBuilder.size(dto.getSize().intValue());
		sourceBuilder.query(functionScoreQuery);
		searchRequest.source(sourceBuilder);
		SearchResponse searchResponse = esService.search(searchRequest);
		if (searchResponse == null || searchResponse.status().getStatus() != 200) {
			return new GoodsLotInfoAppPageVO(Collections.emptyList());
		}

		GoodsLotInfoAppPageVO page = getGoodsLotPage(searchResponse);
		page.setSeed(seed);
		log.info("【pageForRecommend】 拍品分页查询结果size：{}", page.getRecords().size());
		return page;
	}

	private GoodsLotInfoAppPageVO getGoodsLotPage(SearchResponse searchResponse) {

		Object[] sortValues = new Object[0];
		List<GoodsLotInfoAppVO> records = new ArrayList<>();
		for (SearchHit hit : searchResponse.getHits().getHits()) {

			Long id = Long.valueOf(hit.getId());
			Map<String, Object> sourceAsMap = hit.getSourceAsMap();
			String mainImage = MapUtils.getString(sourceAsMap, "mainImage");
			String shopAvatar = MapUtils.getString(sourceAsMap, "shopAvatar");

			GoodsLotInfoAppVO goods = new GoodsLotInfoAppVO();
			goods.setId(id);
			goods.setName(MapUtils.getString(sourceAsMap, "name"));
			goods.setGoods_id(MapUtils.getLong(sourceAsMap, "goodsId"));
			goods.setShopName(MapUtils.getString(sourceAsMap, "shopName"));
			goods.setState(MapUtils.getString(sourceAsMap, "state"));
			goods.setMainImage(mainImage);
			goods.setImgUrl(imageUrlService.convertImageUrl(mainImage).getSmallWebpUrl());
			goods.setShopAvatar(imageUrlService.convertImageUrl(shopAvatar).getSmallUrl());
			goods.setSalePrice(new BigDecimal(MapUtils.getString(sourceAsMap, "salePrice")));
			goods.setGoodsEra(MapUtils.getString(sourceAsMap, "goodsEra"));
			goods.setMerchantId(MapUtils.getLong(sourceAsMap, "merchantId"));
			goods.setBenefitLevel(MapUtils.getString(sourceAsMap, "benefitLevel"));
			goods.setTranName(MapUtils.getString(sourceAsMap, "tranName"));
			goods.setTranCode(MapUtils.getString(sourceAsMap, "tranCode"));
			goods.setTranIcon(MapUtils.getString(sourceAsMap, "tranIcon"));
			records.add(goods);

			sortValues = hit.getSortValues();
		}

		GoodsLotInfoAppPageVO page = new GoodsLotInfoAppPageVO();
		page.setRecords(records);
		page.setSortValues(sortValues);
		return page;
	}

	/**
	 * 拍品详情
	 * @param dto  ids
	 * @return res
	 */
	@Override
	public GoodsLotInfoVO detail(GoodsLotDetailDTO dto, Boolean isAdmin) {
		log.info("【GoodsLotInfoServiceImpl.detail】 拍品详情，入参：{}",JSONObject.toJSONString(dto));
		if (StrUtil.isBlank(dto.getId())) {
			throw GoodsException.build(GoodsBizErrorEnum.PARAMS_ERROR);
		}
		// 查询藏品信息
		GoodsLotInfoVO lotInfoVO = this.baseMapper.queryDetail(dto.getId());
		if (ObjUtil.isNull(lotInfoVO)) {
			throw GoodsException.build(GoodsBizErrorEnum.GOODS_IS_NULL_ERROR);
		}
		// 查询上链信息
		lotInfoVO.setUpperChainInfo(goodsTraceabilityPreService.getTraceCodeByGoodsNo(lotInfoVO.getGoodsNo()));
		// 商户类型处理
		lotInfoVO.setShopTypeName(MerchantShopTypeEnum.getByCode(lotInfoVO.getShopType()+"").getDesc());
		// 店铺头像
		lotInfoVO.setShopAvatar(imageUrlService.convertImageUrl(lotInfoVO.getShopAvatar()).getSmallUrl());
		// 切割图处理
		lotInfoVO.setImgUrl(imageUrlService.convertImageUrl(lotInfoVO.getMainImage()).getMediumUrl());
		// 详情图处理
		List<ImageUrlDTO> images = goodsImageInfoService.getImageUrlByGoodsId(lotInfoVO.getGoodsId());
		List<ImageUrlDTO> originUrlImagesList = new ArrayList<>();
		for (ImageUrlDTO image : images) {
			String imgUrl = image.getUrl();
			image.setUrl(imageUrlService.convertImageUrl(imgUrl).getMediumUrl());
			ImageUrlDTO imageUrlDTO = new ImageUrlDTO();
			imageUrlDTO.setUrl(imgUrl);
			originUrlImagesList.add(imageUrlDTO);
		}
		lotInfoVO.setMainImageList(images);
		if(isAdmin){
			lotInfoVO.setOriginImageUrl(originUrlImagesList);
		}

		// 获取佣金信息（只有登录用户才能获取）
		if (SecurityUtils.getUser() != null) {
			Long currentUserId = SecurityUtils.getUser().getId();
			if (currentUserId != null) {
				CalculateCommissionVO commissionInfo = baseRemoteService.calculateCommission(lotInfoVO.getId(), currentUserId, lotInfoVO.getSalePrice());
				lotInfoVO.setCommissionAmount(commissionInfo.getCommissionAmount());
				lotInfoVO.setIsDistributor(commissionInfo.getIsDistributor());
			}
		}

		// 处理分享码
		if(StrUtil.isNotBlank(dto.getShareCode())){
			// 记录点击统计（通过分享链接进入）
			baseRemoteService.recordDistributionClick(dto.getShareCode());
		}

		return lotInfoVO;
	}

	/**
	 * 用户查看某个店铺的商品
	 * 只能看到店铺销售中的拍品
	 * @param search search
	 * @return res
	 */
	@Override
	public Page<GoodsLotInfoAppVO> shopGoodsPage(ShopGoodsSearchDTO search) {
		log.info("【GoodsLotInfoServiceImpl.shopGoodsPage】 店铺商品，入参：{}",JSONObject.toJSONString(search));
		//参数校验
		if (ObjUtil.isNull(search) || StrUtil.isBlank(search.getMerchantId())) {
			throw GoodsException.build(GoodsBizErrorEnum.PARAMS_ERROR);
		}
		search.setState(GoodsLotInfoEnum.State.SELLING.getCode());
		return queryShopGoods(search);
	}


	/**
	 * 商家查看自己的店铺
	 * @param search search
	 * @return res
	 */
	@Override
	public Page<GoodsLotInfoAppVO> myShopGoodsPage(ShopGoodsSearchDTO search) {
		log.info("【GoodsLotInfoServiceImpl.myShopGoodsPage】 我的店铺商品，入参：{}",JSONObject.toJSONString(search));
		//参数校验
		if (StrUtil.isBlank(search.getState())) {
			throw GoodsException.build(GoodsBizErrorEnum.PARAMS_ERROR);
		}
		//根据当前登录用户查询商户id
		MerchantDTO merchantDTO = baseRemoteService.getMerchantByUserId(SecurityUtils.getUser().getId());
		search.setMerchantId(merchantDTO.getMerchantId()+"");
		return queryShopGoods(search);
	}

	@NotNull
	private Page<GoodsLotInfoAppVO> queryShopGoods(ShopGoodsSearchDTO search) {
		//根据店铺id查询商品信息,只能看到店铺销售中的拍品
		QueryWrapper<GoodsLotInfo> wrapper = new QueryWrapper<GoodsLotInfo>()
				.eq("a.del_flag", '0')
				.eq("a.merchant_id", search.getMerchantId())
				.eq("a.state", search.getState())
				.eq("tyga.status", 1)//只能查询元末明初以后的数据
				.groupBy("a.id")
				.orderByDesc("a.sale_price").orderByDesc("a.create_time");
		Page<GoodsLotInfoAppVO> pageList = this.baseMapper.queryPageData(PageUtil.toPage(search), wrapper);

		log.info("【GoodsLotInfoServiceImpl.shopGoodsPage】 店铺商品，结果：{}",JSONObject.toJSONString(search));
		//处理主图
		if (CollUtil.isNotEmpty(pageList.getRecords())) {
			pageList.getRecords().parallelStream().forEach(goods -> {
				if (StrUtil.isBlank(goods.getMainImage())) {
					return;
				}
				goods.setImgUrl(imageUrlService.convertImageUrl(goods.getMainImage()).getSmallWebpUrl());
                goods.setMainImage(imageUrlService.convertImageUrl(goods.getMainImage()).getOriginUrl());

            });
		}
		return pageList;
	}

	/**
	 * 转售
	 * @param dto 入参
	 * @return res
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean resell(MerchantResellDTO dto) {
		log.info("【GoodsLotInfoServiceImpl.resell】 转售，入参：{}", JSONObject.toJSONString(dto));
		//1、校验当前拍品是否已经转售过
		Optional<GoodsLotInfo> oneOpt = new LambdaQueryChainWrapper<>(this.baseMapper)
				.eq(GoodsLotInfo::getSourceLotId, dto.getLotId())
				.eq(GoodsLotInfo::getDelFlag, '0')
				.oneOpt();
		if (oneOpt.isPresent()) {
			log.info("【GoodsLotInfoServiceImpl.resell】 转售，拍品已经转售过，不可重复转售");
			return Boolean.FALSE;
		}
		//2、查询当前这个拍品的信息
		GoodsLotInfo dbGoods = this.queryById(dto.getLotId());
		//3、设置新的销售价、状态等
		MerchantDTO merchant = baseRemoteService.getMerchantByUserId(dto.getUserId());
		dbGoods.setId(IDS.uniqueID());
		dbGoods.setSalePrice(dto.getAmount());
		dbGoods.setState(GoodsLotInfoEnum.State.WAIT_SELLING.getCode());
		dbGoods.setMerchantId(merchant.getMerchantId());
		dbGoods.setSourceLotId(dto.getLotId()); //源拍品id
		//4、原拍品记录上orderNo（藏品详情中用）
//		this.update(new LambdaUpdateWrapper<GoodsLotInfo>()
//				.set(GoodsLotInfo::getOrderNo, dto.getOrderNo())
//				.eq(GoodsLotInfo::getId, dto.getLotId()));
		//5、保存新的拍品
		if (!this.save(dbGoods)) {
			log.info("【GoodsLotInfoServiceImpl.resell】 转售，保存拍品信息失败");
			throw GoodsException.build(GoodsBizErrorEnum.RESELL_ERROR);
		}

		return Boolean.TRUE;
	}

	/**
	 * 根据藏品记录添加拍品记录
	 * @param goodsId 藏品id
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public GoodsLotInfo saveByStoreGoodsInfo(Long goodsId) {
		log.info("【GoodsLotInfoServiceImpl.saveByStoreGoodsInfo】 鉴定成功自动添加拍品，藏品id：{}", goodsId);
		if (ObjUtil.isNull(goodsId)) {
			throw GoodsException.build(GoodsBizErrorEnum.PARAMS_ERROR);
		}
		//1、获取最新的藏品信息
		StoreGoodsInfo storeGoodsInfo = storeGoodsInfoService.getById(goodsId);
		if (ObjUtil.isNull(storeGoodsInfo)) {
			throw GoodsException.build(GoodsBizErrorEnum.GOODS_NOT_EXIST);
		}
		//2、检查当前藏品是否已经添加过拍品
		Optional<GoodsLotInfo> oneOpt = new LambdaQueryChainWrapper<>(this.baseMapper)
				.eq(StrUtil.isNotBlank(storeGoodsInfo.getGoodsNo()), GoodsLotInfo::getGoodsNo, storeGoodsInfo.getGoodsNo())
				.eq(GoodsLotInfo::getGoodsId, goodsId)
				.eq(GoodsLotInfo::getDelFlag, '0')
				.oneOpt();
		if (oneOpt.isPresent()) {
			log.info("【GoodsLotInfoServiceImpl.saveByStoreGoodsInfo】 鉴定成功自动添加拍品，该藏品已添加过拍品");
			return oneOpt.get();
		}
		//3、查询藏品所属的商户是否已经入驻成功
		UserHuifuAccountQueryVO userHuifuAccountQueryVO = baseRemoteService.getUserHuifuAccountInfoByMerchantId(storeGoodsInfo.getMerchantId());
		String state = GoodsLotInfoEnum.State.SELLING.getCode();
		if (ObjUtil.isNull(userHuifuAccountQueryVO) || !userHuifuAccountQueryVO.getStatus().equals(HuifuAccountStatus.FULLY_ACTIVATED.getCode())) {
			state = GoodsLotInfoEnum.State.WAIT_SELLING.getCode();
		}
		//4、新增拍品信息
		GoodsLotInfo goodsLotInfo = new GoodsLotInfo();
		BeanUtil.copyProperties(storeGoodsInfo, goodsLotInfo);
		goodsLotInfo.setId(IDS.uniqueID());
		goodsLotInfo.setGoodsId(storeGoodsInfo.getId()); //藏品id
		goodsLotInfo.setMerchantId(storeGoodsInfo.getMerchantId()); //所属id
		goodsLotInfo.setState(state); //状态
		goodsLotInfo.setEraId(storeGoodsInfo.getEraId());
		goodsLotInfo.setCreateTime(LocalDateTime.now());
		goodsLotInfo.setUpdateTime(LocalDateTime.now());
		goodsLotInfo.setDistEnable(GoodsLotInfoEnum.DistEnableEnum.DIST.getCode());
		log.info("【GoodsLotInfoServiceImpl.saveByStoreGoodsInfo】 鉴定成功自动添加拍品，拍品信息：{}", JSONObject.toJSONString(goodsLotInfo));
		this.save(goodsLotInfo);

		// 发布新增拍品事件（藏品公示完后新增拍品）
		publishLotStatusChangeEventsByPublicityUp(goodsLotInfo);

		// 添加藏品到es
		sendGoods2ESMsg(List.of(goodsLotInfo.getId()));

		return goodsLotInfo;
	}

	/**
	 * 逻辑删除
	 * @param dto dto
	 * @return res
	 */
	@Override
	public Boolean LogicRemoveByIds(IdDTO dto) {
		log.info("【GoodsLotInfoServiceImpl.LogicRemoveByIds】 拍品删除，入参：{}", JSONObject.toJSONString(dto));
		if (IdDTO.idAndIdsIsEmpty(dto)) {
			throw GoodsException.build(GoodsBizErrorEnum.PARAMS_ERROR);
		}
		//逻辑删除
		List<Long> ids = IdDTO.mergeList(dto);
		// 需要判断是否是已销售状态
		List<GoodsLotInfo> dbList = this.listByIds(ids);
		if (CollUtil.isEmpty(dbList)) {
			log.info("【GoodsLotInfoServiceImpl.LogicRemoveByIds】删除拍品，未查询到拍品数据");
			throw GoodsException.build(GoodsBizErrorEnum.PARAMS_ERROR);
		}
		List<GoodsLotInfo> notSoldList = dbList.stream().filter(item -> !GoodsLotInfoEnum.State.SOLD.getCode().equals(item.getState())).toList();
		if (CollUtil.isNotEmpty(notSoldList)) {
			log.info("【GoodsLotInfoServiceImpl.LogicRemoveByIds】删除拍品，存在非已销售的数据：{}", JSONObject.toJSONString(notSoldList));
			throw GoodsException.build(GoodsBizErrorEnum.GOODS_CANNOT_DEL_ERROR);
		}

		return this.removeByIds(ids);
	}

	/**
	 * 修改拍品状态
	 * @param goodsLotUpdateStateDTO dto
	 * @return res
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean updateStateWithCheck(GoodsLotUpdateStateDTO goodsLotUpdateStateDTO) {
		log.info("[店铺商品上下架]-------->>>，入参：{}",JSONObject.toJSONString(goodsLotUpdateStateDTO));
		// 入参只能是：销售中、待上架状态
		if (!GoodsLotInfoEnum.State.SELLING.getCode().equals(goodsLotUpdateStateDTO.getState())
				&& !GoodsLotInfoEnum.State.WAIT_SELLING.getCode().equals(goodsLotUpdateStateDTO.getState())) {
			throw GoodsException.build(GoodsBizErrorEnum.PARAMS_ERROR);
		}
		// 如果需要修改的拍品的db状态为：锁定中、已销售，则不可修改
		List<Long> ids = goodsLotUpdateStateDTO.getIds();
		List<GoodsLotInfo> dbList = this.listByIds(ids);
		if (CollUtil.isEmpty(dbList)) {
			log.info("[店铺商品上下架]-------->>>未查询到拍品数据");
			throw GoodsException.build(GoodsBizErrorEnum.GOODS_NOT_EXIST);
		}
		List<GoodsLotInfo> soldList = dbList.stream().filter(item -> GoodsLotInfoEnum.State.LOCKING.getCode().equals(item.getState())
				|| GoodsLotInfoEnum.State.SOLD.getCode().equals(item.getState())).toList();
		if (CollUtil.isNotEmpty(soldList)) {
			log.info("【GoodsLotInfoServiceImpl.updateState】店铺商品上下架，存在锁定中或已销售的数据：{}",JSONObject.toJSONString(soldList));
			throw GoodsException.build(GoodsBizErrorEnum.GOODS_CANNOT_EDIT_ERROR);
		}
		// 如果是上架，需要校验对应商户是否已经进件/入驻
		if (StringUtils.equals(goodsLotUpdateStateDTO.getState(), GoodsLotInfoEnum.State.SELLING.getCode())) {
			List<Long> merchantIdList = dbList.stream().map(GoodsLotInfo::getMerchantId).distinct().toList();
			for (Long merchantId : merchantIdList) {
				UserHuifuAccountQueryVO userHuifuAccountQueryVO = baseRemoteService.getUserHuifuAccountInfoByMerchantId(merchantId);
				if (ObjUtil.isNull(userHuifuAccountQueryVO) || !userHuifuAccountQueryVO.getStatus().equals(HuifuAccountStatus.FULLY_ACTIVATED.getCode())) {
					log.info("【GoodsLotInfoServiceImpl.updateState】店铺商品上下架，藏品所属的商户未入驻成功，商户id：{}", merchantId);
					throw GoodsException.build(GoodsBizErrorEnum.SHOP_NOT_ACTIVATED_ERROR);
				}
			}
			// 上架计算佣金 goodsLotInfo 的创建时间需小于 “2025-07-01 00:00:00” 才计算佣金
			String splitDate = remoteParamService.getByKey(GoodsConstant.GOODS_COMMISSION_CALC_DATE_SPLIT).getData();
			List<StoreGoodsInfo> storeGoodsInfos = storeGoodsInfoService.listByIds(dbList.stream().map(GoodsLotInfo::getGoodsId).toList())
					.stream().filter(item -> item.getCreateTime().isBefore(LocalDateTime.parse(splitDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
					).toList();
			for (StoreGoodsInfo storeGoodsInfo : storeGoodsInfos) {
				goodsCommissionRecordService.addRecord(storeGoodsInfo);
			}
			goodsLotUpdateStateDTO.setDistEnable(GoodsLotInfoEnum.DistEnableEnum.DIST.getCode());
		}else{
			goodsLotUpdateStateDTO.setDistEnable(GoodsLotInfoEnum.DistEnableEnum.NO_DIST.getCode());
		}
		// 修改
		return updateState(goodsLotUpdateStateDTO);
	}

	/**
	 * 修改拍品状态
	 * @param goodsLotUpdateStateDTO dto
	 * @return res
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean updateState(GoodsLotUpdateStateDTO goodsLotUpdateStateDTO) {
		log.info("【GoodsLotInfoServiceImpl.updateState】修改商品状态，入参：{}",JSONObject.toJSONString(goodsLotUpdateStateDTO));
		LambdaUpdateWrapper<GoodsLotInfo> updateWrapper = new LambdaUpdateWrapper<GoodsLotInfo>()
				.set(StringUtils.isNotBlank(goodsLotUpdateStateDTO.getState()),GoodsLotInfo::getState, goodsLotUpdateStateDTO.getState())
				.set(GoodsLotInfo::getDistEnable, getDistEnable(goodsLotUpdateStateDTO))
				.set(GoodsLotInfo::getUpdateTime, LocalDateTime.now())
				.set(StrUtil.isNotBlank(goodsLotUpdateStateDTO.getOrderNo()), GoodsLotInfo::getOrderNo, 
                        goodsLotUpdateStateDTO.getOrderNo())
				.in(GoodsLotInfo::getId, goodsLotUpdateStateDTO.getIds());

		boolean res = this.update(updateWrapper);

		// 发布拍品状态变更事件
		if (res) {
			publishLotStatusChangeEventsByBatchUpdateState(goodsLotUpdateStateDTO);
		}

		// 同步到es
		sendGoods2ESMsg(goodsLotUpdateStateDTO.getIds());
		return res;
	}

	/**
	 * 获取拍品分销状态
	 * 1、如果入参有distEnable，则直接返回
	 * 2、如果拍品状态为销售中，则返回分销状态
	 * 3、否则返回不参与分销
	 * @param dto dto
	 * @return res
	 */
	private Integer getDistEnable(GoodsLotUpdateStateDTO dto) {
		if(dto.getDistEnable() != null) {
			return dto.getDistEnable();
		}
		if(dto.getState().equals(GoodsLotInfoEnum.State.SELLING.getCode())) {
			return GoodsLotInfoEnum.DistEnableEnum.DIST.getCode();
		}
		return GoodsLotInfoEnum.DistEnableEnum.NO_DIST.getCode();
	}


	@Override
	public GoodsLotInfo queryById(Long id) {
		//查询拍品
		GoodsLotInfo goodsLotInfo = this.getById(id);
		if (ObjUtil.isNull(goodsLotInfo)) {
			log.info("【GoodsLotInfoServiceImpl.queryById】未查询到拍品信息");
			throw GoodsException.build(GoodsBizErrorEnum.DATA_NOT_EXIST);
		}
		return goodsLotInfo;
	}

	@Override
	public Page<GoodsLotInfoAdminVO> getBasePage(GoodsLotInfoQueryDTO queryDto) {
		log.info("【GoodsLotInfoServiceImpl.getBasePage】后台分页查询，入参：{}",JSONObject.toJSONString(queryDto));
		//如果不是管理员等角色，则只能查看相关商户的藏品数据
		if(!storeGoodsInfoService.getSelectRoleData()){
			//根据登录用户查询商户信息
			List<Long> merchantIds = baseRemoteService.getMerchantIdsBySysUserId(SecurityUtils.getUser().getId());
			if (CollUtil.isEmpty(merchantIds)) {
				log.info("【getBasePage】未查询到相关商户信息");
				return new Page<>();
			}
			queryDto.setMerchantIds(merchantIds);
		}
		Page<GoodsLotInfoAdminVO> page = this.getBaseMapper().getBasePage(PageUtil.toPage(queryDto), queryDto);
		for (int i = 0; i < page.getRecords().size(); i++) {
			GoodsLotInfoAdminVO vo = page.getRecords().get(i);
			vo.setShopTypeName(MerchantShopTypeEnum.getByCode(vo.getShopType()+"").getDesc());
			vo.setImgUrl(imageUrlService.convertImageUrl(vo.getMainImage()).getOriginUrl());
			vo.setMainImage(imageUrlService.convertImageUrl(vo.getMainImage()).getOriginUrl());
			// 店铺头像
			vo.setShopAvatar(imageUrlService.convertImageUrl(vo.getShopAvatar()).getSmallUrl());
		}
		return page;
	}

	/**
	 * 修改：目前只能修改价格
	 * @param dto dto
	 * @return res
	 */
	@Override
	public Boolean updateInfo(GoodsLotInfoUpdateDTO dto) {
		log.info("【GoodsLotInfoServiceImpl.updateInfo】后台-拍品修改，入参：{}",JSONObject.toJSONString(dto));
		if (ObjUtil.isNull(dto) || ObjUtil.isNull(dto.getSalePrice())) {
			throw GoodsException.build(GoodsBizErrorEnum.PARAMS_ERROR);
		}
		LambdaUpdateWrapper<GoodsLotInfo> updateWrapper = new LambdaUpdateWrapper<GoodsLotInfo>()
				.set(GoodsLotInfo::getSalePrice, dto.getSalePrice())
				.eq(GoodsLotInfo::getId, dto.getId());

		boolean res = this.update(updateWrapper);
		// 修改拍品同步到es
		sendGoods2ESMsg(List.of(dto.getId()));

		return res;
	}

	private void sendGoods2ESMsg(List<Long> ids) {
		//
		log.info("发送同步goods lot藏品到es消息，id列表：{}", ids);
		if (ids.isEmpty()) {
			return;
		}
		syncGoodsLot2ES(ids);


//		// 构建消息体
//		GoodsLotESMqDTO dto = new GoodsLotESMqDTO(ids);
//		Message<GoodsLotESMqDTO> message = MessageBuilder
//				.withPayload(dto)
//				.build();
//		SendReceipt sendReceipt = envAwareRocketMQTemplate.sendDelayMsgWithTag(
//                "rocketMQClientTemplate",
//				RocketMQConstants.Topic.TOPIC_GOODS_ES_GRANT,
//				RocketMQConstants.Tag.TAG_GOODS_LOT_ES_GRANT,
//				message,
//				Duration.ofSeconds(1)
//		);
//		log.info("发送同步goods lot藏品到es消息成功，message id: {}", sendReceipt.getMessageId());
	}

	@Override
    public ShopGoodsLotCountVO shopGoodsCounts(IdDTO dto) {
		if (StrUtil.isBlank(dto.getId())) {
			throw GoodsException.build(GoodsBizErrorEnum.PARAMS_ERROR);
		}
		return this.baseMapper.shopGoodsCounts(Long.valueOf(dto.getId()));
    }

	@Override
	public boolean syncGoodsLot2ES(List<Long> ids) {

		List<GoodsLotModel> modelList = this.baseMapper.queryEsModelByIds(ids);
		Map<String, GoodsLotModel> modelMap = modelList.stream().collect(Collectors.toMap(GoodsLotModel::getId, Function.identity()));

		BulkRequest bulkRequest = new BulkRequest();
		ids.stream().map(String::valueOf).forEach(id -> {
			GoodsLotModel model = modelMap.get(id);
			if (model != null) {
				IndexRequest indexRequest = new IndexRequest();
				indexRequest.index(model.getIndex().getIndexName());
				indexRequest.id(model.getId());
				indexRequest.source(model.toMap());
				bulkRequest.add(indexRequest);
			} else {
				DeleteRequest deleteRequest = new DeleteRequest();
				deleteRequest.index(EsIndexEnum.GOODS_LOT.getIndexName());
				deleteRequest.id(id);
				bulkRequest.add(deleteRequest);
			}
		});

		// 同步es
		return esService.bulk(bulkRequest);
	}

    @Override
    public boolean updateInfoByGoodsId(AddStoreGoodsInfoDTO updateParam) {
        // 待上架与销售中状态下的藏品标题/描述/价格支持与藏品栏修改同步更新
        List<String> listState = new ArrayList();
        listState.add(GoodsLotInfoEnum.State.WAIT_SELLING.getCode());
        listState.add(GoodsLotInfoEnum.State.SELLING.getCode());
        LambdaUpdateWrapper<GoodsLotInfo> lambdaUpdateWrapper = new LambdaUpdateWrapper<GoodsLotInfo>();
        lambdaUpdateWrapper.set(GoodsLotInfo::getName, updateParam.getName())
                .set(GoodsLotInfo::getSalePrice, updateParam.getSalePrice())
                .in(GoodsLotInfo::getState, listState)
                .eq(GoodsLotInfo::getGoodsId, updateParam.getId());
        boolean flag = this.update(lambdaUpdateWrapper);
        if(flag) {
            // 更新到es
            List<GoodsLotInfo> goodsLotList = this.list(new LambdaQueryWrapper<GoodsLotInfo>()
                    .select(GoodsLotInfo::getId)
                    .eq(GoodsLotInfo::getGoodsId, updateParam.getId())
                    .eq(GoodsLotInfo::getState, GoodsLotInfoEnum.State.SELLING.getCode()));
			sendGoods2ESMsg(goodsLotList.stream().map(GoodsLotInfo::getId).toList());
			// 批量更新价格和拍品名称到分销商品表中
			List<Long> lotIds = goodsLotList.stream().map(GoodsLotInfo::getId).toList();
			baseRemoteService.updateLotInfo(lotIds, updateParam.getSalePrice(), updateParam.getName());
        }

        return flag;
    }

    /**
     * 批量发布拍品状态变更事件
     *
     * @param updateStateDTO 状态更新参数
     */
    private void publishLotStatusChangeEventsByBatchUpdateState(GoodsLotUpdateStateDTO updateStateDTO) {
		if(StrUtil.isBlank(updateStateDTO.getState())){
			return;
		}
		Integer syncType = LotStatusSyncMqDTO.SyncType.STATE_UPDATE.getCode();
        updateStateDTO.getIds().forEach(id -> {
			LotStatusChangeEvent event = new LotStatusChangeEvent(
					id,
					syncType,
					updateStateDTO.getState(),
					updateStateDTO.getState().equals(GoodsLotInfoEnum.State.SELLING.getCode()) ?
							GoodsLotInfoEnum.DistEnableEnum.DIST.getCode():
							GoodsLotInfoEnum.DistEnableEnum.NO_DIST.getCode(),
					LocalDateTime.now()
			);
			eventPublisher.publishEvent(event);
			log.info("发布拍品状态变更事件成功，拍品ID：{}，同步类型：{}", updateStateDTO.getIds(), LotStatusSyncMqDTO.SyncType.STATE_UPDATE.getDesc());
		});
    }
    /**
     * 公示完成自动上架拍品状态变更事件
     *
     * @param info 状态更新参数
     */
    private void publishLotStatusChangeEventsByPublicityUp(GoodsLotInfo info) {
		LotStatusChangeEvent event = new LotStatusChangeEvent(
				info.getId(),
				info.getGoodsId(),
				LotStatusSyncMqDTO.SyncType.LOT_ADD.getCode(),
				info.getState(),
				GoodsLotInfoEnum.DistEnableEnum.DIST.getCode(),
				info.getName(),
				info.getGoodsNo(),
				info.getMainImage(),
				info.getMerchantId(),
				info.getSalePrice(),
				LocalDateTime.now()
		);
		eventPublisher.publishEvent(event);
		log.info("发布拍品状态变更事件成功，拍品ID：{}，同步类型：{}", info.getId(), LotStatusSyncMqDTO.SyncType.LOT_ADD.getDesc());
    }
}
