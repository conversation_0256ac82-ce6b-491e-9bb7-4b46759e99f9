package com.yts.yyt.goods.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yts.yyt.common.core.dto.IdDTO;
import com.yts.yyt.goods.api.dto.*;
import com.yts.yyt.goods.api.entity.GoodsLotInfo;
import com.yts.yyt.goods.api.vo.*;

import java.util.List;

public interface GoodsLotInfoService extends IService<GoodsLotInfo> {

	/**
	 * 客户端-分页查询拍品信息（适用于搜索）
	 * @param search 搜索条件
	 * @return 拍品信息列表
	 */
	List<GoodsLotInfoAppVO> pageForSearch(GoodsSearchDTO search);

	/**
	 * 客户端-分页查询拍品信息（适用于搜索）v2
	 * @param search 搜索条件
	 * @return 拍品信息列表
	 */
	GoodsLotInfoAppPageVO pageForSearchV2(GoodsSearchV2DTO search);

	/**
	 * 户端-首页拍品查询v1
	 * @param dto 查询参数
	 * @return 拍品信息列表
	 */
	GoodsLotInfoAppPageVO pageForRecommendV1(GoodsRecommendV1DTO dto);

	/**
	 * 根据ID查询拍品详情
	 * @param dto 包含ID和分享码的对象
	 * @return 拍品详情
	 */
	GoodsLotInfoVO detail(GoodsLotDetailDTO dto, Boolean isAdmin);

	/**
	 * 用户查询店铺的商品
	 * @param search 店铺搜索条件
	 * @return 分页的拍品信息
	 */
	Page<GoodsLotInfoAppVO> shopGoodsPage(ShopGoodsSearchDTO search);

	/**
	 * 逻辑删除拍品信息
	 * @param dto 包含ID的对象
	 * @return 删除结果
	 */
	Boolean LogicRemoveByIds(IdDTO dto);

	/**
	 * 更新拍品状态，包含检查逻辑
	 * @param goodsLotUpdateStateDTO 更新状态的DTO
	 * @return 更新结果
	 */
	Boolean updateStateWithCheck(GoodsLotUpdateStateDTO goodsLotUpdateStateDTO);

	/**
	 * 更新拍品状态
	 * @param goodsLotUpdateStateDTO 更新状态的DTO
	 * @return 更新结果
	 */
	Boolean updateState(GoodsLotUpdateStateDTO goodsLotUpdateStateDTO);

	/**
	 * 我的店铺商品
	 * @param search 店铺搜索条件
	 * @return 分页的店铺拍品信息
	 */
	Page<GoodsLotInfoAppVO> myShopGoodsPage(ShopGoodsSearchDTO search);

	/**
	 * 商家重新上架拍品
	 * @param dto 重新上架的DTO
	 * @return 上架结果
	 */
	Boolean resell(MerchantResellDTO dto);

	/**
	 * 根据藏品记录添加拍品记录
	 * @param goodsId 藏品id
	 * @return 添加结果
	 */
	GoodsLotInfo saveByStoreGoodsInfo(Long goodsId);

	/**
	 * 根据ID查询拍品信息
	 * @param id 拍品ID
	 * @return 拍品信息
	 */
	GoodsLotInfo queryById(Long id);

	/**
	 * 管理后台-分页查询拍品信息
	 * @param queryDto dto
	 * @return page
	 */
	Page<GoodsLotInfoAdminVO> getBasePage(GoodsLotInfoQueryDTO queryDto);

	/**
	 * 修改：目前只能修改价格
	 * @param dto dto
	 * @return res
	 */
	Boolean updateInfo(GoodsLotInfoUpdateDTO dto);

	/**
	 * 我的店铺-拍品数量统计
	 * @return 统计结果
	 */
	ShopGoodsLotCountVO shopGoodsCounts(IdDTO dto);

	/**
	 * 同步拍品到es
	 * @param ids
	 */
    boolean syncGoodsLot2ES(List<Long> ids);

	/**
	 * 根据goodsId更新商品信息
	 * @param updateParam
	 * @return
	 */
	boolean updateInfoByGoodsId(AddStoreGoodsInfoDTO updateParam);
}

