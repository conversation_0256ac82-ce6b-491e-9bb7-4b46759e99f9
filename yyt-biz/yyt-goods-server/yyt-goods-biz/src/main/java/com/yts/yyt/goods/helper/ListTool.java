package com.yts.yyt.goods.helper;

import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

public class ListTool {
    /**
     * @Description: 是否全是空
     * <AUTHOR>
     * @date 2025/1/11
     */
    public static boolean allEmpty(List... list){
        for (List list1 : list) {
            if (list1 != null && !list1.isEmpty()) {
                return false;
            }
        }
        return true;
    }

    /**
     * @Description: 是否是空
     * <AUTHOR>
     * @date 2025/1/11
     */
    public static boolean isEmpty(List list){
        if(list == null || list.isEmpty()) return true;
        return false;
    }
    /**
     * @Description: 是否是空
     * <AUTHOR>
     * @date 2025/1/11
     */
    public static boolean isNotEmpty(List list){
        return !isEmpty(list);
    }
    /**
     * @Description: 复制
     * <AUTHOR>
     * @date 2024/9/27
     */
    public static <F,T>  List<T> toList(List<F> list, Class<T> clazz){
        List<T> data =  new ArrayList<>();
        if(list!=null && list.size()>0){
            for(F obj:list){
                try {
                    T newInstance = clazz.newInstance();
                    BeanUtils.copyProperties(obj, newInstance);
                    data.add(newInstance);
                } catch (InstantiationException e) {
                    throw new RuntimeException(e);
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        return data;
    }
    /**
     * @Description: 复制
     * <AUTHOR>
     * @date 2024/9/27
     */
    public static <F,T>  List<T> toList(List<F> list, Class<T> clazz, CopyFunctional<F,T> fun){
        List<T> data =  new ArrayList<>();
        if(list!=null && list.size()>0){
            for(F obj:list){
                try {
                    T newInstance = clazz.newInstance();
                    BeanUtils.copyProperties(obj, newInstance);
                    fun.after(obj,newInstance);
                    data.add(newInstance);
                } catch (InstantiationException e) {
                    throw new RuntimeException(e);
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        return data;
    }
}
