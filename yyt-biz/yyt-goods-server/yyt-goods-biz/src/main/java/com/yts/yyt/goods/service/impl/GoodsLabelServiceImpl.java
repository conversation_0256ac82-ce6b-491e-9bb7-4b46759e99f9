package com.yts.yyt.goods.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yts.yyt.goods.api.entity.GoodsInfoEntity;
import com.yts.yyt.goods.api.entity.GoodsLabelEntity;
import com.yts.yyt.goods.biz.LabelRomBiz;
import com.yts.yyt.goods.mapper.GoodsLabelMapper;
import com.yts.yyt.goods.service.GoodsLabelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description: 商品类型
 * @date 2025/1/6
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
public class GoodsLabelServiceImpl extends ServiceImpl<GoodsLabelMapper, GoodsLabelEntity> implements GoodsLabelService {

    @Autowired
    private LabelRomBiz labelRomBiz;

    /**
     * @Description: 保存
     * <AUTHOR>
     * @date 2025/1/15
     */
    @Override
    public boolean save(GoodsLabelEntity entity) {
        int result = baseMapper.insert(entity);
        labelRomBiz.save(entity);
        return result > 0;
    }

    /**
     * @Description: 保存
     * <AUTHOR>
     * @date 2025/1/15
     */
    @Override
    public boolean updateById(GoodsLabelEntity entity) {
        int result = baseMapper.updateById(entity);
        labelRomBiz.update(entity);
        return result > 0;
    }
    /**
     * @Description: 重新update
     * <AUTHOR>
     * @date 2025/1/15
     */
    @Override
    public boolean removeById(Serializable id){
        GoodsLabelEntity goodsLabel = baseMapper.selectById(id);
        int result =  baseMapper.deleteById(id);
        // 成功后同步到rom
        if(result>=1){
            labelRomBiz.delete(goodsLabel.getId());
        }
        return result >= 1;
    }
}
