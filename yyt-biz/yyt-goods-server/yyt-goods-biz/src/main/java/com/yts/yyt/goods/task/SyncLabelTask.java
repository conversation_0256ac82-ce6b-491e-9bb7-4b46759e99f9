package com.yts.yyt.goods.task;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yts.yyt.goods.api.entity.GoodsLabelEntity;
import com.yts.yyt.goods.biz.LabelRomBiz;
import com.yts.yyt.goods.service.GoodsLabelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description: 同步标签
 * @date 2025/1/11
 */
@Component
@Slf4j
public class SyncLabelTask {
    @Autowired
    private LabelRomBiz labelRomBiz;
    @Autowired
    private GoodsLabelService goodsLabelService;

    @XxlJob("SYNC_ROM_LABEL")
    public ReturnT<String> sync(String param) {
        log.info("同步标签开始");
        List<GoodsLabelEntity> data = goodsLabelService.list();
        if(data.size() == 0) return ReturnT.SUCCESS;
        for(GoodsLabelEntity goodsLabel : data) {
            labelRomBiz.sync(goodsLabel);
        }
        log.info("同步标签结束");
        return ReturnT.SUCCESS;
    }
}
