package com.yts.yyt.goods.controller;

import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.swagger.base.BaseController;
import com.yts.yyt.goods.api.dto.GoodsDashboardQueryDTO;
import com.yts.yyt.goods.api.vo.DashboardDataVO;
import com.yts.yyt.goods.api.vo.JoinStatisticVO;
import com.yts.yyt.goods.api.vo.ProductListingVO;
import com.yts.yyt.goods.api.vo.RegionStatisticVO;
import com.yts.yyt.goods.api.vo.StatisticCardVO;
import com.yts.yyt.goods.service.GoodsDashboardService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 商品数据分析看板
 */
@RestController
@RequestMapping("/data/analysis")
@Tag(description = "dataAnalysis", name = "数据分析")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@RequiredArgsConstructor
@Slf4j
public class GoodsDashboardController extends BaseController {

	private final GoodsDashboardService goodsDashboardService;

	/**
	 * 获取商品数据看板信息
	 *
	 * @param dto 查询参数
	 * @return 数据看板信息
	 */
	@Operation(summary = "数据看板", description = "数据看板")
	@PostMapping("/dashboard")
	public R<DashboardDataVO> getDashboardData(@RequestBody GoodsDashboardQueryDTO dto) {
		return R.ok(goodsDashboardService.getDashboardData(dto));
	}
	
	/**
	 * 获取数据概览
	 *
	 * @param dto 查询参数
	 * @return 数据概览信息
	 */
	@Operation(summary = "数据概览", description = "获取销售额、销售数量、鉴定成功数和公示中数")
	@PostMapping("/statistic/card")
	public R<StatisticCardVO> getStatisticCard(@RequestBody GoodsDashboardQueryDTO dto) {
		return R.ok(goodsDashboardService.getStatisticCard(dto));
	}
	
	/**
	 * 获取入驻统计数据
	 *
	 * @param dto 查询参数
	 * @return 入驻统计数据
	 */
	@Operation(summary = "入驻统计", description = "获取合伙人和商家入驻统计数据")
	@PostMapping("/statistic/join")
	public R<JoinStatisticVO> getJoinStatistic(@RequestBody GoodsDashboardQueryDTO dto) {
		return R.ok(goodsDashboardService.getJoinStatistic(dto));
	}
	
	/**
	 * 获取上架商品统计
	 *
	 * @param dto 查询参数
	 * @return 上架商品统计数据
	 */
	@Operation(summary = "上架商品统计", description = "获取上架商品统计数据")
	@PostMapping("/statistic/product")
	public R<ProductListingVO> getProductListing(@RequestBody GoodsDashboardQueryDTO dto) {
		return R.ok(goodsDashboardService.getProductListing(dto));
	}
	
	/**
	 * 获取地区统计
	 *
	 * @param dto 查询参数
	 * @return 地区统计数据
	 */
	@Operation(summary = "地区统计", description = "获取各地区统计数据")
	@PostMapping("/statistic/region")
	public R<List<RegionStatisticVO>> getRegionStatistic(@RequestBody GoodsDashboardQueryDTO dto) {
		return R.ok(goodsDashboardService.getRegionStatistic(dto));
	}
}