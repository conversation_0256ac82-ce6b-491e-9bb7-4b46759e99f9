package com.yts.yyt.goods.service;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yts.yyt.common.core.dto.IdDTO;
import com.yts.yyt.goods.api.dto.*;
import com.yts.yyt.goods.api.entity.StoreGoodsInfo;
import com.yts.yyt.goods.api.vo.*;
import com.yts.yyt.merchant.api.dto.BatchAddStoreGoodsDTO;
import lombok.NonNull;

import java.util.List;
import java.util.Map;

public interface StoreGoodsInfoService extends IService<StoreGoodsInfo> {

    /**
     * 新增商品信息
     * @param addParam 新增商品参数
     * @return 是否新增成功
     */
    boolean add(AddStoreGoodsInfoDTO addParam);

    /**
     * 根据ID删除商品信息
     * @param id 商品ID
     * @return 是否删除成功
     */
    boolean removeById(Long id);

    /**
     * 更新商品信息
     * @param updateParam 更新商品参数
     * @return 是否更新成功
     */
    boolean updateInfo(AddStoreGoodsInfoDTO updateParam);

	/**
	 * 管理后台-取消公示
	 * @param dto id
	 * @return res
	 */
	Boolean cancelPublicity(IdDTO dto);

	/**
     * 取消商品公示
     * @param dto 取消公示参数
     * @return 是否取消成功
     */
    Boolean cancelPublicity(GoodsCancelPublicityDTO dto);

    /**
     * 分页查询商品信息
     * @param pageParam 分页参数
     * @param search 查询条件
     * @return 商品信息分页结果
     */
    Page<StoreGoodsInfoVO> getBasePage(Page pageParam, SearchStoreGoodsInfoDTO search);

    /**
     * 获取角色数据
     * @return 是否获取成功
     */
    Boolean getSelectRoleData();

    /**
     * 根据ID查询商品详情
     * @param id 商品ID
     * @return 商品详情
     */
    StoreGoodsInfoVO getVOById(Long id);

//    /**
//     * 根据商品编号查询商品是否存在
//     * @param goodsNo 商品编号
//     * @param id 商品ID（非必填，用于排除自身记录）
//     * @return true-存在 false-不存在
//     */
//    Boolean existByGoodsNo(String goodsNo, Long id);

    /**
     * 提交鉴定
     * @param batchUpdateState 鉴定参数
     * @return 鉴定结果
     */
    Boolean submitAppraisal(StoreUpdateStateDTO batchUpdateState);

    /**
     * 域鉴鉴定回调
     * @param requestBody 请求参数
     * @return 回调结果
     */
    JSONObject yujianCallback(Map<String, Object> requestBody);

    /**
     * 域鉴专家回复鉴定回调
     * @param requestBody 请求参数
     * @return 回调结果
     */
    JSONObject yujianReplyCallback(Map<String, Object> requestBody);

    /**
     * 通过商品编号获取商品详细信息
     * @param goodNo 商品编号
     * @return 商品详细信息
     */
    StoreGoodsInfoFeignVO getInfo(String goodNo);

    /**
     * 获取符合条件的商品ID集合
     * @param dto 查询参数
     * @return 商品ID集合
     */
    List<CommissionLadderCountVO> storeGoodsInfoForIncentivePartner(CommissionQueryDTO dto);

    /**
     * 根据时间范围统计鉴定成功的商品数量
     * @param dto 查询参数（包含时间范围）
     * @return 商品数量
     */
    long countAppraisalSuccessGoodsByTimeRange(StoreGoodsInfoIncentiveCountDTO dto);

//	void batchAdd(List<StoreGoodsInfo> storeGoodsList);


    /**
     * 查询公示到期的商品列表
     * @return 公示到期的商品列表
     */
    List<StoreGoodsInfo> getPublicityEndList();


    /**
     * 将单个商品从公示状态更新为售卖状态
     * @param storeGoodsInfo 商品信息
     */
    void publicityToSelling(StoreGoodsInfo storeGoodsInfo);

    /**
     * 批量公示商品
     * @param param 公示参数
     */
    void batchPublicity(GoodsPublicityDTO param);

    /**
     * 计算商品公示时间并延长
     * @param id 商品ID
     * @param putOffDays 延长天数
     */
    void calculatePublicityTimeWithPutOff(@NonNull Long id, @NonNull Integer putOffDays);

    /**
     * 批量新增商品
     * @param batchAddParam 批量新增参数
     * @return 新增结果
     */
    List<String> batchAddStoreGoods(BatchAddStoreGoodsDTO batchAddParam);

    /**
     * 分页查询藏品信息
     * @param searchDTO 查询条件
     * @return 分页结果
     */
    List<StoreGoodsInfoAppVO> pageForSearch(StoreGoodsSearchDTO searchDTO);

    /**
     * 统计藏品和拍品数量
     * @param dto 统计查询参数
     * @return 统计结果
     */
    GoodsStatisticVO storeGoodsCount(StoreGoodsCountDTO dto);

    /**
     * 公示商品搜索
     * @param search
     * @return
     */
    StoreGoodsInfoAppPageVO pageForSearchV2(GoodsSearchV2DTO search);

    /**
     * 公示商品列表
     * @param dto
     * @return
     */
    StoreGoodsInfoAppPageVO pageForRecommendV1(GoodsRecommendV1DTO dto);

    /**
     * 同步藏品到es
     * @param ids
     * @return
     */
    boolean syncStoreGoods2ES(List<Long> ids);

    Boolean audit(GoodsAuditDTO dto);
}

