package com.yts.yyt.goods.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.yts.yyt.common.core.exception.GlobalBizException;
import com.yts.yyt.common.core.util.IDS;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.data.resolver.DictResolver;
import com.yts.yyt.common.security.util.SecurityUtils;
import com.yts.yyt.common.tencent.service.TMSService;
import com.yts.yyt.common.tencent.vo.TextModerationVO;
import com.yts.yyt.goods.api.constants.GoodsConstant;
import com.yts.yyt.goods.api.constants.PublicLogConstant;
import com.yts.yyt.goods.api.dto.*;
import com.yts.yyt.goods.api.entity.*;
import com.yts.yyt.goods.api.enums.GoodsInfoEnum;
import com.yts.yyt.goods.api.enums.GoodsTypeEnum;
import com.yts.yyt.goods.api.enums.PublicLogTypeEnum;
import com.yts.yyt.goods.api.exception.GoodsBizErrorEnum;
import com.yts.yyt.goods.api.exception.GoodsException;
import com.yts.yyt.goods.api.vo.*;
import com.yts.yyt.goods.biz.GoodsRomBiz;
import com.yts.yyt.goods.helper.ListTool;
import com.yts.yyt.goods.helper.ROMHelper;
import com.yts.yyt.goods.mapper.*;
import com.yts.yyt.goods.service.GoodsFeedbackService;
import com.yts.yyt.goods.service.GoodsInfoService;
import com.yts.yyt.goods.service.GoodsTraceabilityPreService;
import com.yts.yyt.goods.service.PublicLogService;
import com.yts.yyt.user.api.entity.UserInterestEntity;
import com.yts.yyt.user.api.feign.RemoteBizUserInfoService;
import com.yts.yyt.user.api.vo.UserInfoVO;
import jakarta.annotation.Resource;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Description: 商品信息
 * <AUTHOR>
 * @date 2025/1/6
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class,isolation = Isolation.READ_COMMITTED)
public class GoodsInfoServiceImpl extends ServiceImpl<GoodsInfoMapper, GoodsInfoEntity> implements GoodsInfoService {
    @Autowired
    private GoodsInfoLabelMapper goodsInfoLabelMapper;
    @Autowired
    private GoodsLabelMapper goodsLabelMapper;
    @Autowired
    private GoodsPropertyMapper goodsPropertyMapper;
    @Autowired
    private GoodsCategoryMapper goodsCategoryMapper;
    @Autowired
    private GoodsTypeMapper goodsTypeMapper;
    @Autowired
    private GoodsConditionMapper goodsConditionMapper;
    @Autowired
    private GoodsRomBiz goodsRomBiz;
    @Autowired
    private GoodsUserOrderMapper goodsUserOrderMapper;
    @Autowired
    private GoodsAssociateRecordMapper goodsAssociateRecordMapper;
    @Autowired
    private RemoteBizUserInfoService remoteBizUserInfoService;

    @Autowired
    private GoodsFeedbackService goodsFeedbackService;
	@Resource
	private PublicLogService publicLogService;
	@Autowired
	private GoodsTraceabilityPreService goodsTraceabilityPreService;
	@Autowired
	private GoodsInfoMapper goodsInfoMapper;

	@Autowired
	private TMSService tmsService;

	/**
     * @Description: 重新update
     * <AUTHOR>
     * @date 2025/1/15
     */
    @Override
    public boolean updateById(GoodsInfoEntity entity){

        int result =  baseMapper.updateById(entity);
        // 成功后同步到rom
        if(result>=1){
            // 存储到rom
            GoodsInfoVO vo = new GoodsInfoVO();
            BeanUtils.copyProperties(entity,vo);
            fillDetailInfo(vo);
            goodsRomBiz.update(vo);
        }
        return result >= 1;
    }

    /**
     * @Description: 重新update
     * <AUTHOR>
     * @date 2025/1/15
     */
    @Override
    public boolean removeById(Serializable id){
        GoodsInfoEntity goodsInfo = baseMapper.selectById(id);
        int result =  baseMapper.deleteById(id);
        // 成功后同步到rom
        if(result>=1){
            goodsRomBiz.delete(goodsInfo.getId());
        }
        return result >= 1;
    }

    /**
     * @Description: 获取商品的标签
     * <AUTHOR>
     * @date 2025/1/7
     */
    @Override
    public List<GoodsInfoLabelVO> getLabelsById(Long id) {
        List<GoodsInfoLabelVO> result = new ArrayList<>();
        List<GoodsInfoLabelEntity> list = goodsInfoLabelMapper.selectList(new LambdaQueryWrapper<GoodsInfoLabelEntity>()
                .eq(GoodsInfoLabelEntity::getGoodsInfoId,id)
        );
        for(GoodsInfoLabelEntity label:list){
            GoodsInfoLabelVO vo = new GoodsInfoLabelVO();
            BeanUtils.copyProperties(label,vo);
            GoodsLabelEntity goodsLabel = goodsLabelMapper.selectById(label.getGoodsLabelId());
            if(goodsLabel!=null)vo.setGoodsLabelName(goodsLabel.getName());
            result.add(vo);
        }
        return result;
    }

    /**
     * @Description: 填充详细数据
     * <AUTHOR>
     * @date 2025/1/7
     */
    @Override
    public void fillDetailInfo(GoodsInfoVO vo) {
		//TODO 建议优化为根据ids一次性获取所有的属性
        // 品相
        if(vo.getGoodsConditionId()!=null){
            GoodsConditionEntity entity = goodsConditionMapper.selectById(vo.getGoodsConditionId());
            if(entity!=null)vo.setGoodsConditionName(entity.getName());
        }
        // 年代
        if(vo.getGoodsEraId()!=null){
            GoodsPropertyEntity entity = goodsPropertyMapper.selectById(vo.getGoodsEraId());
            if(entity!=null)vo.setGoodsEraName(entity.getName());
        }
        // 大类型
        if(vo.getGoodsTypeId()!=null){
            GoodsTypeEntity entity = goodsTypeMapper.selectById(vo.getGoodsTypeId());
            if(entity!=null)vo.setGoodsTypeName(entity.getName());
        }
        // 类型
        if(vo.getGoodsCategoryId()!=null){
            GoodsCategoryEntity entity = goodsCategoryMapper.selectById(vo.getGoodsCategoryId());
            if(entity!=null)vo.setGoodsCategoryName(entity.getName());
        }
        // 颜色
        if(vo.getGoodsColorId()!=null){
            GoodsPropertyEntity entity = goodsPropertyMapper.selectById(vo.getGoodsColorId());
            if(entity!=null)vo.setGoodsColorName(entity.getName());
        }
        // 纹路
        if(vo.getGoodsPatternId()!=null){
            GoodsPropertyEntity entity = goodsPropertyMapper.selectById(vo.getGoodsPatternId());
            if(entity!=null)vo.setGoodsPatternName(entity.getName());
        }
        // 器型
        if(vo.getGoodsShapeId()!=null){
            GoodsPropertyEntity entity = goodsPropertyMapper.selectById(vo.getGoodsShapeId());
            if(entity!=null)vo.setGoodsShapeName(entity.getName());
        }
        // 收藏人信息
        GoodsUserOrderEntity goodsUserOrder = goodsUserOrderMapper.selectOne(new LambdaQueryWrapper<GoodsUserOrderEntity>()
                .eq(GoodsUserOrderEntity::getGoodsId,vo.getId())
        );
        if(goodsUserOrder!=null){
            vo.setOwnerId(goodsUserOrder.getUserId());
            vo.setOwnerName(goodsUserOrder.getUserName());
            vo.setOwnerHeadImg(goodsUserOrder.getUserHeadImg());
            vo.setOrderId(goodsUserOrder.getOrderId());
        }
        // 标签
        List<GoodsInfoLabelVO> labelList = new ArrayList<>();
        List<GoodsInfoLabelEntity> list = goodsInfoLabelMapper.selectList(new LambdaQueryWrapper<GoodsInfoLabelEntity>()
                .eq(GoodsInfoLabelEntity::getGoodsInfoId,vo.getId())
        );
        for(GoodsInfoLabelEntity label:list){
            GoodsInfoLabelVO labelVO = new GoodsInfoLabelVO();
            BeanUtils.copyProperties(label,labelVO);
            GoodsLabelEntity goodsLabel = goodsLabelMapper.selectById(label.getGoodsLabelId());
            if(goodsLabel!=null)labelVO.setGoodsLabelName(goodsLabel.getName());
            labelList.add(labelVO);
        }
        vo.setGoodsInfoLabels(labelList);
		//是否已经公示
		vo.setPublicityFlag(GoodsInfoEnum.State.handlePublicityFlag(vo.getState()));
    }

    @Override
    public GoodsInfoVO getVOById(Long id) {
        GoodsInfoEntity entity = getById(id);
        if(entity==null)return null;
        GoodsInfoVO vo = new GoodsInfoVO();
        BeanUtils.copyProperties(entity,vo);
        fillDetailInfo(vo);
        return vo;
    }

	/**
     * @Description: 新增商品信息（带敏感词检测）
     * <AUTHOR>
     * @date 2025/1/6
     */
    @Override
    public void addWithCheck(AddGoodsInfoDTO addParam) {
        if (StrUtil.isBlank(addParam.getGoodsNo())) {
            throw GoodsException.build(GoodsBizErrorEnum.GOODS_NO_NULL_ERROR);
        }
        //检查商品编码是否唯一
        List<GoodsInfoEntity> checkList =  this.baseMapper.selectList(new LambdaQueryWrapper<GoodsInfoEntity>()
                .eq(GoodsInfoEntity::getGoodsNo,addParam.getGoodsNo())
        );
        if(ListTool.isNotEmpty(checkList)){
            throw GoodsException.build(GoodsBizErrorEnum.GOODS_NO_REPEAT_ERROR);
        }

        // 敏感词检测
        try {
            TextModerationVO result = tmsService.textModeration(addParam.getName(), addParam.getCurrentStateDescription());
            if (!result.isPassed()) {
                throw new GoodsException("文本包含敏感词：" + result.getSensitiveWords());
            }
        } catch (TencentCloudSDKException e) {
            log.error("敏感词检测失败", e);
            throw new GoodsException("敏感词检测服务异常");
        }

        //注入敏感词
        add(addParam);
    }

	/**
     * @Description: 新增
     * <AUTHOR>
     * @date 2025/1/7
     */
    @Override
    public boolean add(AddGoodsInfoDTO addParam) {
        GoodsInfoEntity add = new GoodsInfoEntity();
        BeanUtils.copyProperties(addParam,add);
        GoodsInfoCheck(add);
		LocalDateTime now = LocalDateTime.now();
		//公示时间处理
		if (GoodsInfoEnum.State.PUBLICITY.getCode().equals(add.getState())) {
			calculatePublicityTime(add, now, null);
		}

        add.setId(IDS.uniqueID());
        add.setCreateTime(now);
        add.setLikeNumber(0);
        add.setUpdateTime(now);
        add.setType(addParam.getType());
        add.setCurrentPrice(addParam.getSalePrice());
        if(addParam.getGoodsInfoLabels()!=null){
            for(GoodsInfoLabelVO vo:addParam.getGoodsInfoLabels()){
                GoodsInfoLabelEntity goodsInfoLabel = new GoodsInfoLabelEntity();
                goodsInfoLabel.setId(IDS.uniqueID());
                goodsInfoLabel.setGoodsInfoId(add.getId());
                goodsInfoLabel.setGoodsLabelId(vo.getGoodsLabelId());
                goodsInfoLabel.setGoodsLabelName(vo.getGoodsLabelName());
                goodsInfoLabelMapper.insert(goodsInfoLabel);
            }
        }
        // 获取物主
        GoodsUserOrderEntity orderUser = new GoodsUserOrderEntity();
        orderUser.setId(IDS.uniqueID());
        orderUser.setGoodsId(add.getId());
        orderUser.setUserId(GoodsInfoEnum.PLATFORM_ID);
        UserInfoVO user = remoteBizUserInfoService.getDetailById(GoodsInfoEnum.PLATFORM_ID).getData();
		if (Objects.nonNull(user)) {
			orderUser.setUserName(user.getNickname());
			orderUser.setUserHeadImg(user.getHeadImg());
		}
        orderUser.setCreateTime(now);
        orderUser.setUpdateTime(now);
        goodsUserOrderMapper.insert(orderUser);
        boolean result = save(add);
		CompletableFuture.runAsync(()->{
			if(result){
				// 存储到rom
				GoodsInfoVO vo = new GoodsInfoVO();
				BeanUtils.copyProperties(add,vo);
				fillDetailInfo(vo);
				goodsRomBiz.save(vo);
			}
			addParam.setId(add.getId());
			// 保存藏品公示记录, 状态转换: 无 -> 公示中
			savePublicLog(add);
		});
		return result;
    }

	private void savePublicLog(GoodsInfoEntity goodsInfo) {
		PublicLogSaveDTO publicLogSaveDTO = new PublicLogSaveDTO();
		publicLogSaveDTO.setGoodsIds(Collections.singletonList(goodsInfo.getId()))
				.setPublicityTime(ObjectUtil.isNotNull(goodsInfo.getPublicityTime()) ? goodsInfo.getPublicityTime() +"" : null)
				.setPublicityStartTime(ObjectUtil.isNotNull(goodsInfo.getPublicityStartTime()) ? goodsInfo.getPublicityStartTime() : null)
				.setPublicityEndTime(ObjectUtil.isNotNull(goodsInfo.getPublicityEndTime()) ? goodsInfo.getPublicityEndTime() : null)
				.setRemark(PublicLogConstant.SAVE_GOODS_INFO);
		if (GoodsInfoEnum.State.PUBLICITY.getCode().equals(goodsInfo.getState())) {
			publicLogSaveDTO.setPublicLogType(PublicLogTypeEnum.PUBLIC.getCode());
		}else if(GoodsInfoEnum.State.PRE_PUBLICITY.getCode().equals(goodsInfo.getState())){
			publicLogSaveDTO.setPublicLogType(PublicLogTypeEnum.WAIT_PUBLIC.getCode());
		}
		publicLogService.saveOrUpdatePublicLog(publicLogSaveDTO);
	}

	/**
	 * 转换链数据
	 * @param goodsNo
	 * @return
	 */
	private AddChainAndQRCodeDTO getChainData(String goodsNo) {
		GoodsChainInfoVO vo = this.goodsInfoMapper.getGoodsChainInfoByGoodNo(goodsNo);
		AddChainAndQRCodeDTO dto = new AddChainAndQRCodeDTO();
		dto.setName(vo.getGoodsName());
		dto.setGoodsEra(vo.getGoodsYear());
		dto.setGoodsCondition(vo.getGrade());
		dto.setMainImage(vo.getMainImage());
		dto.setPreviewImage(vo.getPreviewImage());
		dto.setSalePrice(vo.getSalePrice());
		dto.setStockQuantity(1);
		dto.setDetailInfo(vo.getDetailInfo());
		dto.setPlaceOfOrigin(vo.getPlaceOfOrigin());
		dto.setOwnership("");
		dto.setGoodsNo(vo.getGoodsNo());
		dto.setType(GoodsTypeEnum.TYPE_SELF.getType());
		String sizeContent = String.format("尺寸:口径 %sCM, 宽:%sCM, 高:%sCM",
										ObjectUtil.isEmpty(vo.getGoodsSizeLength()) ? "" : vo.getGoodsSizeLength(),
										ObjectUtil.isEmpty(vo.getGoodsSizeWidth()) ? "" : vo.getGoodsSizeWidth(),
										ObjectUtil.isEmpty(vo.getGoodsSizeHeight())? "" : vo.getGoodsSizeHeight());
		dto.setGoodsSize(sizeContent);
		return dto;
	}

	/**
     * 校验商品信息
     * @param entity
     */
    public void GoodsInfoCheck(GoodsInfoEntity entity){
        if(StrUtil.isEmpty(entity.getName()) && entity.getName().trim().isEmpty())
            throw  new GlobalBizException("商品名称不允许为空或空字符串!");
		//新增商品，状态为：待公示 或 公式中 TODO 方便测试，暂不做校验
//		if (!GoodsInfoEnum.State.PRE_PUBLICITY.getCode().equals(entity.getState())
//				&& !GoodsInfoEnum.State.PUBLICITY.getCode().equals(entity.getState())) {
//			throw GoodsException.build(GoodsBizErrorEnum.GOODS_ADD_STATE_ERROR);
//		}
        // todo
    }

    @Override
	@Transactional(rollbackFor = Exception.class)
    public boolean update(AddGoodsInfoDTO addParam) {
		if (StrUtil.isNotBlank(addParam.getName()) && addParam.getName().length() > 30) {
			throw new GoodsException("商品名称不得超过30个字符");
		}
		//锁定中、已销售状态不可编辑
		GoodsInfoEntity dbInfo = this.getById(addParam.getId());
		if (GoodsInfoEnum.State.cannotEditState(dbInfo.getState())) {
			throw GoodsException.build(GoodsBizErrorEnum.GOODS_CANNOT_EDIT_ERROR);
		}

		// 敏感词检测
		try {
			TextModerationVO result = tmsService.textModeration(addParam.getName(), addParam.getCurrentStateDescription());
			if (!result.isPassed()) {
				throw new GoodsException("文本包含敏感词：" + result.getSensitiveWords());
			}
		} catch (TencentCloudSDKException e) {
			log.error("敏感词检测失败", e);
			throw new GoodsException("敏感词检测服务异常");
		}

		GoodsInfoEntity update = new GoodsInfoEntity();
        BeanUtils.copyProperties(addParam,update);
        GoodsInfoCheck(update);
        update.setUpdateTime(LocalDateTime.now());
        // 因为支持远程feign修改所以暂时不进行同步销售价
        // DC 2025/3/2 14:11 取消注释：注释后会导致下单价格不匹配
        update.setCurrentPrice(addParam.getSalePrice());
        if(addParam.getGoodsInfoLabels()!=null){
            // 删除历史
            goodsInfoLabelMapper.delete(new LambdaQueryWrapper<GoodsInfoLabelEntity>().eq(GoodsInfoLabelEntity::getGoodsInfoId,addParam.getId()));
            for(GoodsInfoLabelVO vo:addParam.getGoodsInfoLabels()){
                GoodsInfoLabelEntity goodsInfoLabel = new GoodsInfoLabelEntity();
                goodsInfoLabel.setId(IDS.uniqueID());
                goodsInfoLabel.setGoodsInfoId(update.getId());
                goodsInfoLabel.setGoodsLabelId(vo.getGoodsLabelId());
                goodsInfoLabel.setGoodsLabelName(vo.getGoodsLabelName());
                goodsInfoLabelMapper.insert(goodsInfoLabel);
            }
        }
        boolean result = updateById(update);
        return result;
    }

    /**
     * @Description: 分页查询
     * <AUTHOR>
     * @date 2025/1/7
     */
    @Override
    public Page<GoodsInfoVO> getBasePage(Page page, SearchGoodsInfoDTO search) {
        return baseMapper.getBasePage(page,search);
    }

    @Override
    public Page<GoodsInfoVO> pageForRecommend(Page page, SearchRecommendGoodsInfoDTO search) {
        return baseMapper.pageForRecommend(page,search);
    }

    /**
     * @Description: 批量更新商品状态
     * <AUTHOR>
     * @date 2025/1/8
     */
    @Override
    public Boolean batchUpdateState(BatchUpdateStateDTO batchUpdateState) {
        for(Long id:batchUpdateState.getIds()){
            GoodsInfoEntity update = getById(id);
            update.setState(batchUpdateState.getState());
            update.setGoodsBatchNo(batchUpdateState.getBatchNo());
            update.setUpdateTime(LocalDateTime.now());
            updateById(update);
        }
        return true;
    }

	@Override
	public List<AppGoodsInfoVO> pageForSearch(Page pageParam, SearchGoodsDTO search) {
		log.info("【pageForSearch】 params：{}", JSONObject.toJSONString(search));

		search.setSearchKey(ROMHelper.removeInvalidCharacter(search.getSearchKey()));
//		LambdaQueryChainWrapper<GoodsInfoEntity> wrapper = new LambdaQueryChainWrapper<>(this.baseMapper);
		LambdaQueryWrapper<GoodsInfoEntity> wrapper = new LambdaQueryWrapper<>();

		wrapper.eq(GoodsInfoEntity::getState, StrUtil.isBlank(search.getState()) ? GoodsInfoEnum.State.SELLING.getCode() : search.getState())
				.eq(StringUtils.isNotEmpty(search.getType()), GoodsInfoEntity::getType, search.getType())
				.eq(StringUtils.isNotEmpty(search.getShowTag()), GoodsInfoEntity::getShowTag, search.getShowTag())
				.in(ListTool.isNotEmpty(search.getShowTags()), GoodsInfoEntity::getShowTag, search.getShowTags())
				.in(ListTool.isNotEmpty(search.getGoodsCategoryIds()), GoodsInfoEntity::getGoodsCategoryId, search.getGoodsCategoryIds())
				.in(ListTool.isNotEmpty(search.getGoodsShapeIds()), GoodsInfoEntity::getGoodsShapeId, search.getGoodsShapeIds())
				.in(ListTool.isNotEmpty(search.getGoodsPatternIds()), GoodsInfoEntity::getGoodsPatternId, search.getGoodsPatternIds())
				.in(ListTool.isNotEmpty(search.getGoodsEraIds()), GoodsInfoEntity::getGoodsEraId, search.getGoodsEraIds())
				.in(ListTool.isNotEmpty(search.getGoodsColorIds()), GoodsInfoEntity::getGoodsColorId, search.getGoodsColorIds())
				.ge(ObjUtil.isNotNull(search.getCurrentPriceMin()), GoodsInfoEntity::getSalePrice, search.getCurrentPriceMin())  //前端展示的：销售价格，和前端保持一致
				.le(ObjUtil.isNotNull(search.getCurrentPriceMax()), GoodsInfoEntity::getSalePrice, search.getCurrentPriceMax())
				;
		//联想搜索条件
		associateWrapperHandle(search, wrapper);

		//排序
		sortWrapperHandle(pageParam, search, wrapper);
		List<GoodsInfoEntity> list = this.baseMapper.pageForSearch(pageParam, wrapper);
		if (ListTool.isEmpty(list)){
			return Collections.emptyList();
		}
		log.info("【pageForSearch】 搜索结果size：{}", list.size());
		List<AppGoodsInfoVO> resList = list.stream().map(item -> {
			AppGoodsInfoVO appGoodsInfoVO = new AppGoodsInfoVO();
			BeanUtil.copyProperties(item, appGoodsInfoVO);
			//是否已经公示
			appGoodsInfoVO.setPublicityFlag(GoodsInfoEnum.State.handlePublicityFlag(appGoodsInfoVO.getState()));
			return appGoodsInfoVO;
		}).toList();
		return resList;
	}

	private void sortWrapperHandle(Page pageParam, SearchGoodsDTO search, LambdaQueryWrapper<GoodsInfoEntity> wrapper) {
		if(search.getCurrentPriceSort()!=null){
			if(search.getCurrentPriceSort()>0){
				wrapper.orderByAsc(GoodsInfoEntity::getCurrentPrice);
			}else{
				wrapper.orderByDesc(GoodsInfoEntity::getCurrentPrice);
			}
			wrapper.orderByDesc(GoodsInfoEntity::getSort); //数字越大，越考前
			wrapper.orderByDesc(GoodsInfoEntity::getCreateTime);
		}else if (ObjUtil.isNotNull(SecurityUtils.getUser()) && pageParam.getCurrent()<=2) {
			String baseSort = " FIND_IN_SET(%s, '%s') desc ,";
			StrBuilder lastSort = new StrBuilder(" ORDER BY ");
			Long userId = SecurityUtils.getUser().getId();
			R<List<UserInterestEntity>> interestResult = remoteBizUserInfoService.getUserInterestListByUserId(userId); //1663354821187805184L
			if (interestResult.isOk() && CollUtil.isNotEmpty(interestResult.getData())) {
				List<UserInterestEntity> interestList = interestResult.getData();
				Collections.shuffle(interestList);  //打乱顺序
				Map<String, List<UserInterestEntity>> listMap = interestList.stream().collect(Collectors.groupingBy(UserInterestEntity::getLabelType));
				for (Map.Entry<String, List<UserInterestEntity>> entry : listMap.entrySet()) {
					List<Long> labelIds = entry.getValue().stream().map(UserInterestEntity::getLabelId).toList();
					switch (entry.getKey()){
						case "1": //窖口
							lastSort.append(String.format(baseSort,"goods_category_id", StrUtil.join(",", labelIds)));
							break;
						case "2": //年代
							lastSort.append(String.format(baseSort,"goods_era_id", StrUtil.join(",", labelIds)));
							break;
						case "3": //颜色
							lastSort.append(String.format(baseSort,"goods_color_id", StrUtil.join(",", labelIds)));
							break;
						case "4": //花纹
							lastSort.append(String.format(baseSort,"goods_pattern_id", StrUtil.join(",", labelIds)));
							break;
						case "5": //器型
							lastSort.append(String.format(baseSort,"goods_shape_id", StrUtil.join(",", labelIds)));
							break;
					}
				}
				log.info("lastSort : {}", lastSort);
				String lastSortStr = lastSort.toString();
//				if (lastSortStr.contains(",")) {
//					lastSortStr = lastSortStr.substring(0, lastSortStr.length()-1);
//				}
				lastSortStr = lastSortStr + " sort DESC,create_time DESC";
				log.info("lastSort : {}", lastSortStr);
				wrapper.last(lastSortStr);
			}
		}else{
			wrapper.orderByDesc(GoodsInfoEntity::getSort); //数字越大，越考前
			wrapper.orderByDesc(GoodsInfoEntity::getCreateTime);
		}
	}

	private void associateWrapperHandle(SearchGoodsDTO search, LambdaQueryWrapper<GoodsInfoEntity> wrapper) {
		if (StrUtil.isBlank(search.getAssociateType()) || ObjUtil.isNull(search.getAssociateId())) {
			wrapper.like(StringUtils.isNotEmpty(search.getSearchKey()),GoodsInfoEntity::getName, search.getSearchKey());
			return;
		}
		GoodsInfoEnum.AssociateType ass = GoodsInfoEnum.AssociateType.getByCode(search.getAssociateType());
		if (ObjUtil.isNull(ass)) {
			wrapper.like(StringUtils.isNotEmpty(search.getSearchKey()),GoodsInfoEntity::getName, search.getSearchKey());
			return;
		}
		// 根据（商品名称 || 联想词） 查询
		switch (ass) {
			case GOODS_TYPE:
				wrapper.and(w->w.eq(GoodsInfoEntity::getGoodsTypeId, search.getAssociateId())
						.or().like(StringUtils.isNotEmpty(search.getSearchKey()),GoodsInfoEntity::getName, search.getSearchKey()));
				break;
			case GOODS_CATEGORY:
//				wrapper.eq(GoodsInfoEntity::getGoodsCategoryId, search.getAssociateId());
				wrapper.and(w->w.eq(GoodsInfoEntity::getGoodsCategoryId, search.getAssociateId())
						.or().like(StringUtils.isNotEmpty(search.getSearchKey()),GoodsInfoEntity::getName, search.getSearchKey()));
				break;
			case GOODS_LABEL:
				//查询标签信息表
				List<Long> goodsIds = new LambdaQueryChainWrapper<>(goodsInfoLabelMapper).eq(GoodsInfoLabelEntity::getGoodsLabelId, search.getAssociateId())
						.list().stream().map(GoodsInfoLabelEntity::getGoodsInfoId).toList();
//				wrapper.in(CollUtil.isNotEmpty(goodsIds), GoodsInfoEntity::getId, goodsIds);
				wrapper.and(w->w.in(CollUtil.isNotEmpty(goodsIds), GoodsInfoEntity::getId, goodsIds)
						.or().like(StringUtils.isNotEmpty(search.getSearchKey()),GoodsInfoEntity::getName, search.getSearchKey()));
				break;
			case GOODS_COLOR:
//				wrapper.eq(GoodsInfoEntity::getGoodsColorId, search.getAssociateId());
				wrapper.and(w->w.eq(GoodsInfoEntity::getGoodsColorId, search.getAssociateId())
						.or().like(StringUtils.isNotEmpty(search.getSearchKey()),GoodsInfoEntity::getName, search.getSearchKey()));
				break;
			case GOODS_ERA:
//				wrapper.eq(GoodsInfoEntity::getGoodsEraId, search.getAssociateId());
				wrapper.and(w->w.eq(GoodsInfoEntity::getGoodsEraId, search.getAssociateId())
						.or().like(StringUtils.isNotEmpty(search.getSearchKey()),GoodsInfoEntity::getName, search.getSearchKey()));
				break;
			case GOODS_PATTERN:
//				wrapper.eq(GoodsInfoEntity::getGoodsPatternId, search.getAssociateId());
				wrapper.and(w->w.eq(GoodsInfoEntity::getGoodsPatternId, search.getAssociateId())
						.or().like(StringUtils.isNotEmpty(search.getSearchKey()),GoodsInfoEntity::getName, search.getSearchKey()));
				break;
			case GOODS_SHAPE:
//				wrapper.eq(GoodsInfoEntity::getGoodsShapeId, search.getAssociateId());
				wrapper.and(w->w.eq(GoodsInfoEntity::getGoodsShapeId, search.getAssociateId())
						.or().like(StringUtils.isNotEmpty(search.getSearchKey()),GoodsInfoEntity::getName, search.getSearchKey()));
				break;
			default:
				break;
		}
		//异步记录联想词搜索记录
		CompletableFuture.runAsync(()->{
			log.info("异步添加搜索记录，params：{}-{}",search.getAssociateId(),search.getAssociateType());
			GoodsAssociateRecordEntity associateRecord = new LambdaQueryChainWrapper<>(goodsAssociateRecordMapper)
					.eq(GoodsAssociateRecordEntity::getAssociateType, search.getAssociateType())
					.eq(GoodsAssociateRecordEntity::getAssociateId, search.getAssociateId())
					.one();
			if (ObjUtil.isNull(associateRecord)) {
				//save
				GoodsAssociateRecordEntity saveEntity = new GoodsAssociateRecordEntity();
				saveEntity.setAssociateId(search.getAssociateId());
				saveEntity.setAssociateType(search.getAssociateType());
				saveEntity.setAssociateName(search.getSearchKey());
				saveEntity.setSearchTimes(1L);
				saveEntity.setCreateTime(LocalDateTime.now());
				goodsAssociateRecordMapper.insert(saveEntity);
			}else {
				//记录数加一
				goodsAssociateRecordMapper.updateTimesById(associateRecord.getId());
			}
		});
	}

	@Override
	public List<GoodsInfoEntity> getListByCategoryId(Long id) {
		if (Objects.isNull(id)) {
			return Collections.emptyList();
		}
		return new LambdaQueryChainWrapper<>(this.baseMapper)
				.eq(GoodsInfoEntity::getGoodsCategoryId, id)
				.list();
	}

	@Override
	public List<GoodsInfoEntity> getListByTypeId(Long id) {
		if (Objects.isNull(id)) {
			return Collections.emptyList();
		}
		return new LambdaQueryChainWrapper<>(this.baseMapper)
				.eq(GoodsInfoEntity::getGoodsTypeId, id)
				.list();
	}

	/**
	 * 商品搜索联想
	 * @param keyWord 搜索关键词
	 * @return
	 */
	@Override
	public List<GoodsSearchAssociateVO> goodsSearchAssociate(String keyWord) {
		if (StrUtil.isBlank(keyWord)) {
			throw GoodsException.build(GoodsBizErrorEnum.PARAMS_ERROR);
		}
		List<GoodsSearchAssociateVO> resList = Lists.newArrayList();

		//库表查询
		List<GoodsSearchAssociateVO> dbList = this.baseMapper.querySearchAssociate(keyWord);
		return dbList;

//		//商品分类
//		LambdaQueryChainWrapper<GoodsTypeEntity> goodsTypeNameQw = new LambdaQueryChainWrapper<>(goodsTypeMapper);
//		List<GoodsSearchAssociateVO> goodsNameAssVos = goodsTypeNameQw.like(GoodsTypeEntity::getName, keyWord).list()
//				.stream().map(item -> {
//					GoodsSearchAssociateVO associateVO = new GoodsSearchAssociateVO();
//					associateVO.setAssociateId(item.getId());
//					associateVO.setAssociateName(item.getName());
//					associateVO.setAssociateType(GoodsInfoEnum.AssociateType.CATEGORY.getCode());
//					return associateVO;
//				}).toList();
//		if (CollUtil.isNotEmpty(goodsNameAssVos)) {
//			resList.addAll(goodsNameAssVos);
//		}
//		//最多展示10条
//		if (resList.size() >= 10) {
//			return CollUtil.sub(resList,0,10);
//		}
//		//商品标签
//		LambdaQueryChainWrapper<GoodsInfoLabelEntity> labelQw = new LambdaQueryChainWrapper<>(goodsInfoLabelMapper);
//		List<GoodsSearchAssociateVO> labelAssVos = labelQw.like(GoodsInfoLabelEntity::getGoodsLabelName, keyWord).list()
//				.stream().map(item -> {
//					GoodsSearchAssociateVO associateVO = new GoodsSearchAssociateVO();
//					associateVO.setAssociateId(item.getId());
//					associateVO.setAssociateName(item.getGoodsLabelName());
//					associateVO.setAssociateType(GoodsInfoEnum.AssociateType.LABEL.getCode());
//					return associateVO;
//				}).toList();
//		if (CollUtil.isNotEmpty(labelAssVos)) {
//			resList.addAll(labelAssVos);
//		}
//		//最多展示10条
//		if (resList.size() >= 10) {
//			return CollUtil.sub(resList,0,10);
//		}
//		return resList;
	}

    @Override
    public Boolean setFootViewCountInc(Long goodsId) {
        return lambdaUpdate()
                .setIncrBy(GoodsInfoEntity::getFootViewCount, 1)
                .eq(GoodsInfoEntity::getId, goodsId)
                .update();
    }

	/**
	 * 商品公示到期自动上架
	 */
	@Override
	public void publicityToSelling() {
		//1、查询公示到期时间小于当前时间的数据
		List<GoodsInfoEntity> dbList = new LambdaQueryChainWrapper<>(this.baseMapper)
				.select(GoodsInfoEntity::getId, GoodsInfoEntity::getPublicityEndTime)
				.eq(GoodsInfoEntity::getState, GoodsInfoEnum.State.PUBLICITY.getCode())
				.le(GoodsInfoEntity::getPublicityEndTime, LocalDateTime.now())
				.list();
		log.info("【publicityToSelling】公示到期自动上架，记录数：{}",dbList.size());
		//2、修改状态为：销售中
		if (CollUtil.isNotEmpty(dbList)) {
			new LambdaUpdateChainWrapper<>(this.baseMapper).set(GoodsInfoEntity::getState, GoodsInfoEnum.State.SELLING.getCode())
					.in(GoodsInfoEntity::getId, dbList.stream().map(GoodsInfoEntity::getId).toList())
					.update();

			// 保存藏品公示记录, 状态转换: 公示中 -> 已公示
			PublicLogSaveDTO publicLogSaveDTO = new PublicLogSaveDTO();
			publicLogSaveDTO.setGoodsIds(dbList.stream().map(GoodsInfoEntity::getId).toList())
							.setPublicLogType(PublicLogTypeEnum.PUBLIC_COMPLETED.getCode())
							.setRemark(PublicLogConstant.PUBLIC_COMPLETED);
			publicLogService.saveOrUpdatePublicLog(publicLogSaveDTO);

			dbList.forEach(goodsInfo -> {
				goodsTraceabilityPreService.addChainAndQRCode(getChainData(goodsInfo.getGoodsNo()));
			});

		}
	}

	@Override
	public Boolean modifyState(GoodsStateUpdateDTO updateDTO) {
		if (ObjUtil.isNull(updateDTO) || ObjUtil.isNull(updateDTO.getId()) || ObjUtil.isNull(updateDTO.getState())) {
			throw GoodsException.build(GoodsBizErrorEnum.PARAMS_ERROR);
		}
		// 处理藏品记录
		processPublicLog(updateDTO);

		return new LambdaUpdateChainWrapper<>(this.baseMapper).eq(GoodsInfoEntity::getId, updateDTO.getId())
				.set(GoodsInfoEntity::getState, updateDTO.getState())
				.update();
	}

	private void processPublicLog(GoodsStateUpdateDTO updateDTO) {
		if (GoodsInfoEnum.State.PRE_PUBLICITY.getCode().equals(updateDTO.getState())) {
			// 取消公示, 保存藏品公示记录, 状态转换: 公示中 -> 待公示
			PublicLogSaveDTO publicLogSaveDTO = new PublicLogSaveDTO();
			publicLogSaveDTO.setGoodsIds(Collections.singletonList(updateDTO.getId()))
					.setPublicLogType(PublicLogTypeEnum.WAIT_PUBLIC.getCode())
					.setRemark(PublicLogConstant.UPDATE_GOODS_INFO);
			publicLogService.saveOrUpdatePublicLog(publicLogSaveDTO);
		}
	}

	/**
	 * 批量公示
	 * @param param 商品ids
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void batchPublicity(GoodsPublicityDTO param) {
		log.info("【batchPublicity】批量公示，入参：{}",JSONObject.toJSONString(param));
		if (CollUtil.isEmpty(param.getGoodsIds())) {
			throw GoodsException.build(GoodsBizErrorEnum.PARAMS_ERROR);
		}
		//1、查询商品信息
		List<GoodsInfoEntity> dbList = new LambdaQueryChainWrapper<>(this.baseMapper)
//				.in(GoodsInfoEntity::getState, Lists.newArrayList(GoodsInfoEnum.State.PRE_PUBLICITY.getCode(), GoodsInfoEnum.State.IN_WAREHOUSE.getCode()))
				.in(GoodsInfoEntity::getId, param.getGoodsIds())
				.list();
		if (CollUtil.isEmpty(dbList)) {
			throw GoodsException.build(GoodsBizErrorEnum.GOODS_CANNOT_EDIT_TO_PUBLICITY_ERROR);
		}
		//校验
		List<GoodsFeedbackEntity> checkList = goodsFeedbackService.bacthValidByGoodsId(dbList.stream().map(GoodsInfoEntity::getId).toList());
		if (CollUtil.isNotEmpty(checkList)) {
			List<Long> checkIds = checkList.stream().map(GoodsFeedbackEntity::getGoodsId).toList();
			String nameJoin = StrUtil.join(",", new LambdaQueryChainWrapper<>(this.baseMapper)
					.select(GoodsInfoEntity::getName)
					.in(GoodsInfoEntity::getId, checkIds)
					.list().stream().map(GoodsInfoEntity::getName).toList());
			throw new GoodsException(nameJoin+"-存在被举报记录");
		}
		//2、分批，>50 <=50
		List<GoodsInfoEntity> underList = Lists.newArrayList(); // 小于等于50
		List<GoodsInfoEntity> aboveList = Lists.newArrayList(); // 大于50
		for (GoodsInfoEntity goodsInfo : dbList) {
			if (goodsInfo.getSalePrice().compareTo(GoodsConstant.publicity_amount) > 0) {
				aboveList.add(goodsInfo);
			}else {
				underList.add(goodsInfo);
			}
		}
		//3、计算并更新公示信息
		LocalDateTime now = LocalDateTime.now();
		updatePublicityInfo(underList, now);
		updatePublicityInfo(aboveList, now);
	}

	/**
	 * 更新商品公示信息
	 * @param list 商品信息
	 * @param now now
	 */
	private void updatePublicityInfo(List<GoodsInfoEntity> list, LocalDateTime now) {
		if (CollUtil.isNotEmpty(list)) {
			GoodsInfoEntity first = list.get(0);
			log.info("【updatePublicityInfo】公示日期计算：{}",now);
			calculatePublicityTime(first, now, null);
			log.info("【updatePublicityInfo】公示日期计算结束：{}-{}",first.getPublicityTime(),first.getPublicityEndTime());
			//更新数据
			new LambdaUpdateChainWrapper<>(this.baseMapper).set(GoodsInfoEntity::getPublicityTime, first.getPublicityTime())
					.set(GoodsInfoEntity::getPublicityStartTime, first.getPublicityStartTime())
					.set(GoodsInfoEntity::getPublicityEndTime, first.getPublicityEndTime())
					.set(GoodsInfoEntity::getState, GoodsInfoEnum.State.PUBLICITY.getCode())
					.in(GoodsInfoEntity::getId, list.stream().map(GoodsInfoEntity::getId).toList())
					.update();

			// 保存藏品公示记录, 状态转换: 待公示 -> 公示中
			PublicLogSaveDTO publicLogSaveDTO = new PublicLogSaveDTO();
			publicLogSaveDTO.setGoodsIds(list.stream().map(GoodsInfoEntity::getId).collect(Collectors.toList()))
					.setPublicityTime(first.getPublicityTime() + "")
					.setPublicityStartTime(first.getPublicityStartTime())
					.setPublicityEndTime(first.getPublicityEndTime())
					.setPublicLogType(PublicLogTypeEnum.PUBLIC.getCode())
					.setRemark(PublicLogConstant.SELECT_PUBLIC);
			publicLogService.saveOrUpdatePublicLog(publicLogSaveDTO);
		}
	}

	/**
	 * 计算公示时间：工作日（不计算周末）
	 * @param add goods记录
	 * @param now 起始日期
	 * @param putOffDays 顺延日期
	 */
	public static void calculatePublicityTime(GoodsInfoEntity add, LocalDateTime now, Integer putOffDays) {
		//获取公示期
		Integer period = getPeriodFromConfig(add.getSalePrice());
		if (ObjUtil.isNotNull(putOffDays)) {
			period = period + putOffDays;
		}
		LocalDate lastDay = now.toLocalDate();
		//计算公示结束时间（不算周末）
		for (int i = 0; i < period; i++) {
			//当前日期+1天，是否是周末，是：则继续+1，否则进入下一个循环
			lastDay = checkDateIsWeek(lastDay);
		}
		add.setPublicityTime(period);
		add.setPublicityStartTime(now.toLocalDate().plusDays(1).atTime(0,0,0));
		add.setPublicityEndTime(lastDay.atTime(23,59,59));
	}

	/**
	 * 公示时间顺延
	 * @param id 商品id
	 * @param putOffDays 顺延天数
	 */
	@Override
	public void calculatePublicityTimeWithPutOff(@NonNull Long id, @NonNull Integer putOffDays) {
		GoodsInfoEntity byId = this.getById(id);
		if (ObjUtil.isNull(byId)) {
			throw GoodsException.build(GoodsBizErrorEnum.GOODS_IS_NULL_ERROR);
		}
		if (ObjUtil.isNotNull(byId.getPublicityEndTime())) {
//			calculatePublicityTime(byId, byId.getPublicityEndTime(), putOffDays);
			calculatePublicityTime(byId, LocalDateTime.now(), putOffDays);
			//更新
			byId.setState(GoodsInfoEnum.State.PUBLICITY.getCode());
			this.updateById(byId);

			// 保存藏品公示日志, 状态转换: 待公示 -> 公示中
			PublicLogSaveDTO publicLogSaveDTO = new PublicLogSaveDTO();
			publicLogSaveDTO.setGoodsIds(Collections.singletonList(byId.getId()))
					.setPublicityTime(byId.getPublicityTime() + "")
					.setPublicityStartTime(byId.getPublicityStartTime())
					.setPublicityEndTime(byId.getPublicityEndTime())
					.setPublicLogType(PublicLogTypeEnum.PUBLIC.getCode())
					.setRemark(PublicLogConstant.AUDIT_FALSE);
			publicLogService.saveOrUpdatePublicLog(publicLogSaveDTO);
		}
	}

	public static LocalDate checkDateIsWeek(LocalDate today) {
		//当前日期+1天，是否是周末，是：则继续+1，否则进入下一个循环
		LocalDate nextDay = today.plusDays(1);
		DayOfWeek dayOfWeek = nextDay.getDayOfWeek();
		if (dayOfWeek != DayOfWeek.SATURDAY && dayOfWeek != DayOfWeek.SUNDAY) {
			log.info("{}-不是周末",nextDay);
			return nextDay;
		}
		log.info("{}-是周末",nextDay);
		return checkDateIsWeek(nextDay);
	}

	private static Integer getPeriodFromConfig(BigDecimal salePrice) {
		//获取公示周期
		String period = "";
		if (salePrice.compareTo(GoodsConstant.publicity_amount) > 0) {
			period = StrUtil.blankToDefault(DictResolver.getDictItemValue(GoodsConstant.publicity_period, GoodsConstant.publicity_period_fifty_above),GoodsConstant.publicity_15);
		}else {
			period = StrUtil.blankToDefault(DictResolver.getDictItemValue(GoodsConstant.publicity_period, GoodsConstant.publicity_period_fifty_under),GoodsConstant.publicity_7);
		}
		return Integer.valueOf(period);
	}

}
