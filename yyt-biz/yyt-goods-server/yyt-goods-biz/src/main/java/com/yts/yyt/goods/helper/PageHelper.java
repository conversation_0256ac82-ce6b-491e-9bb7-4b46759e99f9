package com.yts.yyt.goods.helper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yts.yyt.common.swagger.functional.CopyFunctional;
import lombok.experimental.UtilityClass;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: page处理类
 * <AUTHOR>
 * @date 2025/1/7
 */
@UtilityClass
public class PageHelper {
    public <F,T> Page<T> toPage(Page<F> page, Class<T> clazz){
        List<T> data =  new ArrayList<>();
        if(page.getRecords()!=null && page.getRecords().size()>0){
            for(F obj:page.getRecords()){
                try {
                    T newInstance = clazz.newInstance();
                    BeanUtils.copyProperties(obj, newInstance);
                    data.add(newInstance);
                } catch (InstantiationException e) {
                    throw new RuntimeException(e);
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        Page<T> result = new Page<>();
        result.setRecords(data);
        result.setTotal(page.getTotal());
        result.setCurrent(page.getCurrent());
        result.setSize(page.getSize());
        return result;

    }

    public <F,T> Page<T> toPage(Page<F> page, Class<T> clazz, CopyFunctional<F,T> copyFunctional){
        List<T> data =  new ArrayList<>();
        if(page.getRecords()!=null && page.getRecords().size()>0){
            for(F obj:page.getRecords()){
                try {
                    T newInstance = clazz.newInstance();
                    BeanUtils.copyProperties(obj, newInstance);
                    copyFunctional.after(obj,newInstance);
                    data.add(newInstance);
                } catch (InstantiationException e) {
                    throw new RuntimeException(e);
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        Page<T> result = new Page<>();
        result.setRecords(data);
        result.setTotal(page.getTotal());
        result.setCurrent(page.getCurrent());
        result.setSize(page.getSize());
        return result;

    }
}
