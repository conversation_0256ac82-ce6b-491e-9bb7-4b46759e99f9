package com.yts.yyt.goods.excel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.yts.yyt.goods.api.entity.GoodsTraceabilityPreEntity;
import com.yts.yyt.goods.excel.model.GoodsTraceabilityPreModel;

import java.util.ArrayList;
import java.util.List;

public class GoodsTraceabilityPreUploadListener extends AnalysisEventListener<GoodsTraceabilityPreModel> {
    private List<GoodsTraceabilityPreModel> goodsTraceabilityPreModelList = new ArrayList<>();



    // 每解析一行数据会调用此方法
    @Override
    public void invoke(GoodsTraceabilityPreModel goodsTraceabilityPreModel, AnalysisContext analysisContext) {
		goodsTraceabilityPreModelList.add(goodsTraceabilityPreModel);
    }

	// 所有数据解析完成后调用此方法
    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
    }

    public List<GoodsTraceabilityPreModel> getGoodsTraceabilityPreModelList() {
        return goodsTraceabilityPreModelList;
    }
}
