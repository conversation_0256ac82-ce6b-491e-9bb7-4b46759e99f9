package com.yts.yyt.goods.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yts.yyt.goods.api.entity.GoodsTransactionRecordEntity;

/**
 * @Description: 商品交易
 * <AUTHOR>
 * @date 2025/1/6
 */
public interface GoodsTransactionRecordService extends IService<GoodsTransactionRecordEntity> {
    /**
     * @Description: 需要重写save做额外操作
     * <AUTHOR>
     * @date 2025/1/7
     */
    boolean save(GoodsTransactionRecordEntity entity) ;
}
