package com.yts.yyt.goods.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yts.yyt.goods.api.entity.GoodsInfoLabelEntity;
import com.yts.yyt.goods.mapper.GoodsInfoLabelMapper;
import com.yts.yyt.goods.service.GoodsInfoLabelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Description: 商品类型
 * <AUTHOR>
 * @date 2025/1/6
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class,isolation = Isolation.READ_COMMITTED)
public class GoodsInfoLabelServiceImpl extends ServiceImpl<GoodsInfoLabelMapper, GoodsInfoLabelEntity> implements GoodsInfoLabelService {

}
