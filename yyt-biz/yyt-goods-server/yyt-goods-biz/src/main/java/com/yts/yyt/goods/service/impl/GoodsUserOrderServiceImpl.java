package com.yts.yyt.goods.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yts.yyt.goods.api.entity.GoodsUserOrderEntity;
import com.yts.yyt.goods.mapper.GoodsUserOrderMapper;
import com.yts.yyt.goods.service.GoodsUserOrderService;
import org.springframework.stereotype.Service;
/**
 * 用户商品订单
 *
 * <AUTHOR>
 * @date 2025-01-11 15:34:06
 */
@Service
public class GoodsUserOrderServiceImpl extends ServiceImpl<GoodsUserOrderMapper, GoodsUserOrderEntity> implements GoodsUserOrderService {
}