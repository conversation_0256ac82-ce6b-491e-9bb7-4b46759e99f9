package com.yts.yyt.goods.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yts.yyt.common.data.datascope.YytBaseMapper;
import com.yts.yyt.goods.api.entity.GoodsLikeRecordEntity;
import com.yts.yyt.goods.api.vo.GoodsRecordVO;
import com.yts.yyt.goods.api.vo.UserCollectionGoodsLotVO;

/**
 * @Description: 商品收藏记录
 * <AUTHOR>
 * @date 2025/1/6
 */
@Mapper
public interface GoodsLikeRecordMapper extends YytBaseMapper<GoodsLikeRecordEntity> {
    /**
     * @Description: 获取基本分页
     * <AUTHOR>
     * @date 2025/1/17
     */
    Page<UserCollectionGoodsLotVO> getBasePage(Page pageParam, @Param("ew") QueryWrapper wrapper);

    /**
     * 查询收藏记录
     */
    @Select("SELECT " +
            "gli.main_image as goods_image, " +
            "gli.name as goods_name, " +
            "gli.goods_no as goods_code, " +
            "gli.sale_price as price, " +
            "gli.state as status, " +
            "(SELECT GREATEST(IFNULL(SUM(CASE WHEN operate_type = 1 THEN 1 ELSE -1 END), 0), 0) " +
            "FROM goods_stats gs " +
            "WHERE gs.lot_id = gli.id " +
            "AND gs.type = #{type} " +
            "AND gs.del_flag = '0') as count, " +
            "glr.create_time as record_time " +
            "FROM goods_lot_info gli " +
            "LEFT JOIN goods_like_record glr ON gli.id = glr.lot_id " +
            "WHERE glr.user_id = #{userId} " +
            "ORDER BY glr.create_time DESC")
    Page<GoodsRecordVO> selectGoodsRecordPage(Page page, @Param("userId") Long userId, @Param("type") Integer type);

    /**
     * 查询浏览记录
     */
    @Select("SELECT " +
            "gli.main_image as goods_image, " +
            "gli.name as goods_name, " +
            "gli.goods_no as goods_code, " +
            "gli.sale_price as price, " +
            "gli.state as status, " +
            "(SELECT GREATEST(IFNULL(SUM(CASE WHEN operate_type = 3 THEN 1 ELSE -1 END), 0), 0) " +
            "FROM goods_stats gs " +
            "WHERE gs.lot_id = gli.id " +
            "AND gs.type = #{type} " +
            "AND gs.del_flag = '0') as count, " +
            "gf.create_time as record_time " +
            "FROM goods_lot_info gli " +
            "LEFT JOIN goods_footmark gf ON gli.id = gf.lot_id " +
            "WHERE gf.user_id = #{userId} " +
            "ORDER BY gf.create_time DESC")
    Page<GoodsRecordVO> selectFootmarkPage(Page page, @Param("userId") Long userId, @Param("type") Integer type);
}
