package com.yts.yyt.goods.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yts.yyt.common.core.util.LocalDateUtils;
import com.yts.yyt.goods.api.dto.CommissionQueryDTO;
import com.yts.yyt.goods.api.dto.GoodsCommissionRecordAddDTO;
import com.yts.yyt.goods.api.dto.GoodsCommissionRecordQueryDTO;
import com.yts.yyt.goods.api.entity.GoodsCommissionRecordEntity;
import com.yts.yyt.goods.api.entity.StoreGoodsInfo;
import com.yts.yyt.goods.api.vo.CommissionStatisticsVO;
import com.yts.yyt.goods.api.vo.GoodsCommissionRecordQueryVO;
import com.yts.yyt.goods.api.vo.GoodsCommissionStatisticsVO;
import com.yts.yyt.goods.mapper.GoodsCommissionRecordMapper;
import com.yts.yyt.goods.service.BaseRemoteService;
import com.yts.yyt.goods.service.GoodsCommissionRecordService;
import com.yts.yyt.merchant.api.dto.CityPartnerAndMerchantDTO;
import com.yts.yyt.merchant.api.entity.IncentiveConfigEntity;
import com.yts.yyt.merchant.api.enums.IncentiveConfTypeEnum;
import com.yts.yyt.merchant.api.enums.MerchantShopTypeEnum;
import com.yts.yyt.merchant.api.vo.IncentiveConfigVO;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 商品佣金明细表 服务实现类
 */
@Slf4j
@Service("goodsCommissionRecordService")
@RequiredArgsConstructor
public class GoodsCommissionRecordServiceImpl extends ServiceImpl<GoodsCommissionRecordMapper, GoodsCommissionRecordEntity>
 implements GoodsCommissionRecordService {

    private final BaseRemoteService baseRemoteService;
    private final GoodsCommissionRecordMapper goodsCommissionRecordMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addRecord(@Valid GoodsCommissionRecordAddDTO dto) {
        log.info("添加商品佣金记录入参：{}",dto);
        CityPartnerAndMerchantDTO partnerAndMerchantDTO = baseRemoteService.getPartnerByMerchantId(dto.getMerchantId());
        if (partnerAndMerchantDTO != null && Objects.equals(partnerAndMerchantDTO.getMerchant().getShopType(), MerchantShopTypeEnum.SELF_SUPPORT_SHOP.getType())) {
            log.info("自营合伙人或自营店铺无须计算佣金:商品ID{},商户ID{}",  dto.getGoodsId(),dto.getMerchantId());
            return true;
        }
        GoodsCommissionRecordEntity entity = new GoodsCommissionRecordEntity();
        BeanUtil.copyProperties(dto,entity);
        //计算基础佣金
        calcBaseCommission(entity,dto.getAppraisePrice());

        //计算专项激励佣金
        calcSpecialCommission(entity,dto.getAppraisePrice());

        //保存
        try {
            return save(entity);
        } catch (DuplicateKeyException e) {
            log.info("商品佣金记录已存在，无需重复添加,goodsId:{}", dto.getGoodsId());
        }
        return true;
    }

    @Override
    public void addRecord(StoreGoodsInfo storeGoodsInfo) {
        GoodsCommissionRecordAddDTO dto =  new GoodsCommissionRecordAddDTO();
        dto.setGoodsId(storeGoodsInfo.getId());
        dto.setAppraisePrice(storeGoodsInfo.getAppraisePrice());
        dto.setCityPartnerId(storeGoodsInfo.getCityPartnerId());
        dto.setMerchantId(storeGoodsInfo.getMerchantId());
        dto.setAppraisalTime(storeGoodsInfo.getAppraisalTime());
        addRecord(dto);
    }

    @Override
    public List<CommissionStatisticsVO> getPartnerCommissionStatistics(CommissionQueryDTO dto) {
        return goodsCommissionRecordMapper.getPartnerCommissionStatistics(dto);
    }

    @Override
    public List<GoodsCommissionStatisticsVO> getPartnerCommissionStatisticsV1(CommissionQueryDTO dto) {
        return goodsCommissionRecordMapper.getPartnerCommissionStatisticsV1(dto);
    }

    @Override
    public List<CommissionStatisticsVO> getMerchantCommissionStatistics(CommissionQueryDTO dto) {
        return goodsCommissionRecordMapper.getMerchantCommissionStatistics(dto);
    }

    @Override
    public List<GoodsCommissionStatisticsVO> getMerchantCommissionStatisticsV1(CommissionQueryDTO dto) {
        return goodsCommissionRecordMapper.getMerchantCommissionStatisticsV1(dto);
    }


    @Override
    public IPage<GoodsCommissionRecordQueryVO> pageQueryRecord(GoodsCommissionRecordQueryDTO dto) {
        Page<GoodsCommissionRecordQueryVO> pageDto = new Page<>(dto.getCurrent(), dto.getSize());

        String reportCycle = dto.getReportCycle();
        String reportDate = dto.getReportDate();
        if (StringUtils.equals("week", reportCycle) && StringUtils.isNotEmpty(reportDate)) {
            LocalDateTime cycleStart = LocalDateUtils.getDateTimeByWeekOfYear(reportDate);
            LocalDateTime cycleEnd = cycleStart.plusWeeks(1);
            dto.setCycleStart(LocalDateTimeUtil.format(cycleStart, DatePattern.NORM_DATETIME_PATTERN));
            dto.setCycleEnd(LocalDateTimeUtil.format(cycleEnd, DatePattern.NORM_DATETIME_PATTERN));
        } else if (StringUtils.equals("month", reportCycle) && StringUtils.isNotEmpty(reportDate)) {
            LocalDateTime cycleStart = LocalDateTimeUtil.parse(reportDate, DatePattern.SIMPLE_MONTH_PATTERN);
            LocalDateTime cycleEnd = cycleStart.plusMonths(1);
            dto.setCycleStart(LocalDateTimeUtil.format(cycleStart, DatePattern.NORM_DATETIME_PATTERN));
            dto.setCycleEnd(LocalDateTimeUtil.format(cycleEnd, DatePattern.NORM_DATETIME_PATTERN));
        }

        return goodsCommissionRecordMapper.pageQueryRecord(pageDto,dto);
    }

    /**
     * 计算基础激励佣金配置
     * @param entity 保存实体
     * @param appraisePrice 查询配置入参
     */
    private void calcBaseCommission(GoodsCommissionRecordEntity entity, BigDecimal appraisePrice) {
        //基础激励配置列表
        List<IncentiveConfigEntity> baseIncentiveConfigLs = baseRemoteService.getIncentiveConfigLs(IncentiveConfTypeEnum.CONF_TYPE1);
        log.info("佣金计算--->>>获取配置结果:{}" , baseIncentiveConfigLs);
        //获取匹配的基础激励
        IncentiveConfigEntity baseIncentiveConfig = null;
        // 循环计数器
        int loopCount = 0;
        for (IncentiveConfigEntity item : baseIncentiveConfigLs){
            // 增加循环计数器
            loopCount++;
            BigDecimal minVal = new BigDecimal(item.getMinVal());
            BigDecimal maxVal = new BigDecimal(item.getMaxVal());
            if (appraisePrice.compareTo(minVal) >= 0 && appraisePrice.compareTo(maxVal) <= 0) {
                baseIncentiveConfig = item;
                break;
            }
        }

        if(baseIncentiveConfig == null){
            log.info("佣金计算--->>>未匹配到基础佣金配置，参数：{}" , entity);
            return;
        }

        //如果配置为空，则结束
        if (ObjUtil.isEmpty(baseIncentiveConfig.getCommission()) || baseIncentiveConfig.getCommission().compareTo(BigDecimal.ZERO) < 1){
            log.info("佣金计算--->>>基础佣金配置为0 ");
            return;
        }

        // 计算商家基础佣金金额
        if(ObjUtil.isNotEmpty(baseIncentiveConfig.getMerchantRate()) && new BigDecimal(baseIncentiveConfig.getMerchantRate()).compareTo(BigDecimal.ZERO) > 0){
            BigDecimal merchantBaseAmount = baseIncentiveConfig.getCommission()
                    .multiply( new BigDecimal(baseIncentiveConfig.getMerchantRate()).divide(new BigDecimal(100)) )
                    .setScale(2, RoundingMode.DOWN);
            entity.setMerchantCommissionBase(merchantBaseAmount);
        }

        //计算合伙人基础佣金金额
        if(ObjUtil.isNotEmpty(baseIncentiveConfig.getPartnerRate()) && new BigDecimal(baseIncentiveConfig.getPartnerRate()).compareTo(BigDecimal.ZERO) > 0){
            BigDecimal partnerBaseAmount = baseIncentiveConfig.getCommission()
                    .multiply( new BigDecimal(baseIncentiveConfig.getPartnerRate()).divide(new BigDecimal(100)) )
                    .setScale(2, RoundingMode.DOWN);
            entity.setPartnerCommissionBase(partnerBaseAmount);
        }
        entity.setBaseType(loopCount);

        IncentiveConfigVO incentiveConfigVO = new IncentiveConfigVO();
        BeanUtil.copyProperties(baseIncentiveConfig, incentiveConfigVO);
        incentiveConfigVO.setGoodsCount(1L);
    }

    /**
     * 计算专项激励佣金配置
     * @param entity 保存实体
     * @param appraisePrice 查询配置入参
     */
    private void calcSpecialCommission(GoodsCommissionRecordEntity entity, BigDecimal appraisePrice) {
        //获取专项激励佣金激励配置
        List<IncentiveConfigEntity> specialIncentiveConfigLs = baseRemoteService.getIncentiveConfigLs(IncentiveConfTypeEnum.CONF_TYPE3);

        //获取匹配的专项激励
        IncentiveConfigEntity specialIncentiveConfig = null;
        for (IncentiveConfigEntity item : specialIncentiveConfigLs){
            BigDecimal minVal = new BigDecimal(item.getMinVal());
            BigDecimal maxVal = new BigDecimal(item.getMaxVal());
            if (appraisePrice.compareTo(minVal) >= 0 && appraisePrice.compareTo(maxVal) <= 0) {
                specialIncentiveConfig = item;
                break;
            }
        }
        log.info("佣金计算--->>>获取专项佣金配置结果:{}" , specialIncentiveConfig);

        //如果配置为空，则结束
        if (ObjUtil.isEmpty(specialIncentiveConfig) || ObjUtil.isEmpty(specialIncentiveConfig.getCommission()) || specialIncentiveConfig.getCommission().compareTo(BigDecimal.ZERO) < 1){
            return;
        }
        //计算合伙人专项佣金金额
        BigDecimal partnerSpecialAmount = specialIncentiveConfig.getCommission()
                .setScale(2, RoundingMode.DOWN);

        entity.setPartnerCommissionSpecial(partnerSpecialAmount);

        IncentiveConfigVO incentiveConfigVO = new IncentiveConfigVO();
        BeanUtil.copyProperties(specialIncentiveConfig, incentiveConfigVO);
        incentiveConfigVO.setGoodsCount(1L);
    }
}
