package com.yts.yyt.goods.controller;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.net.HttpHeaders;
import com.yts.yyt.common.core.dto.IdDTO;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.core.validation.UrlValidation;
import com.yts.yyt.common.feign.annotation.NoToken;
import com.yts.yyt.common.log.annotation.SysLog;
import com.yts.yyt.common.security.annotation.HasPermission;
import com.yts.yyt.common.security.annotation.Inner;
import com.yts.yyt.goods.api.dto.*;
import com.yts.yyt.goods.api.vo.*;
import com.yts.yyt.goods.service.StoreGoodsInfoService;
import com.yts.yyt.merchant.api.dto.BatchAddStoreGoodsDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@AllArgsConstructor
@RequestMapping("/storeGoodsInfo")
@Tag(description = "StoreGoodsInfoController", name = "藏品管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@Slf4j
public class StoreGoodsInfoController {
    
    private final StoreGoodsInfoService storeGoodsInfoService;

//	private final StoreGoodsBiz storeGoodsBiz;

	/**
	 * 新增藏品信息
	 * @param addParam 新增藏品参数
	 * @return 藏品id
	 */
    @Operation(summary = "新增商品信息 权限：merchant_goods_add", description = "新增商品信息 权限：merchant_goods_add")
    @SysLog("新增商品信息")
    @PostMapping("/add")
    @HasPermission("merchant_goods_add")
    @UrlValidation
    public R<String> save(@Valid @RequestBody AddStoreGoodsInfoDTO addParam) {
        storeGoodsInfoService.add(addParam);
        return R.ok(String.valueOf(addParam.getId()));
    }


	/**
	 * 修改藏品信息
	 * @param updateParam 修改藏品请求参数
	 * @return 是否成功
	 */
    @Operation(summary = "修改藏品信息 权限：merchant_goods_edit", description = "修改藏品信息 权限：merchant_goods_edit")
    @SysLog("修改商品信息")
    @PostMapping("/update")
    @HasPermission("merchant_goods_edit")
    public R<Boolean> updateById(@RequestBody AddStoreGoodsInfoDTO updateParam) {
        return R.ok(storeGoodsInfoService.updateInfo(updateParam));
    }

	/**
	 * 通过id删除藏品信息
	 * @param id 藏品id
	 * @return 是否成功
	 */
    @Operation(summary = "通过id删除藏品信息 权限：merchant_goods_delete", description = "通过id删除藏品信息 权限：merchant_goods_delete")
    @SysLog("通过id删除藏品信息")
    @GetMapping("del/{id}")
    @HasPermission("merchant_goods_delete")
    public R<Boolean> removeById(@PathVariable("id") Long id) {
        return R.ok(storeGoodsInfoService.removeById(id));
    }

//	/**
//	 * 根据藏品编号查询商品是否存在
//	 * @param goodsNo 藏品编号
//	 * @param id 藏品id
//	 * @return 是否存在
//	 */
//    @Operation(summary = "根据商品编号查询商品是否存在", description = "根据商品编号查询商品是否存在")
//    @SysLog("根据商品编号查询商品是否存在")
//    @GetMapping("/exist/{goodsNo}")
//    public R<Boolean> existByGoodsNo(@PathVariable("goodsNo") String goodsNo,
//                                     @RequestParam(value = "id", required = false) Long id) {
//        return R.ok(storeGoodsInfoService.existByGoodsNo(goodsNo, id));
//    }

	/**
	 * 藏品分页列表
	 * @param pageParam 分页参数
	 * @param search 搜索参数
	 * @return 分页列表
	 */
    @Operation(summary = "商品信息分页", description = "商品信息分页")
    @GetMapping("/page")
    @SysLog("商品信息分页")
    public R<Page<StoreGoodsInfoVO>> page(Page pageParam, @ParameterObject SearchStoreGoodsInfoDTO search) {
        Page<StoreGoodsInfoVO> page = storeGoodsInfoService.getBasePage(pageParam, search);
        return  R.ok(page);
    }

	/**
	 * 商品信息详细
	 * @param id 藏品id
	 * @return 藏品详情
	 */
    @Operation(summary = "商品信息详细", description = "商品信息详细")
    @GetMapping("/info/{id}")
    @SysLog("商品信息详细")
    public R<StoreGoodsInfoVO> infoById(@PathVariable("id") Long id) {
        StoreGoodsInfoVO vo = storeGoodsInfoService.getVOById(id);
        return R.ok(vo);
    }

	/**
	 * 管理后台-藏品统计
	 * 财务人员查看各个状态的藏品数量
	 * @return  藏品状态数量
	 */
    @Operation(summary = "管理后台-藏品统计", description = "管理后台-藏品统计，财务人员查看，接口前缀 /goods")
    @PostMapping("/storeGoodsCount")
    @SysLog("管理后台-藏品统计")
    public R<GoodsStatisticVO> storeGoodsCount(@RequestBody StoreGoodsCountDTO dto) {
		GoodsStatisticVO result = storeGoodsInfoService.storeGoodsCount(dto);
        return R.ok(result);
    }

	/**
	 * 批量提交鉴定
	 * @param batchUpdateState 提交鉴定请求参数
	 * @return 成功
	 */
    @Operation(summary = "提交鉴定 权限：merchant_goods_identify", description = "提交鉴定 权限：merchant_goods_identify")
    @PostMapping("/submitAppraisal")
    @SysLog("提交鉴定")
    @HasPermission("merchant_goods_identify")
    public R<Boolean> submitAppraisal(@RequestBody StoreUpdateStateDTO batchUpdateState) {
        return R.ok(storeGoodsInfoService.submitAppraisal(batchUpdateState));
    }


	/**
	 * Feign 域鉴鉴定回调
	 * @param requestBody 域鉴请求我方参数
	 * @return 返回域鉴我方处理结果
	 */
    @NoToken
    @Inner(value = false)
    @Operation(summary = "域鉴鉴定回调", description = "域鉴鉴定回调")
    @PostMapping("/yujian/callback")
    @SysLog("域鉴鉴定回调")
    public JSONObject yujianCallback(@RequestBody Map<String, Object> requestBody) {
        log.info("域鉴鉴定回调,入参：{}", JSONUtil.toJsonStr(requestBody));
        JSONObject data = storeGoodsInfoService.yujianCallback(requestBody);
        return data;
    }

    /**
     * 域鉴专家回复鉴定回调
     * @param requestBody 域鉴请求我方参数
     * @return 返回域鉴我方处理结果
     */
    @NoToken
    @Inner(value = false)
    @Operation(summary = "域鉴专家回复鉴定回调", description = "域鉴专家回复鉴定回调")
    @PostMapping("/yujian/reply/callback")
    @SysLog("域鉴专家回复鉴定回调")
    public JSONObject yujianReplyCallback(@RequestBody Map<String, Object> requestBody) {
        return storeGoodsInfoService.yujianReplyCallback(requestBody);
    }


    /**
     * 过度接口，后续需删除
     * Feign 域鉴鉴定回调
     * @param requestBody 域鉴请求我方参数
     * @return 返回域鉴我方处理结果
     */
    @Inner
    @Operation(summary = "域鉴鉴定回调-（过度待删）", description = "域鉴鉴定回调-（过度待删）")
    @PostMapping("/feign/yujian/callback")
    @SysLog("域鉴鉴定回调")
    public JSONObject feignYujianCallback(@RequestBody Map<String, Object> requestBody) {
        log.info("域鉴鉴定回调,入参：{}", JSONUtil.toJsonStr(requestBody));
        JSONObject data = storeGoodsInfoService.yujianCallback(requestBody);
        return data;
    }

//	/**
//	 * 上传藏品
//	 * @param file 文件
//	 * @param merchantId 商家id
//	 * @return 是否上传成功
//	 */
//	@Operation(summary = "上传藏品", description = "上传藏品")
//	@PostMapping("/uploadStoreGoods")
//    @HasPermission("merchant_goods_add")
//	@Idempotent(key = "T(com.yts.yyt.common.security.util.SecurityUtils).getUser.getId()", expireTime = 20, info = "请求频繁，请稍后重试")
//	public R<List<ErrorMessage>> uploadStoreGoods(@RequestParam("file") MultipartFile file,
//												  @RequestParam(value = "merchantId", required = false) Long merchantId) {
//		log.info("uploadStoreGoods 开始。。。");
//		List<ErrorMessage> errorMessages = storeGoodsBiz.uploadStoreGoodsInfo(file, merchantId);
//		if(CollectionUtil.isNotEmpty(errorMessages)) {
//			return R.failed(errorMessages);
//		}
//		return R.ok();
//	}

	/**
	 * Feign 通过goodNo获取藏品详细
	 * @param goodNo 藏品编号
	 * @return 藏品详情
	 */
    @Inner
    @GetMapping("/feign/info/{goodNo}")
    @SysLog("通过goodNo获取藏品详细")
    R<StoreGoodsInfoFeignVO> getInfo(@PathVariable("goodNo") String goodNo){
       return R.ok(storeGoodsInfoService.getInfo(goodNo));
    }

    /**
     * Feign 获取符合条件的藏品ID集合
     * @param dto 请求参数
     * @return 藏品id集合
     */
    @Inner
    @PostMapping("/feign/storeGoodsInfoForIncentivePartner")
    public R<List<CommissionLadderCountVO>> storeGoodsInfoForIncentivePartner(@RequestBody CommissionQueryDTO dto){
        return R.ok(storeGoodsInfoService.storeGoodsInfoForIncentivePartner(dto));
    }

    /**
     * 获取符合条件的藏品数
     * @param dto 请求参数
     * @return 藏品数量
     */
    @Inner
    @PostMapping("/feign/countAppraisalSuccessGoodsByTimeRange")
    public R<Long> countAppraisalSuccessGoodsByTimeRange(@RequestBody StoreGoodsInfoIncentiveCountDTO dto){
        return R.ok(storeGoodsInfoService.countAppraisalSuccessGoodsByTimeRange(dto));
    }

	/**
	 * 藏品批量公示
	 * @param param 请求参数: 藏品id集合
	 * @return 是否成功
	 */
	@Operation(summary = "管理后台-藏品批量公示", description = "管理后台-藏品批量公示，权限-merchant_goods_publicity")
	@SysLog("藏品批量公示")
	@PostMapping("/batchPublicity")
	@HasPermission("merchant_goods_publicity")
	public R<Boolean> batchPublicity(@Valid @RequestBody GoodsPublicityDTO param) {
		storeGoodsInfoService.batchPublicity(param);
		return R.ok(Boolean.TRUE);
	}

	/**
	 * 管理后台-取消公示
	 * @param dto 请求参数: 藏品id集合
	 * @return 是否成功
	 */
	@Operation(summary = "管理后台-取消公示", description = "管理后台-取消公示，权限-merchant_goods_cancel_publicity")
	@SysLog("管理后台-取消公示")
	@PostMapping("/cancelPublicity")
	@HasPermission("merchant_goods_cancel_publicity")
	public R<Boolean> cancelPublicity(@Valid @RequestBody IdDTO dto) {
		return R.ok(storeGoodsInfoService.cancelPublicity(dto));
	}

    /**
     * 管理后台-运营审核
     * @param dto 请求参数: 藏品id集合
     * @return 是否成功
     */
    @Operation(summary = "管理后台-运营审核", description = "管理后台-运营审核，权限-merchant_goods_audit")
    @SysLog("管理后台-运营审核")
    @PostMapping("/audit")
    @HasPermission("merchant_goods_audit")
    public R<Boolean> audit(@Valid @RequestBody GoodsAuditDTO dto) {
        return R.ok(storeGoodsInfoService.audit(dto));
    }

    /**
     * 批量新增商品
     */
    @Operation(summary = "批量新增商品 权限：merchant_goods_add", description = "批量新增商品 权限：merchant_goods_add")
    @SysLog("批量新增商品")
    @PostMapping("/batchAdd")
    @HasPermission("merchant_goods_add")
    public R<List<String>> batchAdd(@Valid @RequestBody BatchAddStoreGoodsDTO batchAddParam) {
        log.info("批量上传藏品，请求参数:{}",batchAddParam);
        List<String> errorList = storeGoodsInfoService.batchAddStoreGoods(batchAddParam);
        if(CollectionUtil.isEmpty(errorList)) {
            return R.ok();
        }
        return R.failed(errorList,"存在上传失败的藏品数据");
    }

	/**
	 * 客户端-公示查询(分页)
	 */
	@Operation(summary = "客户端-公示查询(分页)", description = "客户端-公示查询(分页)")
    @PostMapping("/pageForSearch")
	@Inner(false)
	@SysLog("客户端-公示查询(分页)")
    public R<List<StoreGoodsInfoAppVO>> pageForSearch(@RequestBody StoreGoodsSearchDTO searchDTO) {
        return R.ok(storeGoodsInfoService.pageForSearch(searchDTO));
    }

    /**
     * 客户端-公示拍品搜索(分页)v2
     */
    @Operation(summary = "客户端-公示拍品搜索(分页)v2", description = "客户端-公示拍品搜索(分页)v2")
    @PostMapping("/v2/pageForSearch")
    @SysLog("客户端-公示拍品搜索(分页)v2")
    @Inner(false)
    public R<StoreGoodsInfoAppPageVO> pageForSearchV2(@RequestBody GoodsSearchV2DTO search) {
        StoreGoodsInfoAppPageVO page = storeGoodsInfoService.pageForSearchV2(search);
        return R.ok(page);
    }

    /**
     * 客户端-公示拍品查询v1
     */
    @Operation(summary = "客户端-公示拍品查询v1", description = "客户端-公示拍品查询v1")
    @PostMapping("/v1/pageForRecommend")
    @SysLog("客户端-公示拍品查询v1")
    @Inner(false)
    public R<StoreGoodsInfoAppPageVO> pageForRecommendV1(@RequestBody GoodsRecommendV1DTO dto) {
        StoreGoodsInfoAppPageVO page = storeGoodsInfoService.pageForRecommendV1(dto);
        return R.ok(page);
    }

	/**
	 * 客户端-公示详情
	 *
	 * @param dto 主键
	 * @return 单条数据
	 */
	@PostMapping("/detail")
	@Operation(summary = "客户端-公示详情", description = "客户端-公示详情")
	@Inner(false)
	@SysLog("客户端-公示详情")
	public R<StoreGoodsInfoVO> detail(@RequestBody IdDTO dto) {
		StoreGoodsInfoVO vo = storeGoodsInfoService.getVOById(Long.valueOf(dto.getId()));
		return R.ok(vo);
	}

}

