package com.yts.yyt.goods.service;


import com.yts.yyt.common.core.dto.IdDTO;
import com.yts.yyt.common.core.dto.PriceDto;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.distribution.api.dto.DistLotInfoUpdateDTO;
import com.yts.yyt.distribution.api.feign.RemoteDistLotInfoService;
import com.yts.yyt.goods.api.exception.GoodsBizErrorEnum;
import com.yts.yyt.goods.api.exception.GoodsException;
import com.yts.yyt.goods.api.feign.RemoteStoreGoodsInfoService;
import com.yts.yyt.merchant.api.dto.CityPartnerAndMerchantDTO;
import com.yts.yyt.merchant.api.dto.MerchantDTO;
import com.yts.yyt.merchant.api.entity.CityPartner;
import com.yts.yyt.merchant.api.entity.IncentiveConfigEntity;
import com.yts.yyt.merchant.api.enums.IncentiveConfTypeEnum;
import com.yts.yyt.merchant.api.feign.RemoteCityPartnerService;
import com.yts.yyt.merchant.api.feign.RemoteIncentiveConfigService;
import com.yts.yyt.merchant.api.feign.RemoteMerchantService;
import com.yts.yyt.user.api.feign.RemoteUserHuifuAccountService;
import com.yts.yyt.user.api.vo.UserHuifuAccountQueryVO;
import com.yts.yyt.distribution.api.feign.RemoteGoodsCommissionService;
import com.yts.yyt.distribution.api.dto.CalculateCommissionDTO;
import com.yts.yyt.distribution.api.vo.CalculateCommissionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@Slf4j
@Service
public class BaseRemoteService {

    @Lazy
    @Autowired
    private RemoteStoreGoodsInfoService remoteStoreGoodsInfoService;

    @Lazy
    @Autowired
    private RemoteCityPartnerService remoteCityPartnerService;

    @Lazy
    @Autowired
    private RemoteIncentiveConfigService remoteIncentiveConfigService;
    
    @Lazy
    @Autowired
    private RemoteMerchantService remoteMerchantService;

    @Lazy
    @Autowired
    private RemoteUserHuifuAccountService remoteUserHuifuAccountService;

    @Lazy
    @Autowired
    private RemoteGoodsCommissionService remoteGoodsCommissionService;

	@Autowired
	private RemoteDistLotInfoService remoteDistLotInfoService;

	/**
	 * 根据merchantId查询汇付账户信息
	 * @return
	 */
	public UserHuifuAccountQueryVO getUserHuifuAccountInfoByMerchantId(Long merchantId){
		IdDTO idDTO = new IdDTO();
		idDTO.setId(String.valueOf(merchantId));
		R<UserHuifuAccountQueryVO> r = remoteUserHuifuAccountService.query(idDTO);
		// 调用结果有处理，不做判断空处理，直接返回
		if (r == null || !r.isOk()) {
			throw GoodsException.build(GoodsBizErrorEnum.QUERY_MERCHANT_INFO_ERROR);
		}
		return r.getData();
	}

    /**
     * 根据id获取合伙人信息
     * @return
     */
    public CityPartner getCityPartnerById(Long id){
        IdDTO idDTO = new IdDTO();
        idDTO.setId(String.valueOf(id));
        R<CityPartner> r = remoteCityPartnerService.getCityPartnerById(idDTO);
        // 调用结果有处理，不做判断空处理，直接返回
        return r.getData();
    }

    /**
     * 根据商家id获取商户合伙人信息
     * @return
     */
    public CityPartnerAndMerchantDTO getPartnerByMerchantId(Long id){
        IdDTO idDTO = new IdDTO();
        idDTO.setId(String.valueOf(id));
        R<CityPartnerAndMerchantDTO> r = remoteMerchantService.getPartnerByMerchantId(idDTO);
        // 调用结果有处理，不做判断空处理，直接返回
        return r.getData();
    }

    /**
     * 根据移动端用户id获取商户信息
     * @return
     */
    public MerchantDTO getMerchantByUserId(Long id){
        IdDTO idDTO = new IdDTO();
        idDTO.setId(String.valueOf(id));
		R<MerchantDTO> r = remoteMerchantService.getMerchantByUserId(idDTO);
		// 调用结果有处理，不做判断空处理，直接返回
		if (r == null || !r.isOk() || r.getData() == null) {
			throw GoodsException.build(GoodsBizErrorEnum.QUERY_MERCHANT_INFO_ERROR);
		}
        return r.getData();
    }

    /**
     * 根据价格获取响应配置等级
     * @return
     */
    public String getAppraisalLevel(BigDecimal appraisePrice){
        PriceDto priceDto = new PriceDto();
        priceDto.setPrice(appraisePrice);
        R<String> r = remoteIncentiveConfigService.getAppraisalLevel(priceDto);
        // 调用结果有处理，不做判断空处理，直接返回
        return r.getData();
    }

    /**
     * 通过类型获取配置数据
     * @param confTypeEnum 查询条件
     * @return List<IncentiveConfigEntity>
     */
    public List<IncentiveConfigEntity> getIncentiveConfigLs(IncentiveConfTypeEnum confTypeEnum){
        R<List<IncentiveConfigEntity>>  resR = remoteIncentiveConfigService.getConfigLsByType(confTypeEnum.getType());
        if(resR == null || !resR.isOk() || resR.getData().isEmpty()) {
            throw GoodsException.build(GoodsBizErrorEnum.INCENTIVE_CONF_NOT_EXIST);
        }
        return resR.getData();
    }

	/**
	 * 根据区域id获取合伙人信息
	 */
	public List<CityPartner> getPartnerByRegionId(String regionId) {
		IdDTO idDTO = new IdDTO();
		idDTO.setId(regionId);
		R<List<CityPartner>> r = remoteMerchantService.getPartnerByRegionId(idDTO);
		// 调用结果有处理，不做判断空处理，直接返回
		if (r == null || !r.isOk()) {
			throw GoodsException.build(GoodsBizErrorEnum.QUERY_MERCHANT_INFO_ERROR);
		}
		return r.getData();
	}

	/**
	 * 根据系统用户id查询商户id
	 */
	public List<Long> getMerchantIdsBySysUserId(Long sysUserId) {
		R<List<Long>> r = remoteMerchantService.getMerchantIdsBySysUserId(sysUserId);
		// 调用结果有处理，不做判断空处理，直接返回
		if (r == null || !r.isOk()) {
			throw GoodsException.build(GoodsBizErrorEnum.QUERY_MERCHANT_INFO_ERROR);
		}
		return r.getData();
	}

	/**
	 * 获取拍品佣金
	 * @param lotId 拍品id
	 * @param userId 用户id
	 * @param salePrice 销售价格
	 * @return 佣金信息
	 */
	public CalculateCommissionVO calculateCommission(Long lotId, Long userId, BigDecimal salePrice) {
		log.info("【BaseRemoteService.getCommissionByLotIdAndUserId】获取拍品佣金，拍品id：{}，用户id：{}，销售价格：{}", lotId, userId, salePrice);
		try {
			// 构建请求参数
			CalculateCommissionDTO dto = new CalculateCommissionDTO();
			dto.setLotId(lotId);
			dto.setUserId(userId);
			dto.setSalePrice(salePrice);
			// 调用分销模块的佣金计算接口
			R<CalculateCommissionVO> result = remoteGoodsCommissionService.calculateCommission(dto);
			if (result == null || !result.isOk() || result.getData() == null) {
				log.warn("【BaseRemoteService.getCommissionByLotIdAndUserId】调用分销模块计算佣金失败，返回默认值");
				return createDefaultCommissionInfo();
			}
			CalculateCommissionVO calculateResult = result.getData();
			log.info("【BaseRemoteService.getCommissionByLotIdAndUserId】获取拍品佣金成功，佣金金额：{}，是否分销员：{}",
					calculateResult.getCommissionAmount(), calculateResult.getIsDistributor());
			return calculateResult;
		} catch (Exception e) {
			log.error("【BaseRemoteService.getCommissionByLotIdAndUserId】调用分销模块计算佣金异常，错误信息：{}", e.getMessage(), e);
			return createDefaultCommissionInfo();
		}
	}

	/**
	 * 创建默认佣金信息（用户不是分销员时）
	 */
	private CalculateCommissionVO createDefaultCommissionInfo() {
		CalculateCommissionVO defaultInfo = new CalculateCommissionVO();
		defaultInfo.setCommissionAmount(BigDecimal.ZERO);
		defaultInfo.setIsDistributor(0);
		return defaultInfo;
	}

	/**
	 * 记录分销点击统计
	 * @param shortCode 分享短码
	 */
	public void recordDistributionClick(String shortCode) {
		try {
			log.debug("【BaseRemoteService.recordDistributionClick】记录分销点击统计，分享短码：{}", shortCode);
			// 调用分销模块的点击统计接口
			remoteGoodsCommissionService.recordClickStatistics(shortCode);
			log.debug("【BaseRemoteService.recordDistributionClick】调用分销模块记录点击统计成功");
		} catch (Exception e) {
			log.error("【BaseRemoteService.recordDistributionClick】调用分销模块记录点击统计异常：{}", e.getMessage(), e);
			// 异步调用失败不影响主流程
		}
	}

	/**
	 * 更新分销拍品信息
	 * @param ids 拍品id列表
	 * @param salePrice 销售价格
	 * @param name 拍品名称
	 */
	public void updateLotInfo(List<Long> ids, BigDecimal salePrice, String name) {
		log.info("【BaseRemoteService.updateLotInfo】更新分销拍品信息，拍品数量：{}，价格：{}，名称：{}", ids.size(), salePrice, name);
		// 构建批量更新DTO
		DistLotInfoUpdateDTO dto = new DistLotInfoUpdateDTO();
		dto.setLotIds(ids);
		dto.setSalePrice(salePrice);
		dto.setLotName(name);
		// 调用feign接口进行批量更新
		R<Boolean> r = remoteDistLotInfoService.updateLotInfo(dto);
		if (r == null || !r.isOk()) {
			// todo 更新失败不影响主流程
			log.warn("【BaseRemoteService.updateLotInfo】批量更新分销拍品信息失败");
		} else {
			log.info("【BaseRemoteService.updateLotInfo】批量更新分销拍品信息成功");
		}
	}
}
