<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yts.yyt.goods.mapper.StoreGoodsInfoMapper">

    <resultMap type="com.yts.yyt.goods.api.entity.StoreGoodsInfo" id="StoreGoodsInfoMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="cityPartnerId" column="city_partner_id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="placeOfOrigin" column="place_of_origin" jdbcType="VARCHAR"/>
        <result property="mainImage" column="main_image" jdbcType="VARCHAR"/>
        <result property="publicityTime" column="publicity_time" jdbcType="INTEGER"/>
        <result property="publicityStartTime" column="publicity_start_time" jdbcType="TIMESTAMP"/>
        <result property="publicityEndTime" column="publicity_end_time" jdbcType="TIMESTAMP"/>
        <result property="recordFilingNumber" column="record_filing_number" jdbcType="VARCHAR"/>
        <result property="ownership" column="ownership" jdbcType="VARCHAR"/>
        <result property="salePrice" column="sale_price" jdbcType="NUMERIC"/>
        <result property="appraisePrice" column="appraise_price" jdbcType="NUMERIC"/>
        <result property="currentPrice" column="current_price" jdbcType="NUMERIC"/>
        <result property="goodsNo" column="goods_no" jdbcType="VARCHAR"/>
        <result property="stockQuantity" column="stock_quantity" jdbcType="INTEGER"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="state" column="state" jdbcType="VARCHAR"/>
        <result property="appraisalAuthenticity" column="appraisal_authenticity" jdbcType="VARCHAR"/>
        <result property="appraisalGuanWare" column="appraisal_guan_ware" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="VARCHAR"/>
    </resultMap>
    
    <select id="getBasePage" resultType="com.yts.yyt.goods.api.vo.StoreGoodsInfoVO">
        SELECT
            gi.id,
            gi.name,
            gi.goods_no as goodsNo,
            gi.goods_era as goodsEra,
            gi.main_image,
            gi.sale_price as salePrice,
            gi.create_time as createTime,
            gi.appraisal_time as appraisalTime,
            gi.state as state,
            c.partner_name,
            c.contact_phone,
            m.shop_name,
            m.contact_person_name,
            m.shop_avatar as shopAvatar,
            m.contact_phone as merchantContactPhone,
            m.shop_type
        FROM
        store_goods_info gi
        LEFT JOIN city_partner c on c.id = gi.city_partner_id
        LEFT JOIN merchant_info m on m.id = gi.merchant_id
        where gi.del_flag = 0
            <if test="p.name != null and p.name !=''">
                and  gi.name like concat('%',#{p.name},'%')
            </if>
            <if test="p.shopName != null and p.shopName !=''">
                and  m.shop_name like concat('%',#{p.shopName},'%')
            </if>
            <if test="p.id != null and p.id !=''">
                and  gi.id  = #{p.id}
            </if>
            <if test="p.state != null and p.state !=''">
                and  gi.state  = #{p.state}
            </if>
            <if test="p.goodsNo != null and p.goodsNo !=''">
                and  gi.goods_no  = #{p.goodsNo}
            </if>
            <if test="p.merchantContactPhone != null and p.merchantContactPhone !=''">
                and  (c.contact_phone  = #{p.merchantContactPhone} or m.contact_phone = #{p.merchantContactPhone})
            </if>
            <if test="p.merchantId != null and p.merchantId !=''">
                and  (m.id  = #{p.merchantId} or c.id = #{p.merchantId})
            </if>
			<if test="p.merchantIds != null and p.merchantIds.size() > 0">
				and m.id in
				<foreach item="merchantId" index="index" collection="p.merchantIds" open="(" separator="," close=")">
					#{merchantId}
				</foreach>
			</if>
            <if test="p.startCreateTime !=null and p.startCreateTime !='' and p.endCreateTime !=null and p.endCreateTime !=''">
                and gi.create_time BETWEEN #{p.startCreateTime} and #{p.endCreateTime}
            </if>
            <if test="p.startAppraisalTime !=null and p.startAppraisalTime !='' and p.endAppraisalTime !=null and p.endAppraisalTime !=''">
                and gi.appraisal_time BETWEEN #{p.startAppraisalTime} and #{p.endAppraisalTime}
            </if>
        order by gi.id desc
    </select>
    <select id="getVOById" resultType="com.yts.yyt.goods.api.vo.StoreGoodsInfoVO">
        SELECT
            gi.id,
            gi.merchant_id,
            gi.appraisal_no,
			gi.name,
			gi.goods_no as goodsNo,
			gi.goods_era as goodsEra,
			gi.main_image,
			gi.sale_price as salePrice,
			gi.goods_condition as goodsCondition,
			gi.place_of_origin as placeOfOrigin,
			gi.goods_size as goodsSize,
			gi.description as description,
			gi.create_time as createTime,
			gi.state as state,
			gi.appraisal_time as appraisalTime,
			gi.appraise_price as appraisePrice,
			tyga.age_name as appraisalEra,
			gi.appraisal_authenticity as appraisalAuthenticity,
			gi.appraisal_level as appraisalLevel,
			gi.publicity_time as publicityTime,
			gi.publicity_start_time as publicityStartTime,
			gi.publicity_end_time as publicityEndTime,
            c.partner_name,
            c.contact_phone,
            m.shop_name,
            m.contact_person_name,
            m.shop_avatar shopAvatar,
            m.contact_phone as merchantContactPhone,
            m.shop_type,
			ts.tran_name,
			ts.tran_code,
			ts.tran_icon
        FROM
            store_goods_info gi
                LEFT JOIN city_partner c on c.id = gi.city_partner_id
                LEFT JOIN merchant_info m on m.id = gi.merchant_id
				left join transaction_setting ts on m.benefit_level = ts.tran_code
				left join t_yj_goods_age tyga on gi.era_id = tyga.id
        where gi.del_flag = 0
        and gi.id = #{id}
    </select>
    <select id="getStoreGoodsConut" resultType="com.yts.yyt.goods.api.vo.StoreGoodsConutVO">
        SELECT
            count( 1 ) AS allCount,
            SUM( CASE WHEN `state` = 'pre_appraisal' THEN 1 ELSE 0 END ) AS preAppraisalCount,
            SUM( CASE WHEN `state` = 'wait_appraisal' THEN 1 ELSE 0 END ) As waitAppraisalCount ,
            SUM( CASE WHEN `state` = 'fail_appraisal' THEN 1 ELSE 0 END ) As failAppraisalCount ,
            SUM( CASE WHEN `state` = 'pre_publicity' THEN 1 ELSE 0 END ) As prePublicityCount ,
            SUM( CASE WHEN `state` = 'publicity' THEN 1 ELSE 0 END ) As publicityCount
--             SUM( CASE WHEN `state` = 'wait_selling' THEN 1 ELSE 0 END ) As waitSellingCount ,
--             SUM( CASE WHEN `state` = 'selling' THEN 1 ELSE 0 END ) As sellingCount ,
--             SUM( CASE WHEN `state` = 'locking' THEN 1 ELSE 0 END ) As lockingCount ,
--             SUM( CASE WHEN `state` = 'sold' THEN 1 ELSE 0 END ) As soldCount
        FROM
            store_goods_info gi
        WHERE
            gi.del_flag = 0
        <if test="p.merchantId != null and p.merchantId !=''">
            and  (gi.city_partner_id  = #{p.merchantId} or gi.merchant_id = #{p.merchantId})
        </if>
        <if test="p.merchantId != null and p.merchantId !=''">
            and  (gi.city_partner_id  = #{p.merchantId} or gi.merchant_id = #{p.merchantId})
        </if>
    </select>

    <!--    合伙人商品上架佣金统计：找出在周期内未被统计的合伙人-->
    <select id="storeGoodsInfoForIncentivePartner" resultType="com.yts.yyt.goods.api.vo.CommissionLadderCountVO">
        SELECT
        city_partner_id,
        goodsCount
        FROM (
            SELECT
                sgi.city_partner_id,
                count(1) as goodsCount
            FROM store_goods_info sgi
            join merchant_info mi on sgi.merchant_id = mi.id
            WHERE sgi.del_flag = 0
                AND sgi.appraisal_state = 1
                AND sgi.appraisal_time BETWEEN #{dto.startTime} and #{dto.endTime}
                AND mi.shop_type in (1,2)
                AND NOT EXISTS (
                    SELECT 1
                    FROM incentive_commission ic
                    WHERE ic.role_uid = sgi.city_partner_id
                    and ic.role_type = '2'
                    and ic.cycle_start = #{dto.startTime}
                    and ic.cycle_end = #{dto.endTime}
                )
            GROUP BY sgi.city_partner_id
        ) AS subquery
        <if test="dto.limit != null">
            limit #{dto.limit}
        </if>
    </select>

    <select id="listForSearch" resultType="com.yts.yyt.goods.api.vo.StoreGoodsInfoAppVO">
		select a.id,
			   a.sale_price,
			   a.name,
			   a.main_image,
			   a.state,
			   tyga.age_name as goodsEra,
			   a.merchant_id,
			   m.shop_name,
			   m.benefit_level,
			   m.shop_avatar shopAvatar,
			   ts.tran_name,
			   ts.tran_code,
			   ts.tran_icon
		from store_goods_info a
				 left join merchant_info m on a.merchant_id = m.id
				 left join transaction_setting ts on m.benefit_level = ts.tran_code
				 left join t_yj_goods_age tyga on a.era_id = tyga.id
			${ew.customSqlSegment}
	</select>

    <select id="countByState" resultType="com.yts.yyt.goods.api.vo.StoreGoodsCountResultVO">
		SELECT
		    a.state,
		    COUNT(1) AS count
		FROM
		    store_goods_info a
			left join city_partner p on a.city_partner_id = p.id
			left join merchant_info m on a.merchant_id = m.id
		    ${ew.customSqlSegment}
		GROUP BY
		    a.state
	</select>
	<!-- 数据分析 开始 -->
	<select id="getSalesAmountAndCount" resultType="com.yts.yyt.goods.api.vo.StatisticCardVO">
		select
			COALESCE(COUNT(1), 0) AS salesCount,
			COALESCE(SUM(o.amount - IFNULL(c.coupon_amount, 0)), 0) AS salesAmount
		from order_info o
		LEFT JOIN coupon_usage c ON o.id = c.order_id AND c.status in (2,5,6) AND c.del_flag = '0'
		where o.`status` = 'first_launch_completed'
			and o.del_flag = '0'
        <if test="dto.startTime != null">
            and o.create_time >= #{dto.startTime}
        </if>
        <if test="dto.endTime != null">
            and o.create_time &lt;= #{dto.endTime}
        </if>
	</select>

	<select id="getJoinStatisticTotal" resultType="com.yts.yyt.goods.api.dto.StatisticsResult">
		SELECT
			(SELECT COUNT(1) FROM city_partner WHERE del_flag = 0
			<if test="dto.startTime != null">
				AND create_time >= #{dto.startTime}
			</if>
			<if test="dto.endTime != null">
				AND create_time &lt;= #{dto.endTime}
			</if>
			) AS totalPartners,
			(SELECT COUNT(1) FROM merchant_info WHERE del_flag = 0
			<if test="dto.startTime != null">
				AND create_time >= #{dto.startTime}
			</if>
			<if test="dto.endTime != null">
				AND create_time &lt;= #{dto.endTime}
			</if>
			) AS totalMerchants
	</select>
	
	<select id="getPartnerJoinList" resultType="java.util.Map">
		SELECT 
			DATE_FORMAT(create_time, '%Y-%m-%d') AS joinDate,
			COUNT(1) AS count
		FROM city_partner 
		WHERE del_flag = 0
		<if test="dto.startTime != null">
			AND create_time >= #{dto.startTime}
		</if>
		<if test="dto.endTime != null">
			AND create_time &lt;= #{dto.endTime}
		</if>
		GROUP BY DATE_FORMAT(create_time, '%Y-%m-%d')
		ORDER BY joinDate
	</select>
	
	<select id="getMerchantJoinList" resultType="java.util.Map">
		SELECT 
			DATE_FORMAT(create_time, '%Y-%m-%d') AS joinDate,
			COUNT(1) AS count
		FROM merchant_info 
		WHERE del_flag = 0
		<if test="dto.startTime != null">
			AND create_time >= #{dto.startTime}
		</if>
		<if test="dto.endTime != null">
			AND create_time &lt;= #{dto.endTime}
		</if>
		GROUP BY DATE_FORMAT(create_time, '%Y-%m-%d')
		ORDER BY joinDate
	</select>
	
	<select id="getProductListingTotal" resultType="java.lang.Integer">
		SELECT COUNT(1) 
		FROM goods_lot_info 
		WHERE del_flag = 0
		AND state = 'selling'
		<if test="dto.startTime != null">
			AND create_time >= #{dto.startTime}
		</if>
		<if test="dto.endTime != null">
			AND create_time &lt;= #{dto.endTime}
		</if>
	</select>
	
	<select id="getProductListingByDate" resultType="java.util.Map">
		SELECT 
			DATE_FORMAT(create_time, '%Y-%m-%d') AS listingDate,
			COUNT(1) AS count
		FROM goods_lot_info 
		WHERE del_flag = 0
		AND state = 'selling'
		<if test="dto.startTime != null">
			AND create_time >= #{dto.startTime}
		</if>
		<if test="dto.endTime != null">
			AND create_time &lt;= #{dto.endTime}
		</if>
		GROUP BY DATE_FORMAT(create_time, '%Y-%m-%d')
		ORDER BY listingDate
	</select>

    <!-- 获取所有地区列表 -->
    <select id="getRegionList" resultType="com.yts.yyt.goods.api.vo.RegionStatisticVO">
        SELECT DISTINCT
            province AS regionName
        FROM 
            city_partner_area
        WHERE 
            del_flag = '0' 
            AND province IS NOT NULL
        ORDER BY 
            province
    </select>
    
    <!-- 获取地区合伙人数量 -->
    <select id="getRegionPartnerCount" resultType="com.yts.yyt.goods.api.vo.RegionStatisticVO">
        SELECT
            a.province AS regionName,
            COUNT(DISTINCT p.id) AS partnerCount
        FROM
            city_partner_area a
        INNER JOIN
            city_partner p ON p.region = a.id AND p.del_flag = '0'
            <if test="dto.startTime != null">
                AND p.create_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null">
                AND p.create_time &lt;= #{dto.endTime}
            </if>
        WHERE
            a.del_flag = '0'
        GROUP BY
            a.province
    </select>
    
    <!-- 获取地区商家数量 -->
    <select id="getRegionMerchantCount" resultType="com.yts.yyt.goods.api.vo.RegionStatisticVO">
        SELECT
            m.province AS regionName,
            COUNT(DISTINCT m.id) AS merchantCount
        FROM
            merchant_info m
        WHERE
            m.del_flag = '0'
            AND m.province IS NOT NULL
            <if test="dto.startTime != null">
                AND m.create_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null">
                AND m.create_time &lt;= #{dto.endTime}
            </if>
        GROUP BY
            m.province
    </select>
    
    <!-- 获取地区商品数量（鉴定成功和公示中） -->
    <select id="getRegionGoodsCount" resultType="com.yts.yyt.goods.api.vo.RegionStatisticVO">
        SELECT
            mi.province AS regionName,
            SUM(CASE WHEN g.appraisal_state = 1 THEN 1 ELSE 0 END) AS prePublicityCount,
            SUM(CASE WHEN g.state = 'publicity' THEN 1 ELSE 0 END) AS publicityCount
        FROM
            store_goods_info g
        JOIN 
            merchant_info mi ON g.merchant_id = mi.id AND mi.del_flag = '0'
        WHERE 
            g.del_flag = '0'
            AND mi.province IS NOT NULL
            <if test="dto.startTime != null">
                AND g.create_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null">
                AND g.create_time &lt;= #{dto.endTime}
            </if>
        GROUP BY
            mi.province
    </select>
    
    <!-- 获取地区销售数据 -->
    <select id="getRegionSalesData" resultType="com.yts.yyt.goods.api.vo.RegionStatisticVO">
        SELECT
            mi.province AS regionName,
            COUNT(o.id) AS soldCount,
            COALESCE(SUM(o.amount - IFNULL(c.coupon_amount, 0)), 0) AS salesAmount
        FROM
            merchant_info mi
        LEFT JOIN goods_lot_info gl ON gl.merchant_id = mi.id AND gl.del_flag = '0'
        LEFT JOIN order_info o ON o.lot_id = gl.id 
            AND o.status = 'first_launch_completed' 
            AND o.del_flag = '0'
            <if test="dto.startTime != null">
                AND o.create_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null">
                AND o.create_time &lt;= #{dto.endTime}
            </if>
        LEFT JOIN coupon_usage c ON o.id = c.order_id AND c.status in (2,5,6) AND c.del_flag = '0'
        WHERE
            mi.del_flag = '0'
            AND mi.province IS NOT NULL
        GROUP BY
            mi.province
    </select>
	<select id="getAppraisalSuccessCount" resultType="java.lang.Long">
		SELECT
			COUNT(1)
		FROM
			store_goods_info
		WHERE
			del_flag = 0
			AND appraisal_state = 1
			<if test="dto.startTime != null">
				AND create_time >= #{dto.startTime}
			</if>
			<if test="dto.endTime != null">
				AND create_time &lt;= #{dto.endTime}
			</if>
	</select>
	<!-- 数据分析 结束 -->

    <select id="queryEsModelByIds" parameterType="java.util.List" resultType="com.yts.yyt.common.es.model.StoreGoodsModel">
        select a.id,
               a.sale_price,
               a.name,
               a.main_image,
               a.state,
               a.goods_era,
               a.merchant_id,
               a.goods_no,
               a.goods_condition,
               m.shop_name,
               m.benefit_level,
               m.shop_avatar shopAvatar,
               ts.tran_name,
               ts.tran_code,
               ts.tran_icon,
               a.`sort`,
               a.create_time,
               a.update_time,
               a.publicity_start_time,
               a.publicity_end_time
        from store_goods_info a
        left join merchant_info m on a.merchant_id = m.id
        left join transaction_setting ts on m.benefit_level = ts.tran_code
        where a.id in
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and a.`state` = 'publicity' and a.`del_flag` = '0'
    </select>
</mapper>

