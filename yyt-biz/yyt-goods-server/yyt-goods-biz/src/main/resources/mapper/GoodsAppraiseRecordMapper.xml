<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yts.yyt.goods.mapper.GoodsAppraiseRecordMapper">

    <resultMap type="com.yts.yyt.goods.api.entity.GoodsAppraiseRecord" id="GoodsAppraiseRecordMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="goodsId" column="goods_id" jdbcType="BIGINT"/>
        <result property="appraisePrice" column="appraise_price" jdbcType="DECIMAL"/>
        <result property="appraiseTime" column="appraise_time" jdbcType="TIMESTAMP"/>
        <result property="appraisalExpertNum" column="appraisal_expert_num" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="VARCHAR"/>
    </resultMap>

</mapper>

