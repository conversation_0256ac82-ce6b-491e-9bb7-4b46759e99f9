<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yts.yyt.goods.mapper.TYjGoodsAgeMapper">

    <resultMap type="com.yts.yyt.goods.api.entity.TYjGoodsAge" id="TYjGoodsAgeMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="ageName" column="age_name" jdbcType="VARCHAR"/>
        <result property="pId" column="p_id" jdbcType="INTEGER"/>
        <result property="isOld" column="is_old" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="modifiedTime" column="modified_time" jdbcType="TIMESTAMP"/>
        <result property="goodsAgeDesc" column="goods_age_desc" jdbcType="VARCHAR"/>
        <result property="isGuanWare" column="is_guan_ware" jdbcType="INTEGER"/>
        <result property="isAccident" column="is_accident" jdbcType="INTEGER"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createById" column="create_by_id" jdbcType="INTEGER"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateById" column="update_by_id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="VARCHAR"/>
        <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
    </resultMap>

</mapper>

