<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yts.yyt.goods.mapper.GoodsStatsMapper">

    <!-- 获取商品统计总数 -->
    <select id="getTotalCount" resultType="java.lang.Integer">
        SELECT 
            GREATEST(IFNULL(SUM(CASE WHEN operate_type = 1 THEN 1 ELSE -1 END), 0), 0)
        FROM goods_stats
        WHERE lot_id = #{lotId}
        AND type = #{type}
        AND del_flag = '0'
    </select>

    <!-- 获取商品增量统计数 -->
    <select id="getIncrementalCount" resultType="java.lang.Integer">
        SELECT 
            GREATEST(IFNULL(SUM(CASE WHEN operate_type = 1 THEN 1 ELSE -1 END), 0), 0)
        FROM goods_stats
        WHERE lot_id = #{lotId}
        AND type = #{type}
        AND access_time >= #{startTime}
        AND access_time <![CDATA[<=]]> #{endTime}
        AND del_flag = '0'
    </select>

</mapper> 