package com.yts.yyt.admin.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

/**
 * 前端上传文件返回对象
 */
@Data
@AllArgsConstructor
@Schema(description = "前端上传文件返回对象")
public class UploadFileVO implements Serializable {

    /**
     * 存储同名称
     */
    @Schema(description = "存储同名称")
    private String bucketName;

    /**
     * 文件名称
     */
    @Schema(description = "文件名称")
    private String fileName;

    /**
     * 文件路径
     */
    @Schema(description = "文件路径")
    private String url;

//    /**
//     * OSS文件全路径
//     */
//    @Schema(description = "OSS文件全路径")
//    private String fullOssUrl;
//
//	public UploadFileVO(String bucketName, String fileName, String url) {
//		this.bucketName = bucketName;
//		this.fileName = fileName;
//		this.url = url;
//	}
}
