package com.yts.yyt.admin.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 文件数据
 */
@Data
@Schema(description = "文件")
public class SysFileVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @Schema(description = "文件编号")
    private Long id;

    /**
     * 文件名
     */
    @Schema(description = "文件名")
    private String fileName;

    /**
     * 原文件名
     */
    @Schema(description = "原始文件名")
    private String original;

    /**
     * 容器名称
     */
    @Schema(description = "存储桶名称")
    private String bucketName;

    /***
     * 文件夹
     */
    @Schema(description = "文件夹")
    private String dir;

    /**
     * 文件大小
     */
    @Schema(description = "文件大小")
    private Long fileSize;

    /**
     * 文件hash
     */
    @Schema(description = "文件hash")
    private String hash;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
}
