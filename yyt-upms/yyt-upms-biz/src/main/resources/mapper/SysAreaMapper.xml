<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yts.yyt.admin.mapper.SysAreaMapper">

  <resultMap id="sysAreaMap" type="com.yts.yyt.admin.api.entity.SysAreaEntity">
        <id property="id" column="id"/>
        <result property="pid" column="pid"/>
        <result property="name" column="name"/>
        <result property="letter" column="letter"/>
        <result property="adcode" column="adcode"/>
        <result property="location" column="location"/>
        <result property="areaSort" column="area_sort"/>
        <result property="areaStatus" column="area_status"/>
        <result property="areaType" column="area_type"/>
        <result property="hot" column="hot"/>
        <result property="cityCode" column="city_code"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
  </resultMap>
</mapper>
