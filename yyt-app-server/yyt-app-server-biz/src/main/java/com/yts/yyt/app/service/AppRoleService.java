/*
 *    Copyright (c) 2018-2025, yyt All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: yyt
 */

package com.yts.yyt.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yts.yyt.app.api.entity.AppRole;
import com.yts.yyt.app.api.vo.AppRoleExcelVO;
import com.yts.yyt.common.core.util.R;
import org.springframework.validation.BindingResult;

import java.util.List;

/**
 * app角色表
 *
 * <AUTHOR>
 * @date 2022-12-07 09:52:03
 */
public interface AppRoleService extends IService<AppRole> {

	List<AppRole> findRolesByUserId(Long userId);

	/**
	 * 删除用户的同时，把role_menu关系删除
	 * @param ids RoleIds
	 */
	Boolean deleteRoleByIds(Long[] ids);

	R importRole(List<AppRoleExcelVO> excelVOList, BindingResult bindingResult);

}
