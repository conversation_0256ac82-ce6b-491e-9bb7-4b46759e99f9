package com.yts.yyt.pay.handler.refund;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayTradeRefundModel;
import com.alipay.api.request.AlipayTradeRefundRequest;
import com.alipay.api.response.AlipayTradeRefundResponse;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yts.yyt.pay.entity.PayChannel;
import com.yts.yyt.pay.entity.PayRefundOrder;
import com.yts.yyt.pay.entity.PayTradeOrder;
import com.yts.yyt.pay.mapper.PayChannelMapper;
import com.yts.yyt.pay.mapper.PayRefundOrderMapper;
import com.yts.yyt.pay.utils.ChannelPayApiConfigKit;
import com.yts.yyt.pay.utils.PayChannelNameEnum;
import com.yts.yyt.pay.utils.RefundStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;

/**
 * <p>2025-01-14</p>
 * <p>支付宝退款处理.</p>
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service("ALIPAY_APP_PAY_REFUND")
public class AlipayAppPayRefundHandler extends AbstractPayRefundHandler {

    private final PayRefundOrderMapper refundOrderMapper;

    private final PayChannelMapper channelMapper;

    @Override
    public PayChannel preparePayParams () {
        PayChannel channel = channelMapper.selectOne(
                Wrappers.<PayChannel>lambdaQuery().eq(PayChannel::getChannelId, PayChannelNameEnum.ALIPAY_APP_PAY.name()));

        if (channel == null) {
            throw new IllegalArgumentException("支付宝APP支付渠道配置为空");
        }
        return channel;
    }

    @Override
    public PayRefundOrder createPayRefundOrder (PayRefundOrder refundOrder, PayTradeOrder tradeOrder) {
        refundOrder.setChannelId(PayChannelNameEnum.ALIPAY_APP_PAY.getName());
        return super.createPayRefundOrder(refundOrder, tradeOrder);
    }

    @Override
    public Object refund (PayRefundOrder refundOrder, PayTradeOrder tradeOrder) {
        PayChannel channel = ChannelPayApiConfigKit.get();
        JSONObject params = JSONUtil.parseObj(channel.getParam());

        String serverUrl = params.getStr("serverUrl");
        String privateKey = params.getStr("privateKey");
        String alipayPublicKey = params.getStr("alipayPublicKey");
        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setServerUrl(serverUrl);
        alipayConfig.setAppId(channel.getAppId());
        alipayConfig.setPrivateKey(privateKey);
        alipayConfig.setFormat("json");
        alipayConfig.setAlipayPublicKey(alipayPublicKey);
        alipayConfig.setCharset("UTF-8");
        alipayConfig.setSignType("RSA2");

        AlipayClient alipayClient;
        try {
            AlipayTradeRefundRequest request = new AlipayTradeRefundRequest();
            AlipayTradeRefundModel model = new AlipayTradeRefundModel();

            // 设置商户订单号
            model.setOutTradeNo(tradeOrder.getChannelOrderNo());

            // 设置退款金额
            model.setRefundAmount(new BigDecimal(tradeOrder.getAmount()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());

            // 设置退款原因说明
            model.setRefundReason("正常退款");

            // 设置退款请求号
            model.setOutRequestNo(String.valueOf(refundOrder.getRefundOrderId()));

            request.setBizModel(model);
            alipayClient = new DefaultAlipayClient(alipayConfig);
            alipayClient.sdkExecute(request);
        } catch (AlipayApiException e) {
            log.error("【客户端初始化失败】", e);
            throw new RuntimeException(e);
        }
        return null;
    }

    @Override
    public void updateOrder ( PayTradeOrder tradeOrder, String refundOrderId) {
        super.updateOrder(tradeOrder, refundOrderId);
    }

    @Override
    public void updateRefundOrder (Object obj, PayRefundOrder refundOrder) {
        AlipayTradeRefundResponse refundResponse = (AlipayTradeRefundResponse) obj;
        // 更新退款单状态成功
        refundOrder.setStatus(RefundStatusEnum.RSE2.getStatus());
        refundOrder.setRefundSuccTime(LocalDateTime.now());
        super.updateRefundOrder(obj, refundOrder);
    }
}
