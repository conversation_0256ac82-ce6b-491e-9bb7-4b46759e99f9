package com.yts.yyt.pay.tools;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.service.refund.RefundService;
import com.wechat.pay.java.service.refund.model.AmountReq;
import com.wechat.pay.java.service.refund.model.CreateRequest;
import com.wechat.pay.java.service.refund.model.Refund;
import com.wechat.pay.java.service.refund.model.Status;
import com.yts.yyt.pay.entity.PayChannel;
import com.yts.yyt.pay.entity.PayRefundOrder;
import com.yts.yyt.pay.entity.PayTradeOrder;
import com.yts.yyt.pay.utils.RefundStatusEnum;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>2025-01-17</p>
 * <p>微信支付工具.</p>
 *
 * <AUTHOR>
 */
@Slf4j
@UtilityClass
public class WxPayTool {

    /**
     * 退款
     * @param channel     支付渠道
     * @param refundOrder 退款订单
     * @param tradeOrder  交易订单
     * @return Refund
     */
    public Refund refund (PayChannel channel, PayRefundOrder refundOrder, PayTradeOrder tradeOrder) {
        JSONObject params = JSONUtil.parseObj(channel.getParam());
        RefundService service = new RefundService .Builder().config(new RSAAutoCertificateConfig.Builder()
                .merchantId(channel.getChannelMchId())
                .privateKeyFromPath(params.getStr("privateKeyFromPath"))
                .merchantSerialNumber(params.getStr("merchantSerialNumber"))
                .apiV3Key(params.getStr("apiV3Key"))
                .build()).build();

        CreateRequest request = new CreateRequest();
        long Amount = new BigDecimal(tradeOrder.getAmount()).longValue();
        AmountReq amountReq = new AmountReq();
        amountReq.setCurrency("CNY");
        amountReq.setRefund(Amount);
        amountReq.setTotal(Amount);
        request.setAmount(amountReq);
        request.setOutTradeNo(String.valueOf(tradeOrder.getOrderId()));
        request.setOutRefundNo(String.valueOf(refundOrder.getRefundOrderId()));
        request.setNotifyUrl(params.getStr("refundNotifyUrl"));

        try{
            Refund refund = service.create(request);
            log.info("[微信退款]:{}", JSONUtil.toJsonStr(refund));
            return refund;
        } catch (Exception e) {

            // 使用正则表达式提取 message 的值
            String regex = "\"message\":\"([^\"]+)\"";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(e.getMessage());

            String message;
            if (matcher.find()) {
                message = matcher.group(1);
            } else {
                message = "未知异常";
            }
            throw new RuntimeException(message);
        }
    }


    /**
     * 退款状态转换
     *
     * @param refund 退款响应实体
     * @return RefundStatusEnum
     */
    public RefundStatusEnum conversion (Refund refund) {
        if (Status.SUCCESS.equals(refund.getStatus())) {
            return RefundStatusEnum.RSE2;
        }

        if (Status.CLOSED.equals(refund.getStatus())) {
            return RefundStatusEnum.RSE4;
        }

        if (Status.PROCESSING.equals(refund.getStatus())) {
            return RefundStatusEnum.RSE1;
        }
        return RefundStatusEnum.RSE3;
    }
}
