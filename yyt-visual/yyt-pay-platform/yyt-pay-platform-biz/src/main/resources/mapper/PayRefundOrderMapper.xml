<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright (c) 2018-2025, yyt All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: yyt
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yts.yyt.pay.mapper.PayRefundOrderMapper">

	<resultMap id="payRefundOrderMap" type="com.yts.yyt.pay.entity.PayRefundOrder">
		<id property="refundOrderId" column="refund_]order_id"/>
		<result property="payOrderId" column="pay_order_id"/>
		<result property="channelPayOrderNo" column="channel_pay_order_no"/>
		<result property="mchId" column="mch_id"/>
		<result property="mchRefundNo" column="mch_refund_no"/>
		<result property="channelId" column="channel_id"/>
		<result property="payAmount" column="pay_amount"/>
		<result property="refundAmount" column="refund_amount"/>
		<result property="currency" column="currency"/>
		<result property="status" column="status"/>
		<result property="result" column="result"/>
		<result property="clientIp" column="client_ip"/>
		<result property="device" column="device"/>
		<result property="remark" column="remark"/>
		<result property="channelUser" column="channel_user"/>
		<result property="username" column="username"/>
		<result property="channelMchId" column="channel_mch_id"/>
		<result property="channelOrderNo" column="channel_order_no"/>
		<result property="channelErrCode" column="channel_err_code"/>
		<result property="channelErrMsg" column="channel_err_msg"/>
		<result property="extra" column="extra"/>
		<result property="notifyurl" column="notifyUrl"/>
		<result property="param1" column="param1"/>
		<result property="param2" column="param2"/>
		<result property="expireTime" column="expire_time"/>
		<result property="refundSuccTime" column="refund_succ_time"/>
		<result property="delFlag" column="del_flag"/>
		<result property="createTime" column="create_time"/>
		<result property="updateTime" column="update_time"/>
		<result property="tenantId" column="tenant_id"/>
	</resultMap>
</mapper>
