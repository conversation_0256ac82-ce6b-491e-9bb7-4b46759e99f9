<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~    Copyright (c) 2018-2025, yyt All rights reserved.
  ~
  ~ Redistribution and use in source and binary forms, with or without
  ~ modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~ this list of conditions and the following disclaimer.
  ~ Redistributions in binary form must reproduce the above copyright
  ~ notice, this list of conditions and the following disclaimer in the
  ~ documentation and/or other materials provided with the distribution.
  ~ Neither the name of the pig4cloud.com developer nor the names of its
  ~ contributors may be used to endorse or promote products derived from
  ~ this software without specific prior written permission.
  ~ Author: yyt
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yts.yyt.codegen.mapper.GenFormConfMapper">

	<resultMap id="genRecordMap" type="com.yts.yyt.codegen.entity.GenFormConf">
		<id property="id" column="id"/>
		<result property="tableName" column="table_name"/>
		<result property="formInfo" column="form_info"/>
		<result property="createTime" column="create_time"/>
		<result property="updateTime" column="update_time"/>
		<result property="delFlag" column="del_flag"/>
		<result property="tenantId" column="tenant_id"/>
	</resultMap>
</mapper>
