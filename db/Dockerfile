FROM registry.cn-hangzhou.aliyuncs.com/dockerhub_mirror/mysql-server:8.0.32

MAINTAINER yyt(<EMAIL>)

ENV TZ=Asia/Shanghai
ENV LANG C.UTF-8

RUN ln -sf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

COPY ./1schema.sql /docker-entrypoint-initdb.d

COPY ./2yytx.sql /docker-entrypoint-initdb.d

COPY ./3yytx_flow.sql /docker-entrypoint-initdb.d

COPY ./4yytx_job.sql /docker-entrypoint-initdb.d

COPY ./5yytx_mp.sql /docker-entrypoint-initdb.d

COPY ./6yytx_config.sql /docker-entrypoint-initdb.d

COPY ./7yytx_pay.sql /docker-entrypoint-initdb.d

COPY ./8yytx_codegen.sql /docker-entrypoint-initdb.d

COPY ./99yytx_bi.sql /docker-entrypoint-initdb.d

COPY ./999yytx_app.sql /docker-entrypoint-initdb.d
