<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright (c) 2018-2025, yyt All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: yyt
  ~
  -->
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.yts</groupId>
    <artifactId>yyt</artifactId>
    <version>5.7.0</version>
    <name>${project.artifactId}</name>
    <packaging>pom</packaging>
    <organization>
        <name>pig4cloud</name>
        <url>https://www.pig4cloud.com</url>
    </organization>

    <properties>
        <spring-boot.version>3.3.6</spring-boot.version>
        <spring-cloud.version>2023.0.4</spring-cloud.version>
        <spring-cloud-alibaba.version>2023.0.3.2</spring-cloud-alibaba.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <spring-boot-admin.version>3.3.5</spring-boot-admin.version>
        <spring.authorization.version>1.4.0</spring.authorization.version>
        <maven.compiler.version>3.11.0</maven.compiler.version>
        <spring.checkstyle.version>0.0.39</spring.checkstyle.version>
        <git.commit.version>4.9.9</git.commit.version>
        <captcha.version>1.3.0</captcha.version>
        <captcha.image.version>2.2.3</captcha.image.version>
        <cas.sdk.version>3.6.4</cas.sdk.version>
        <flowable.version>7.0.0</flowable.version>
        <dingtalk.old.version>2.0.0</dingtalk.old.version>
        <swagger.fox.version>3.0.0</swagger.fox.version>
        <knife4j.version>3.0.5</knife4j.version>
        <jasypt.version>3.0.5</jasypt.version>
        <ttl.version>2.12.6</ttl.version>
        <jaxb.version>2.3.5</jaxb.version>
        <jimu.version>1.8.1</jimu.version>
        <aws.version>1.12.261</aws.version>
        <xxl.job.version>2.5.0</xxl.job.version>
        <docker.plugin.version>0.33.0</docker.plugin.version>
        <!--  默认忽略docker构建 -->
        <docker.skip>true</docker.skip>
    </properties>

    <dependencies>
        <!--配置文件处理器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <!--jasypt配置文件加解密-->
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
            <version>${jasypt.version}</version>
        </dependency>
        <!--监控-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <!--监控客户端-->
        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-client</artifactId>
            <version>${spring-boot-admin.version}</version>
        </dependency>
        <!--Lombok-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- JAVA 17 -->
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-impl</artifactId>
            <version>${jaxb.version}</version>
        </dependency>
        <!--测试依赖-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <modules>
        <module>yyt-register</module>
        <module>yyt-gateway</module>
        <module>yyt-auth</module>
        <module>yyt-upms</module>
        <module>yyt-common</module>
        <module>yyt-flow</module>
        <module>yyt-visual</module>
        <module>yyt-app-server</module>
        <module>yyt-biz</module>
        <module>yyt-common/yyt-common-udesk</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <!--yyt 公共版本定义-->
            <dependency>
                <groupId>com.yts</groupId>
                <artifactId>yyt-common-bom</artifactId>
                <version>${project.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--spring boot 公共版本定义-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--spring cloud 公共版本定义-->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--spring cloud alibaba-->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--web 模块-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring-boot.version}</version>
                <exclusions>
                    <!--排除tomcat依赖-->
                    <exclusion>
                        <artifactId>spring-boot-starter-tomcat</artifactId>
                        <groupId>org.springframework.boot</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <finalName>${project.name}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <!--spring boot 默认插件-->
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <!--maven  docker 打包插件 -->
                <plugin>
                    <groupId>io.fabric8</groupId>
                    <artifactId>docker-maven-plugin</artifactId>
                    <version>${docker.plugin.version}</version>
                    <configuration>
                        <dockerHost>${docker.host}</dockerHost>
                        <registry>${docker.registry}</registry>
                        <authConfig>
                            <push>
                                <username>${docker.username}</username>
                                <password>${docker.password}</password>
                            </push>
                        </authConfig>
                        <images>
                            <image>
                                <name>${docker.registry}/${docker.namespace}/${project.name}:5.7.1</name>
                                <build>
                                    <dockerFile>${project.basedir}/Dockerfile</dockerFile>
                                </build>
                            </image>
                        </images>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <!--代码格式插件，默认使用spring 规则-->
            <plugin>
                <groupId>io.spring.javaformat</groupId>
                <artifactId>spring-javaformat-maven-plugin</artifactId>
                <version>${spring.checkstyle.version}</version>
            </plugin>
            <!--代码编译指定版本插件-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.compiler.version}</version>
                <configuration>
                    <target>${maven.compiler.target}</target>
                    <source>${maven.compiler.source}</source>
                    <encoding>UTF-8</encoding>
                    <parameters>true</parameters>
                </configuration>
            </plugin>
            <!--打包关联最新 git commit 信息插件-->
            <plugin>
                <groupId>io.github.git-commit-id</groupId>
                <artifactId>git-commit-id-maven-plugin</artifactId>
                <version>${git.commit.version}</version>
                <executions>
                    <execution>
                        <id>get-the-git-infos</id>
                        <goals>
                            <goal>revision</goal>
                        </goals>
                        <phase>validate</phase> <!-- 或者使用 prepare-package -->
                    </execution>
                </executions>
                <configuration>
                    <failOnNoGitDirectory>false</failOnNoGitDirectory>
                    <generateGitPropertiesFile>true</generateGitPropertiesFile>
                    <!--因为项目定制了jackson的日期时间序列化/反序列化格式，因此这里要进行配置,不然通过management.info.git.mode=full进行完整git信息监控时会存在问题-->
                    <dateFormat>yyyy-MM-dd HH:mm:ss</dateFormat>
                    <includeOnlyProperties>
                        <includeOnlyProperty>^git.build.(time|version)$</includeOnlyProperty>
                        <includeOnlyProperty>^git.commit.(id|message|time).*$</includeOnlyProperty>
                    </includeOnlyProperties>
                </configuration>
            </plugin>
            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>local</id>
            <activation>
                <!-- 默认环境 -->
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>local</profiles.active>
<!--                <nacos.ip>**************:8848</nacos.ip>-->
                <nacos.ip>127.0.0.1:8848</nacos.ip>
                <nacos.username>nacos</nacos.username>
                <nacos.password>123456</nacos.password>
<!--                <nacos.password>NiUFXuXI1</nacos.password>-->
                <mysql.ip>**************:43306</mysql.ip>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <activation>
                <!-- 默认环境 -->
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>dev</profiles.active>
                <nacos.ip>***********:8848</nacos.ip>
                <nacos.username>nacos</nacos.username>
                <nacos.password>qeF4sGiuJ</nacos.password>
                <mysql.ip>***********:3306</mysql.ip>
            </properties>
        </profile>
        <profile>
            <id>sit</id>
            <activation>
                <!-- 默认环境 -->
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>sit</profiles.active>
                <nacos.ip>***********:8848</nacos.ip>
                <nacos.username>nacos</nacos.username>
                <nacos.password>NiUFXuXI1</nacos.password>
                <!--docker配置-->
                <docker.registry>harbor.boyangwenwu.com</docker.registry>
                <docker.host>tcp://***********:2375</docker.host>
                <docker.namespace>yyt</docker.namespace>
                <docker.username>developers</docker.username>
                <docker.password>h2SkhqejghlwCm3</docker.password>

                <!-- -->
                <mysql.ip>***********:3306</mysql.ip>
            </properties>
        </profile>
        <profile>
            <id>uat</id>
            <activation>
                <!-- 默认环境 -->
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>uat</profiles.active>
                <nacos.ip>localhost:8848</nacos.ip>
                <nacos.username>nacos</nacos.username>
                <nacos.password>NiUFXuXI1</nacos.password>
                <mysql.ip>127.0.0.1:3306</mysql.ip>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <activation>
                <!-- 默认环境 -->
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>prod</profiles.active>
                <nacos.ip>***********:8848,***********:8848,***********:8848</nacos.ip>
                <nacos.username>nacos</nacos.username>
                <nacos.password>NiUFXuXI1</nacos.password>
            </properties>
        </profile>
    </profiles>

    <repositories>
        <repository>
            <id>central</id>
            <url>https://maven.aliyun.com/repository/central</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>

        <repository>
            <id>yyt-nexus</id>
            <name>yyt</name>
            <url>https://nexus.byzxd.com:8443/repository/yyt/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

</project>
