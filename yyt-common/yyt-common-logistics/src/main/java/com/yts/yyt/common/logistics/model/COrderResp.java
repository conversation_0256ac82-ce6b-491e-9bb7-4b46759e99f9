package com.yts.yyt.common.logistics.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>2025-02-24</p>
 * <p>C端寄件下单.</p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class COrderResp extends BaseModel<COrderResp> implements Serializable {

    private String taskId;

    private String orderId;

    private String kuaidinum;

    private String pollToken;
}
