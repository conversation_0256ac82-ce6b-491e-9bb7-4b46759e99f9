package com.yts.yyt.common.logistics;

import com.kuaidi100.sdk.request.corder.COrderReq;
import com.yts.yyt.common.logistics.model.*;

/**
 * <p>2025-01-07</p>
 * <p>物流服务.</p>
 *
 * <AUTHOR>
 */
public interface LogisticsService {

    /**
     * 查询快递信息
     * 注意：请控制每一单查询频率至少在半小时以上，否则会造成锁单。
     * <p><a href="https://api.kuaidi100.com/document/5f0ffb5ebc8da837cbd8aefc">查询文档</a></p>
     * @param com   快递公司
     * @param num   快递单号
     * @param phone 手机号
     * @return {
     *     "message": "ok",
     *     "nu": "JT0004301991791",
     *     "ischeck": "0",
     *     "com": "jtexpress",
     *     "status": "200",
     *     "data": [
     *         {
     *             "time": "2021-12-15 17:19:28",
     *             "context": "【杭州市】您的包裹已存放至【驿站】，记得早点来取它回家！",
     *             "ftime": "2021-12-15 17:19:28",
     *             "areaCode": "CN330102000000",//本数据元对应的行政区域编码，resultv2=1或者resultv2=4才会展示
     *             "areaName": "浙江,杭州市,上城区",//本数据元对应的行政区域名称，resultv2=1或者resultv2=4才会展示
     *             "status": "投柜或驿站",//本数据元对应的物流状态名称或者高级物流状态名称，resultv2=1或者resultv2=4才会展示
     *             "location": "浙江省 杭州市 上城区", //本数据元对应的快件当前地点，resultv2=4才会展示
     *             "areaCenter": "120.184349,30.25446", //本数据元对应的行政区域经纬度，resultv2=4才会展示
     *             "areaPinYin": "shang cheng qu",//本数据元对应的行政区域拼音，resultv2=4才会展示
     *             "statusCode": "501"//本数据元对应的高级物流状态值，resultv2=4才会展示
     *         },
     *         {
     *             "time": "2021-12-15 14:17:31",
     *             "context": "【杭州市】【杭州网点】的极兔小哥正在派件",
     *             "ftime": "2021-12-15 14:17:31",
     *             "areaCode": "CN330102000000",
     *             "areaName": "浙江,杭州市,上城区",
     *             "status": "派件",
     *             "location": "浙江省 杭州市 上城区",
     *             "areaCenter": "120.184349,30.25446",
     *             "areaPinYin": "shang cheng qu",
     *             "statusCode": "5"
     *         },
     *         {
     *             "time": "2021-12-15 13:58:18",
     *             "context": "【杭州市】 快件到达【杭州网点】",
     *             "ftime": "2021-12-15 13:58:18",
     *             "areaCode": "CN330102000000",
     *             "areaName": "浙江,杭州市,上城区",
     *             "status": "在途",
     *             "location": "浙江省 杭州市 上城区",
     *             "areaCenter": "120.184349,30.25446",
     *             "areaPinYin": "shang cheng qu",
     *             "statusCode": "0"
     *         },
     *         {
     *             "time": "2021-12-15 04:11:20",
     *             "context": "【杭州市】快件离开【杭州转运中心】已发往【杭州江干四季青网点】",
     *             "ftime": "2021-12-15 04:11:20",
     *             "areaCode": "CN330109000000",
     *             "areaName": "浙江,杭州市,萧山区",
     *             "status": "干线",
     *             "location": "浙江省 杭州市 萧山区",
     *             "areaCenter": "120.493286,30.28333",
     *             "areaPinYin": "xiao shan qu",
     *             "statusCode": "1002"
     *         },
     *         {
     *             "time": "2021-12-15 02:09:52",
     *             "context": "【杭州市】 快件到达【杭州转运中心】",
     *             "ftime": "2021-12-15 02:09:52",
     *             "areaCode": "CN330109000000",
     *             "areaName": "浙江,杭州市,萧山区",
     *             "status": "干线",
     *             "location": "浙江省 杭州市 萧山区",
     *             "areaCenter": "120.493286,30.28333",
     *             "areaPinYin": "xiao shan qu",
     *             "statusCode": "1002"
     *         },
     *         {
     *             "time": "2021-12-14 21:08:34",
     *             "context": "【上海市】快件离开【上海浦西转运中心】已发往【杭州转运中心】",
     *             "ftime": "2021-12-14 21:08:34",
     *             "areaCode": "CN310118000000",
     *             "areaName": "上海,上海,青浦区",
     *             "status": "干线",
     *             "location": "上海 上海市 青浦区",
     *             "areaCenter": "121.124178,31.150681",
     *             "areaPinYin": "qing pu qu",
     *             "statusCode": "1002"
     *         },
     *         {
     *             "time": "2021-12-14 20:54:22",
     *             "context": "【上海市】 快件到达【上海浦西转运中心】",
     *             "ftime": "2021-12-14 20:54:22",
     *             "areaCode": "CN310118000000",
     *             "areaName": "上海,上海,青浦区",
     *             "status": "干线",
     *             "location": "上海 上海市 青浦区",
     *             "areaCenter": "121.124178,31.150681",
     *             "areaPinYin": "qing pu qu",
     *             "statusCode": "1002"
     *         },
     *         {
     *             "time": "2021-12-14 17:25:58",
     *             "context": "【上海市】快件离开【上海杨浦黄兴路网点】已发往【上海浦西转运中心】",
     *             "ftime": "2021-12-14 17:25:58",
     *             "areaCode": "CN310110000000",
     *             "areaName": "上海,上海,杨浦区",
     *             "status": "干线",
     *             "location": "上海 上海市 杨浦区",
     *             "areaCenter": "121.526077,31.259541",
     *             "areaPinYin": "yang pu qu",
     *             "statusCode": "1002"
     *         },
     *         {
     *             "time": "2021-12-14 09:03:58",
     *             "context": "【上海市】【上海杨浦黄兴路网点】已取件。",
     *             "ftime": "2021-12-14 09:03:58",
     *             "areaCode": "CN310110000000",
     *             "areaName": "上海,上海,杨浦区",
     *             "status": "揽收",
     *             "location": "上海 上海市 杨浦区",
     *             "areaCenter": "121.526077,31.259541",
     *             "areaPinYin": "yang pu qu",
     *             "statusCode": "1"
     *         }
     *     ],
     *     "state": "5",
     *     "condition": "00",
     *     "routeInfo": {
     *         "from": {
     *             "number": "CN310110000000",
     *             "name": "上海,上海,杨浦区"
     *         },//本数据元对应的出发地城市信息，resultv2=4才会展示
     *         "cur": {
     *             "number": "CN330102000000",
     *             "name": "浙江,杭州市,上城区"
     *         },//本数据元对应的当前城市信息，resultv2=4才会展示
     *         "to": null
     *     },//本数据元对应的目的地城市信息，resultv2=4才会展示
     *     "isLoop": false
     * }
     * }
     */
    String query(String com, String num, String phone);

    /**
     * 智能识别订单号
     *
     * @param num 订单号
     * @return
     * <p>
     * [{
     * 	"lengthPre": 15,
     * 	"comCode": "yuantong",
     * 	"name": "圆通速递"
     * }]
     * </p>
     * <p>
     *     错误示例
     *      {
     *          "returnCode": "701",
     *          "message": "key缺失",
     *          "result": false
     *      }
     * </p>
     *
     */
    ExpressCompany autonumber(String num);

    /**
     * 订阅物流
     * <p>
     * <a href="https://api.kuaidi100.com/document/5f0ffa8f2977d50a94e1023c">文档</a>
     * @param phone   手机号
     * @param company 物流公司 参数值参考 com.kuaidi100.sdk.contant.CompanyConstant
     * @param number  订单号
     * @return { "result": true, "returnCode": "200", "message": "提交成功" }
     */
    String subscribe(String phone, String company, String number) throws Exception;

    /**
     * C端寄件下单
     *             COrderReq cOrderReq = new COrderReq();
     *             cOrderReq.setKuaidicom(CompanyConstant.SF);
     *             cOrderReq.setSendManName("张三");
     *             cOrderReq.setSendManMobile("12345678910");
     *             cOrderReq.setSendManPrintAddr("西藏日喀则市定日县珠穆朗玛峰");
     *             cOrderReq.setRecManName("李四");
     *             cOrderReq.setRecManMobile("12345678910");
     *             cOrderReq.setRecManPrintAddr("西藏日喀则市定日县珠穆朗玛峰");
     *             cOrderReq.setCallBackUrl("http://www.baidu.com");
     *             cOrderReq.setCargo("文件");
     *             cOrderReq.setRemark("测试下单，待会取消");
     *             cOrderReq.setWeight("1");
     *             cOrderReq.setSalt("123456");
     */
    COrderResp cOrder(COrderReq cOrder) throws Exception;

    /**
     * C端寄件取消
     *
     */
    COrderCancelResp cOrderCancel(String taskId, String orderId, String cancelMsg);

    /**
     * C端寄件价格
     *
     */
    COrderQueryPriceResp cOrderQueryPrice(String sendManPrintAddr, String recManPrintAddr);
}
