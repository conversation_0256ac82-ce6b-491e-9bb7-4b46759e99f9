package com.yts.yyt.common.ess;

import com.yts.yyt.common.ess.model.TemplateModel;

import java.util.List;

/**
 * <p>2025-01-11</p>
 * <p>Ess服务.</p>
 *
 * <AUTHOR>
 */
public interface EssService {

    /**
     * 模板发起.
     *
     * @param ess        对应实现
     * @param model      模板流程参数模型.
     * @return String
     */
    TemplateModel.responseMP templateMP(EssEnum ess, TemplateModel model);

    /**
     * 模板发起.
     *
     * @param ess        对应实现
     * @param model      模板流程参数模型.
     * @return String
     */
    TemplateModel.responseH5 templateH5(EssEnum ess, TemplateModel model,boolean multiSig, String flowId);

    /**
     * 模板发起多签.
     *
     * @param ess        对应实现
     * @param model      模板流程参数模型.
     * @return String
     */
    List<TemplateModel.responseH5> multiSigTemplateH5(EssEnum ess, TemplateModel model,boolean multiSig, String flowId);

    /**
     * 合同下载链接.
     *
     * @param ess         对应实现
     * @param operatorId  电子签操作人编号
     * @param flowId      签约流程编号
     * @return String
     */
    String link(EssEnum ess, String operatorId, String flowId);

    /**
     * 电子签详情.
     *
     * @param ess         对应实现
     * @param operatorId  电子签操作人编号
     * @param flowId      签约流程编号
     * @return List<TemplateModel.responseDetail>
     */
    List<TemplateModel.responseDetail> describeFlowInfo(EssEnum ess, String operatorId, String flowId);
}
