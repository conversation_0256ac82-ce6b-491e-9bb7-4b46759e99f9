package com.yts.yyt.common.ess.handler;

import com.yts.yyt.common.ess.EssEnum;
import com.yts.yyt.common.ess.model.TemplateModel;

import java.util.List;

/**
 * <p>2025-01-11</p>
 * <p>Ess接口.</p>
 *
 * <AUTHOR>
 */
public interface IEssHandler {

    /**
     * 小程序模板发起.
     *
     * @param model      模板流程参数模型.
     * @return String
     */
    TemplateModel.responseMP templateMP (TemplateModel model);

    /**
     * 模板发起.
     *
     * @param model      模板流程参数模型.
     * @return String
     */
    TemplateModel.responseH5 templateH5(TemplateModel model,boolean multiSig, String flowId);

    /**
     * 模板发起多签.
     *
     * @param model      模板流程参数模型.
     * @return String
     */
    List<TemplateModel.responseH5> multiSigTemplateH5(TemplateModel model,boolean multiSig, String flowId);

    /**
     * 合同下载链接.
     *
     * @param operatorId  电子签操作人编号
     * @param flowId      签约流程编号
     * @return String
     */
    String link(String operatorId, String flowId);

    /**
     * 详情信息.
     *
     * @param operatorId 操作人
     * @param flowId    流程id
     * @return List<TemplateModel.responseDetail>
     */
    List<TemplateModel.responseDetail> describeFlowInfo(String operatorId, String flowId);
}
