package com.yts.yyt.common.core.constant;

/**
 * 交易设置，编码常量
 */
public interface TransactionSettingCodeConstants {

    /**
     * 未付款自动关闭时间
     */
    String  UNPAID_AUTO_CLOSE_TIME = "UNPAID_AUTO_CLOSE_TIME";

    /**
     * 发货后自动收货时间
     */
    String AUTO_RECEIVE_TIME = "AUTO_RECEIVE_TIME";

    /**
     * 收货后自动完成时间
     */
    String AUTO_COMPLETE_TIME = "AUTO_COMPLETE_TIME";

    /**
     * 完成后可维权时间
     */
    String POST_COMPLETION_SUPPORT_TIME = "POST_COMPLETION_SUPPORT_TIME";

    /**
     * 提现手续费比率
     */

    String WITHDRAWAL_FEE_RATE = "WITHDRAWAL_FEE_RATE";

    /**
     * 提现方式
     */
    String WITHDRAWAL_METHOD = "WITHDRAWAL_METHOD";

    /**
     * 最低提现额度
     */
    String MIN_WITHDRAWAL_AMOUNT = "MIN_WITHDRAWAL_AMOUNT";

    /**
     * 最高提现额度
     */
    String MAX_WITHDRAWAL_AMOUNT = "MAX_WITHDRAWAL_AMOUNT";

    /**
     * 限额出价，需缴纳保证金不小于（≥）
     */
    String MIN_GUARANTEE_DEPOSIT_LIMITED_BID = "MIN_GUARANTEE_DEPOSIT_LIMITED_BID";

    /**
     * 不限额出价，需缴纳保证金额度不小于（≥）
     */
    String MIN_GUARANTEE_DEPOSIT_UNLIMITED_BID = "MIN_GUARANTEE_DEPOSIT_UNLIMITED_BID";

    /**
     * 冻结保证金百分比
     */
    String FREEZE_GUARANTEE_DEPOSIT_PERCENTAGE = "FREEZE_GUARANTEE_DEPOSIT_PERCENTAGE";

    /**
     * 竞拍商品，平台服务费
     */
    String AUCTION_SERVICE_FEE = "AUCTION_SERVICE_FEE";

    /**
     * 商城/门店寄售商品，平台服务费
     */
    String CONSIGNMENT_SERVICE_FEE = "CONSIGNMENT_SERVICE_FEE";

    /**
     * 商城/门店寄售商品，售买价
     */
    String CONSIGNMENT_SALE_PRICE = "CONSIGNMENT_SALE_PRICE";

    /**
     * 门店寄售周期
     */
    String STORE_CONSIGNMENT_PERIOD_DAYS = "STORE_CONSIGNMENT_PERIOD_DAYS";

    /**
     * 商品继续拍卖次数
     */
    String CONTINUE_AUCTION_TIMES = "CONTINUE_AUCTION_TIMES";

    /**
     * 继续竞拍等待周期
     */
    String CONTINUE_BIDDING_WAIT_PERIOD_MILLIS = "CONTINUE_BIDDING_WAIT_PERIOD_MILLIS";

    /**
     * 买家未付款，扣除保证金金额
     */
    String BUYER_UNPAID_DEPOSIT_DEDUCTION = "BUYER_UNPAID_DEPOSIT_DEDUCTION";

    /**
     * 买家未付款，扣除保证金封顶金额
     */
    String BUYER_UNPAID_DEPOSIT_DEDUCTION_CAP = "BUYER_UNPAID_DEPOSIT_DEDUCTION_CAP";

    /**
     * 线下商城购买金额不小于(≥)(大于该金额则走线下支付)
     */
    String MALL_MAX_ONLINE_PAY_AMOUNT = "MALL_MAX_ONLINE_PAY_AMOUNT";

    /**
     * 线下缴保证金金额不小于(≥)(大于该金额则走线下支付)
     */
    String DEPOSIT_MAX_ONLINE_PAY_AMOUNT = "DEPOSIT_MAX_ONLINE_PAY_AMOUNT";

    /**
     * 线下竟拍出价金额不小于(≥)(大于该金额则走线下支付)
     */
    String ACUTION_MAX_ONLINE_PAY_AMOUNT = "ACUTION_MAX_ONLINE_PAY_AMOUNT";

    /**
     * 商城寄售最小金额
     */
    String ONLINE_CONSIGN_MIN_SELL_AMOUNT = "ONLINE_CONSIGN_MIN_SELL_AMOUNT";

    /**
     * N小时后未自提自动退款
     */
    String ORDER_PICKUP_N_TIME_REFUND = "ORDER_PICKUP_N_TIME_REFUND";

    /**
     * 黄金店铺缴纳消保金金额
     */
    String DEPOSIT_SHOP_LEVEL_ONE = "DEPOSIT_SHOP_LEVEL_ONE";

    /**
     * 钻石店铺缴纳消保金金额
     */
    String DEPOSIT_SHOP_LEVEL_TWO = "DEPOSIT_SHOP_LEVEL_TWO";

    /**
     * 皇冠店铺缴纳消保金金额
     */
    String DEPOSIT_SHOP_LEVEL_THREE = "DEPOSIT_SHOP_LEVEL_THREE";

    /**
     * 黄金个人缴纳消保金金额
     */
    String DEPOSIT_PERSON_LEVEL_ONE = "DEPOSIT_PERSON_LEVEL_ONE";

    /**
     * 钻石个人缴纳消保金金额
     */
    String DEPOSIT_PERSON_LEVEL_TWO = "DEPOSIT_PERSON_LEVEL_TWO";

    /**
     * 皇冠个人缴纳消保金金额
     */
    String DEPOSIT_PERSON_LEVEL_THREE = "DEPOSIT_PERSON_LEVEL_THREE";

    /**
     * 商户藏品销售金额≤
     */
    String DEPOSIT_SHOP_DEDUCT_LE = "DEPOSIT_SHOP_DEDUCT_LE";

    /**
     * 商户小于等于N扣除消保金额=藏品销售金额的
     */
    String DEPOSIT_SHOP_DEDUCT_LE_RATE = "DEPOSIT_SHOP_DEDUCT_LE_RATE";

    /**
     * 商户藏品销售金额＞
     */
    String DEPOSIT_SHOP_DEDUCT_GT = "DEPOSIT_SHOP_DEDUCT_GT";

    /**
     * 商户大于N扣除消保金额=藏品销售金额的
     */
    String DEPOSIT_SHOP_DEDUCT_GT_RATE = "DEPOSIT_SHOP_DEDUCT_GT_RATE";

    /**
     * 个人藏品销售金额≤
     */
    String DEPOSIT_PERSON_DEDUCT_LE = "DEPOSIT_PERSON_DEDUCT_LE";

    /**
     * 个人小于等于N扣除消保金额=藏品销售金额的
     */
    String DEPOSIT_PERSON_DEDUCT_LE_RATE = "DEPOSIT_PERSON_DEDUCT_LE_RATE";

    /**
     * 个人藏品销售金额＞
     */
    String DEPOSIT_PERSON_DEDUCT_GT = "DEPOSIT_PERSON_DEDUCT_GT";

    /**
     * 个人大于N扣除消保金额=藏品销售金额的
     */
    String DEPOSIT_PERSON_DEDUCT_GT_RATE = "DEPOSIT_PERSON_DEDUCT_GT_RATE";


    // 账户提现配置 withdrawal_setting
    /**
     * 每日可提现次数
     */
    
    String DAY_WITHDRAWAL_NUM = "DAY_WITHDRAWAL_NUM";

    /**
     * 提现手续费（元）
     */
    String WITHDRAWAL_FEE_FIXED = "WITHDRAWAL_FEE_FIXED";
    
}
