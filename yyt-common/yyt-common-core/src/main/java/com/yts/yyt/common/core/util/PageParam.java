package com.yts.yyt.common.core.util;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import com.yts.yyt.common.core.constant.CommonConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Description: 分页参数
 * <AUTHOR>
 * @date 2025/1/7
 */
@Data
@Schema(description = "分页参数")
public class PageParam implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long pageNo = 1L;
    private Long pageSize = 10L;

    public PageParam() {

    }
    public PageParam(Long pageNo, Long pageSize) {
        this.pageNo = pageNo;
        this.pageSize= pageSize;
    }
}
