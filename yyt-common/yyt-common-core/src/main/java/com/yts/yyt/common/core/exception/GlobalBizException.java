package com.yts.yyt.common.core.exception;

/**
 * 全局公共业务异常
 */
public class GlobalBizException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    protected String msg;
    protected int code = 500;
    public GlobalBizException(){
        super();
    }

    public GlobalBizException(String msg) {
        super(msg);
        this.msg = msg;
    }

    public GlobalBizException(String msg, Throwable e) {
        super(msg, e);
        this.msg = msg;
    }

    public GlobalBizException(String msg, int code) {
        super(msg);
        this.msg = msg;
        this.code = code;
    }

    public GlobalBizException(String msg, int code, Throwable e) {
        super(msg, e);
        this.msg = msg;
        this.code = code;
    }

    public String getMsg() {
        return this.msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public int getCode() {
        return this.code;
    }

    public void setCode(int code) {
        this.code = code;
    }

}
