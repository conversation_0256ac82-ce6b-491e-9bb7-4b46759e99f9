package com.yts.yyt.common.core.dto;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * idDto
 */
@Data
public class IdDTO implements Serializable {
    private static final long serialVersionUID = 552142084431129588L;
    
    private String id;
    
    private List<String> ids;

	public static Boolean idAndIdsIsEmpty(IdDTO dto){
		if (ObjUtil.isNull(dto) || (CollUtil.isEmpty(dto.getIds()) && StrUtil.isBlank(dto.getId()))) {
			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}
	public static Boolean idIsEmpty(IdDTO dto){
		if (ObjUtil.isNull(dto) || StrUtil.isBlank(dto.getId())) {
			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}

	public static List<Long> mergeList(IdDTO dto){
		List<String> ids = CollUtil.newArrayList(dto.getIds());
		if (StrUtil.isNotBlank(dto.getId())) {
			ids.add(dto.getId());
		}
		//转换成Long
		return ids.stream().map(Long::parseLong).toList();
	}
}
