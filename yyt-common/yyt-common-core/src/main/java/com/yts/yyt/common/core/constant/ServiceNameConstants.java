/*
 *
 *      Copyright (c) 2018-2025, yyt All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: yyt
 *
 */

package com.yts.yyt.common.core.constant;

/**
 * <AUTHOR>
 * @date 2018年06月22日16:41:01 服务名称
 */
public interface ServiceNameConstants {

	/**
	 * 认证中心
	 */
	String AUTH_SERVICE = "yyt-auth";

	/**
	 * UMPS模块
	 */
	String UPMS_SERVICE = "yyt-upms-biz";

	/**
	 * app服务
	 */
	String APP_SERVER = "yyt-app-server-biz";

	/**
	 * 商品服务
	 */
	String GOODS_SERVER = "yyt-goods-biz";

	/**
	 * 流程引擎
	 */
	String FLOW_ENGINE_SERVER = "yyt-flow-engine-biz";

	/**
	 * 流程工单
	 */
	String FLOW_TASK_SERVER = "yyt-flow-task-biz";

	/**
	 * 代码生成模块
	 */
	String CODEGEN_SERVICE = "yyt-codegen";

	/**
	 * 用户服务
	 */
	String BIZ_USER_SERVER = "yyt-user-biz";

	/**
	 * 订单服务
	 */
	String ORDER_SERVER = "yyt-order-biz";

	String MERCHANT_SERVER = "yyt-merchant-biz";
	String TICKET_SERVER = "yyt-ticket-biz";

	String PAY_SERVER = "yyt-pay-platform-biz";

	String DISTRIBUTION_SERVER = "yyt-distribution-biz";

}
