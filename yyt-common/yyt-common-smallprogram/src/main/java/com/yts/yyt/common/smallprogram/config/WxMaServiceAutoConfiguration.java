package com.yts.yyt.common.smallprogram.config;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceOkHttpImpl;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties(SmallProgramProperties.class)
public class WxMaServiceAutoConfiguration {


    @Autowired
    private SmallProgramProperties smallProgramProperties;


    @Bean
    public WxMaService buildWxMaService(){
        WxMaService wxMaService = new WxMaServiceOkHttpImpl();
        WxMaDefaultConfigImpl wxMaDefaultConfig = new WxMaDefaultConfigImpl();
        wxMaDefaultConfig.setAppid(smallProgramProperties.getAppId());
        wxMaDefaultConfig.setSecret(smallProgramProperties.getAppSecret());
        wxMaService.setWxMaConfig(wxMaDefaultConfig);
        return wxMaService;
    }
}
