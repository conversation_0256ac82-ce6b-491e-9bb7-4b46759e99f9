package com.yts.yyt.common.smallprogram.request;

import lombok.Data;

@Data
public class ShippingListRequest {

    /**
     *物流单号，物流快递发货时必填，示例值: 323244567777 字符字节限制: [1, 128]
     */
    private String trackingNo;
    /**
     *物流公司编码，快递公司ID，参见「查询物流公司编码列表」，物流快递发货时必填， 示例值: DHL 字符字节限制: [1, 128]
     */
    private String expressCompany;
    /**
     *商品信息，例如：微信红包抱枕*1个，限120个字以内
     */
    private String itemDesc;
    /**
     *联系方式，当发货的物流公司为顺丰时，联系方式为必填，收件人或寄件人联系方式二选一
     */
    private ContactRequest contact;

}
