package com.yts.yyt.common.rocketmq.config;

import com.yts.yyt.common.rocketmq.properties.RocketMQProperties;
import org.apache.rocketmq.client.core.RocketMQClientTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties(RocketMQProperties.class)
public class RocketMqAutoConfiguration {

    @Autowired
    private ApplicationContext applicationContext;

    @Bean("rocketMQTemplateWithdraw")
    public RocketMQClientTemplate rocketMQTemplateWithdraw() {
        return (RocketMQClientTemplate)applicationContext.getBean("rocketMQClientTemplate");
    }
}
