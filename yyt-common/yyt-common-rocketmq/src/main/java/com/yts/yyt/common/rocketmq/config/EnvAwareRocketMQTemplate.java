package com.yts.yyt.common.rocketmq.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.ClientException;
import org.apache.rocketmq.client.apis.producer.SendReceipt;
import org.apache.rocketmq.client.apis.producer.Transaction;
import org.apache.rocketmq.client.common.Pair;
import org.apache.rocketmq.client.core.RocketMQClientTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Map;

@Component
@Slf4j
public class EnvAwareRocketMQTemplate {
    private final Map<String, RocketMQClientTemplate> rocketMQTemplates;
    private final RocketMQEnvHandler envHandler;
    private final String defaultTemplateName;
    private final ApplicationContext applicationContext;

    @Autowired
    public EnvAwareRocketMQTemplate(Map<String, RocketMQClientTemplate> rocketMQTemplates,
                                    RocketMQEnvHandler envHandler,
                                    ApplicationContext applicationContext) {
        this.rocketMQTemplates = rocketMQTemplates;
        this.envHandler = envHandler;
        this.applicationContext = applicationContext;
        this.defaultTemplateName = "rocketMQClientTemplate"; // 默认模板名称
    }

    private RocketMQClientTemplate getTemplate(String templateName) {
        RocketMQClientTemplate template = rocketMQTemplates.get(templateName);
        if (template == null) {
            return rocketMQTemplates.get(defaultTemplateName);
        }
        return template;
    }
    private RocketMQClientTemplate getTemplate(Class<? extends RocketMQClientTemplate> templateClass) {
        try {
            return applicationContext.getBean(templateClass);
        } catch (Exception e) {
            return getTemplate(defaultTemplateName);
        }
    }

    public void convertAndSend(Class<? extends RocketMQClientTemplate> templateClass, String baseTopic, Object payload) {
        String envTopic = envHandler.getEnvTopic(baseTopic);
        getTemplate(templateClass).convertAndSend(envTopic, payload);
    }

    public void convertAndSend(String baseTopic, Object payload) {
        String envTopic = envHandler.getEnvTopic(baseTopic);
        getTemplate(defaultTemplateName).convertAndSend(envTopic, payload);
    }

    public void send(Class<? extends RocketMQClientTemplate> templateClass, String baseTopic, Message<?> message) {
        String envTopic = envHandler.getEnvTopic(baseTopic);
        getTemplate(templateClass).send(envTopic, message);
    }

    public void send(String baseTopic, Message<?> message) {
        String envTopic = envHandler.getEnvTopic(baseTopic);
        getTemplate(defaultTemplateName).send(envTopic, message);
    }

    public void convertAndSend(Class<? extends RocketMQClientTemplate> templateClass, String baseTopic, String tag, Object payload) {
        String destination = envHandler.getEnvDestination(baseTopic, tag);
        getTemplate(templateClass).convertAndSend(destination, payload);
    }

    public void convertAndSend(String baseTopic, String tag, Object payload) {
        String destination = envHandler.getEnvDestination(baseTopic, tag);
        getTemplate(defaultTemplateName).convertAndSend(destination, payload);
    }

    public SendReceipt sendDelayMsg(Class<? extends RocketMQClientTemplate> templateClass, String baseTopic, String tag,
            Object payload, Duration delayTime) {
        String destination = envHandler.getEnvDestination(baseTopic, tag);
        return getTemplate(templateClass).syncSendDelayMessage(destination, payload, delayTime);
    }
    public SendReceipt sendDelayMsg(String baseTopic, String tag,Object payload, Duration delayTime) {
        String destination = envHandler.getEnvDestination(baseTopic, tag);
        return getTemplate(defaultTemplateName).syncSendDelayMessage(destination, payload, delayTime);
    }

    public SendReceipt sendDelayMsg(Class<? extends RocketMQClientTemplate> templateClass, String baseTopic, Object payload,
            Duration delayTime) {
        String destination = envHandler.getEnvTopic(baseTopic);
        return getTemplate(templateClass).syncSendDelayMessage(destination, payload, delayTime);
    }

    public SendReceipt sendDelayMsg(String baseTopic, Object payload,Duration delayTime) {
        String destination = envHandler.getEnvTopic(baseTopic);
        return getTemplate(defaultTemplateName).syncSendDelayMessage(destination, payload, delayTime);
    }

    public Pair<SendReceipt, Transaction> sendMessageInTransaction(Class<? extends RocketMQClientTemplate> templateClass,
            String baseTopic, String tag, Object payload) throws ClientException {
        return getTemplate(templateClass).sendMessageInTransaction(
            envHandler.getEnvDestination(baseTopic, tag), payload);
    }
    public Pair<SendReceipt, Transaction> sendMessageInTransaction(Class<? extends RocketMQClientTemplate> templateClass,
            String baseTopic, Object payload) throws ClientException {
        return getTemplate(templateClass).sendMessageInTransaction(
            envHandler.getEnvTopic(baseTopic), payload);
    }

}
