package com.yts.yyt.common.tencent.service.impl;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

import org.springframework.stereotype.Service;

import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.tms.v20201229.TmsClient;
import com.tencentcloudapi.tms.v20201229.models.TextModerationRequest;
import com.tencentcloudapi.tms.v20201229.models.TextModerationResponse;
import com.yts.yyt.common.tencent.service.TMSService;
import com.yts.yyt.common.tencent.vo.TextModerationVO;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@AllArgsConstructor
@Service
@Slf4j
public class TMSServiceImpl implements TMSService {

	private final TmsClient tmsClient;
	private static final int MAX_CONTENT_LENGTH = 10000; // 腾讯云文本内容安全服务单次请求最大长度限制

	@Override
	public TextModerationVO textModeration(String... contents) throws TencentCloudSDKException {
		StringBuilder finalContent = new StringBuilder();
		for (String content : contents) {
			if (content != null) {
				finalContent.append(content);
			}
		}

		// 检查内容长度是否超过限制
		if (finalContent.toString().getBytes(StandardCharsets.UTF_8).length > MAX_CONTENT_LENGTH) {
			log.info("文本内容超过最大长度限制，将进行分段处理");
			int halfLength = finalContent.length() / 2;
			String firstHalf = finalContent.substring(0, halfLength);
			String secondHalf = finalContent.substring(halfLength);

			TextModerationVO firstResult = doTextModeration(firstHalf);
			TextModerationVO secondResult = doTextModeration(secondHalf);

			boolean allPassed = firstResult.isPassed() && secondResult.isPassed();
			String sensitiveWords = "";
			if (!firstResult.isPassed() && firstResult.getSensitiveWords() != null) {
				sensitiveWords += firstResult.getSensitiveWords();
			}
			if (!secondResult.isPassed() && secondResult.getSensitiveWords() != null) {
				if (!sensitiveWords.isEmpty()) {
					sensitiveWords += ",";
				}
				sensitiveWords += secondResult.getSensitiveWords();
			}

			return new TextModerationVO(allPassed, sensitiveWords.isEmpty() ? null : sensitiveWords);
		}

		return doTextModeration(finalContent.toString());
	}

	private TextModerationVO doTextModeration(String content) throws TencentCloudSDKException {
		log.info("开始文本内容审核，输入内容：{}", content);
		TextModerationRequest req = new TextModerationRequest();
		String encryptionText = Base64.getEncoder().encodeToString(content.getBytes(StandardCharsets.UTF_8));
		req.setContent(encryptionText);
		req.setBizType("goodsContent_input");

		TextModerationResponse resp = null;
		int retryCount = 0;
		while (retryCount <= 1) {
			try {
				resp = tmsClient.TextModeration(req);
				break;
			} catch (TencentCloudSDKException e) {
				retryCount++;
				log.warn("文本内容审核失败，等待200毫秒后重试，当前重试次数：{}", retryCount);
				try {
					Thread.sleep(200);
				} catch (InterruptedException ie) {
					Thread.currentThread().interrupt();
					throw new TencentCloudSDKException("重试等待被中断", ie);
				}
			}
		}

		String[] keywords = resp.getKeywords();
		// 如果keywords为空，直接返回通过
		if (keywords == null || keywords.length == 0) {
			log.info("[TMS审核] 无敏感词，直接通过，label={}, suggestion={}", resp.getLabel(), resp.getSuggestion());
			return new TextModerationVO(true, null);
		}

		boolean passed = "Pass".equals(resp.getSuggestion());
		StringBuilder sensitiveWordsBuilder = new StringBuilder();
		for (int i = 0; i < keywords.length; i++) {
			sensitiveWordsBuilder.append(keywords[i]);
			if (i < keywords.length - 1) {
				sensitiveWordsBuilder.append(",");
			}
		}
		String sensitiveWords = sensitiveWordsBuilder.length() > 0 ? sensitiveWordsBuilder.toString() : null;

		TextModerationVO result = new TextModerationVO(passed, sensitiveWords);
		log.info("[TMS审核] 审核完成，结果：是否通过={}，敏感词={}，label={}，suggestion={}", 
			passed, sensitiveWords, resp.getLabel(), resp.getSuggestion());
		return result;
	}
}
