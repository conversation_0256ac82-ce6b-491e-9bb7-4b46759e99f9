package com.yts.yyt.common.eid;

import com.tencentcloudapi.faceid.v20180301.models.IdCardVerificationResponse;

import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

/**
 * <p>2025-01-06</p>
 * <p>EID服务.</p>
 *
 * <AUTHOR>
 */
public interface EIDService {

    /**
     * 二要素验证.
     * <p>
     *  返回结果:
     *  <code>
     *     {
     *     "code":"0",//返回码
     *     "message":"成功",//返回码说明
     *     "result":{
     *          "name":"李四",//姓名
     *          "idcard":"******************",//身份证号
     *          "res":"1", //核验结果状态码
     *          "description":"一致",//核验结果状态描述，1 一致；2 不一致；3 无记录
     *          "sex":"男",//性别
     *          "birthday":"19900915",//生日
     *          "address":"河南省邓州市"//籍贯
     *          }
     *     }
     *  </code>
     * </p>
     * <p>
     *    <p> 温馨提示：</p>
     *    <p>   1.解析结果时，先判断code是否等于0，再判断下面result中的res的值；当code不等于0时，表示调用已失败，无需再继续；</p>
     *    <p>   2.出现'无记录'时，有以下几种原因 </p>
     *    <p>  (1)现役军人、武警官兵、特殊部门人员及特殊级别官员；</p>
     *    <p>  (2)退役不到2年的军人和士兵（根据军衔、兵种不同，时间会有所不同，一般为2年）；</p>
     *    <p>  (3)户口迁出，且没有在新的迁入地迁入；</p>
     *    <p>  (4)户口迁入新迁入地，当地公安系统未将迁移信息上报到公安部（上报时间地域不同而有所差异）；</p>
     *    <p>  (5)更改姓名，当地公安系统未将更改信息上报到公安部（上报时间因地域不同而有所差异）；</p>
     *    <p>  (6)移民；</p>
     *    <p>  (7)未更换二代身份证；</p>
     *    <p>  (8)死亡。</p>
     *    <p>  (9)身份证号确实不存在 </p>
     * @param name 名字
     * @param card 身份证
     * @throws NoSuchAlgorithmException 没有这样的算法例外
     * @throws InvalidKeyException      无效的密钥异常
     * @return Boolean
     */
    String check(String name, String card) throws NoSuchAlgorithmException, InvalidKeyException, UnsupportedEncodingException;

    /**
     * {
     *     "Response": {
     *         "Result": "0",
     *         "Description": "姓名和身份证号一致",
     *         "RequestId": "94b54cdf-d975-4718-b091-32f8d79d6397"
     *     }
     * }
     * 
     * 认证结果码，收费情况如下。
     * - 收费结果码：
     * 0: 姓名和身份证号一致
     * -1: 姓名和身份证号不一致
     * 不收费结果码：
     * -2: 非法身份证号（长度、校验位等不正确）
     * -3: 非法姓名（长度、格式等不正确）
     * -4: 证件库服务异常
     * -5: 证件库中无此身份证记录
     * -6: 权威比对系统升级中，请稍后再试
     * -7: 认证次数超过当日限制
     * 示例值：0
     * @param name
     * @param card
     * @return
     */
    IdCardVerificationResponse IdCardVerification(String name , String card);
}
