package com.yts.yyt.common.eid.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;

/**
 * <p>2025-01-06</p>
 * <p>EID配置.</p>
 *
 * <AUTHOR>
 */
@Getter
//@PropertySource(value = "classpath:/application-eid.properties")
public class EIDProperties {

    @Value("${eid.url}")
    private String url;

    @Value("${eid.source}")
    private String source;

    @Value("${eid.secret-id}")
    private String secretId;

    @Value("${eid.secret-key}")
    private String secretKey;
}
