package com.yts.yyt.common.eid.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;

/**
 * 身份信息认证（二要素核验）配置
 * 都是腾讯云sdk,取值与ocr配置一致
 */

@Getter
public class FaceidProperties {
    
    @Value("${ocr.secret-id}")
    private String secretId;

    @Value("${ocr.secret-key}")
    private String secretKey;
    

    @Value("${ocr.region}")
    private String region;
}
