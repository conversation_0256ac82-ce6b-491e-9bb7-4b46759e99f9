<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>yyt-common</artifactId>
		<groupId>com.yts</groupId>
		<version>5.7.0</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.yts</groupId>
	<artifactId>yyt-common-datasource</artifactId>

	<packaging>jar</packaging>

	<description>yyt 动态切换数据源</description>

	<dependencies>
		<dependency>
			<groupId>com.yts</groupId>
			<artifactId>yyt-common-core</artifactId>
		</dependency>
		<!--mybatis-->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
		</dependency>
		<!-- druid 连接池 -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-3-starter</artifactId>
			<scope>provided</scope>
		</dependency>
		<!--拦截器依赖-->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc</artifactId>
		</dependency>
	</dependencies>
</project>
