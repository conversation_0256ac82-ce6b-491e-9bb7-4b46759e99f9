package com.yts.yyt.common.ocr.impl;

import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.ocr.v20181119.OcrClient;
import com.tencentcloudapi.ocr.v20181119.models.BankCardOCRRequest;
import com.tencentcloudapi.ocr.v20181119.models.BankCardOCRResponse;
import com.tencentcloudapi.ocr.v20181119.models.IDCardOCRRequest;
import com.tencentcloudapi.ocr.v20181119.models.IDCardOCRResponse;
import com.yts.yyt.common.ocr.vo.IDCardOcrVO;
import com.yts.yyt.common.ocr.OCRService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

/**
 * <p>2025-01-07</p>
 * <p>ORC实现.</p>
 *
 * <AUTHOR>
 * @implNote 实现说明
 */
@Slf4j
@RequiredArgsConstructor
public class OCRServiceImpl implements OCRService {

    private final OcrClient client;

    @Override
    public IDCardOcrVO IDCardOCR (String base64, String url) throws TencentCloudSDKException {
        IDCardOCRRequest req = new IDCardOCRRequest();
        req.setImageBase64(base64);
        req.setImageUrl(url);
        req.setConfig("""
                    {
                        "CopyWarn":true,
                        "BorderCheckWarn":true,
                        "ReshootWarn":true,
                        "DetectPsWarn":true,
                        "TempIdWarn":true,
                        "InvalidDateWarn":true
                    }
                """
        );
        IDCardOCRResponse response = client.IDCardOCR(req);
        IDCardOcrVO res = new IDCardOcrVO();
        BeanUtils.copyProperties(response, res);
        return res;
    }


    @Override
    public BankCardOCRResponse bankCardOCR(String base64, String url) throws TencentCloudSDKException {
        BankCardOCRRequest request = new BankCardOCRRequest();
        request.setImageBase64(base64);
        request.setImageUrl(url);
        return client.BankCardOCR(request);
    }
}
