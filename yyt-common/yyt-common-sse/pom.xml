<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.yts</groupId>
		<artifactId>yyt-common</artifactId>
		<version>5.7.0</version>
	</parent>

	<artifactId>yyt-common-sse</artifactId>
	<packaging>jar</packaging>

	<description>yyt sse 服务端推送</description>

	<dependencies>
		<dependency>
			<groupId>org.springframework.data</groupId>
			<artifactId>spring-data-redis</artifactId>
		</dependency>

		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-json</artifactId>
		</dependency>

		<dependency>
			<groupId>com.yts</groupId>
			<artifactId>yyt-common-security</artifactId>
		</dependency>
	</dependencies>
</project>
