package com.yts.yyt.common.alert.constans;


/**
 * 异常预警常用类
 */
public class AlertConstans  {

    public final static String WEBHOOK_WECHAT_MESSAGE_TEMPLATE = "{\n" +
            "    \"msgtype\": \"markdown\",\n" +
            "    \"markdown\": {\n" +
            "        \"content\": \"<font color=\\\"warning\\\">程序异常</font>，请相关同事注意。\\n\n" +
            "         >模块: <font color=\\\"info\\\">%s</font>\n" +
            "         >方法: <font color=\\\"info\\\">%s</font>\n" +
            "         >环境: <font color=\\\"info\\\">%s</font>\n" +
            "         >描述: <font color=\\\"info\\\">%s</font>\n" +
            "         >级别: <font color=\\\"info\\\">%s</font>\n" +
            "         >时间: <font color=\\\"info\\\">%s</font>\n" +
            "         >异常: <font color=\\\"info\\\">%s</font>\n" +
            "         >文件: <font color=\\\"info\\\">%s</font>\"\n" +
            "    }\n" +
            "}";

}