package com.yts.yyt.common.mq.idempotent.auto;

import com.yts.yyt.common.mq.idempotent.mapper.MessageDedupMapper;
import com.yts.yyt.common.mq.idempotent.service.IdempotentService;
import com.yts.yyt.common.mq.idempotent.service.impl.IdempotentServiceImpl;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Bean;

@Configuration
@MapperScan("com.yts.yyt.common.mq.idempotent.mapper")
public class IdempotentAutoConfiguration {

    @Bean
    public IdempotentService idempotentService(MessageDedupMapper messageDedupMapper) {
        return new IdempotentServiceImpl(messageDedupMapper);
    }

}
