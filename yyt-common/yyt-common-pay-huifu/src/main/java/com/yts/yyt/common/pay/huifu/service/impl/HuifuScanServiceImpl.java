package com.yts.yyt.common.pay.huifu.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.huifu.bspay.sdk.opps.client.BasePayClient;
import com.huifu.bspay.sdk.opps.core.exception.BasePayException;
import com.huifu.bspay.sdk.opps.core.request.*;
import com.huifu.bspay.sdk.opps.core.utils.DateTools;
import com.huifu.bspay.sdk.opps.core.utils.SequenceTools;
import com.yts.yyt.common.pay.huifu.dto.*;
import com.yts.yyt.common.pay.huifu.enums.HuiFuPayTypeEnum;
import com.yts.yyt.common.pay.huifu.enums.HuiFuTradeStateEnum;
import com.yts.yyt.common.pay.huifu.exception.HuifuException;
import com.yts.yyt.common.pay.huifu.properties.HuifuProperties;
import com.yts.yyt.common.pay.huifu.service.HuifuDelayService;
import com.yts.yyt.common.pay.huifu.service.HuifuScanService;
import com.yts.yyt.common.pay.huifu.util.HuifuResponseParseUtil;
import com.yts.yyt.common.pay.huifu.vo.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
@Tag(name = "汇付天下扫码支付服务")
public class HuifuScanServiceImpl implements HuifuScanService {
    @Autowired
    private HuifuProperties huifuProperties;


    @Override
    @Operation(summary = "扫码支付退款")
    public ScanPayRefundVO scanPayRefund(ScanPayRefundDTO dto) {
        // 2.组装请求参数
        V3TradePaymentScanpayRefundRequest request = new V3TradePaymentScanpayRefundRequest();
        // 请求日期
        request.setReqDate(DateTools.getCurrentDateYYYYMMDD());
        // 请求流水号
        request.setReqSeqId(SequenceTools.getReqSeqId32());
        // 商户号
        request.setHuifuId(huifuProperties.getPlatformHuiFuId());
        // 原交易请求日期
        request.setOrgReqDate(dto.getOrgReqDate());
        // 退款金额
        request.setOrdAmt(dto.getOrdAmt());
        Map<String, Object> extendInfoMap = new HashMap<>();
        extendInfoMap.put("org_hf_seq_id", dto.getOrgHfSeqId());

        //回调地址
        extendInfoMap.put("notify_url", dto.getNotifyUrl() + dto.getRefundOrderId());
        request.setExtendInfo(extendInfoMap);
        // 3. 发起API调用
        String param1 = JSONObject.toJSONString(request);
        log.info("扫码支付退款请求参数: {}", param1);
        ScanPayRefundVO scanPayRefundVO = new ScanPayRefundVO();
        scanPayRefundVO.setTradeSuccess(true);
        scanPayRefundVO.setParam1(param1);
        try {
            Map<String, Object> response = BasePayClient.request(request, false);
            JSONObject res = handleResponse(response);
            scanPayRefundVO.setHuifuId(res.getString("huifu_id"))
                    .setProductId(res.getString("product_id"))
                    .setReqSeqId(res.getString("req_seq_id"))
                    .setHfSeqId(res.getString("hf_seq_id"))
                    .setReqDate(res.getString("req_date"))
                    .setTransDate(res.getString("trans_date"))
                    .setTransStat(res.getString("trans_stat"))
                    .setOrdAmt(res.getString("ord_amt"))
                    .setAcctSplitBunch(res.getString("acct_split_bunch"))
                    .setBankMessage(res.getString("bank_message"))
                    .setRemark(res.getString("remark"))
                    .setRespCode(res.getString("resp_code"))
                    .setRespDesc(res.getString("resp_desc"))
            ;
            if (HuiFuTradeStateEnum.TRADE_FAIL.getType().equals(res.getString("trans_stat"))) {
                scanPayRefundVO.setTradeSuccess(false);
            }
        } catch (BasePayException | IllegalAccessException | HuifuException e) {
            log.error("扫码支付退款请求异常，data:{}", dto);
            log.error(e.getMessage(), e);
            scanPayRefundVO.setTradeSuccess(false);
        }
        return scanPayRefundVO;
    }

    @Override
    @Operation(summary = "扫码支付退款查询")
    public ScanPayRefundQueryVO scanPayRefundQuery(ScanPayRefundQueryDTO dto) {
        // 2.组装请求参数
        V3TradePaymentScanpayRefundqueryRequest request = new V3TradePaymentScanpayRefundqueryRequest();
        // 商户号
        request.setHuifuId(huifuProperties.getPlatformHuiFuId());
        // 原交易请求日期
        request.setOrgReqDate(dto.getOrgReqDate());
        // 原交易请求流水号
        if (StringUtils.hasText(dto.getOrgReqSeqId())) {
            request.setOrgReqSeqId(dto.getOrgReqSeqId());
        }
        // 商户订单号
        if (StringUtils.hasText(dto.getMerOrdId())) {
            request.setMerOrdId(dto.getMerOrdId());
        }
        // 原交易汇付流水号
        request.setOrgHfSeqId(dto.getOrgHfSeqId());

        // 3. 发起API调用
        log.info("扫码支付退款查询请求参数: {}", JSONObject.toJSONString(request));
        ScanPayRefundQueryVO scanPayRefundQueryVO = new ScanPayRefundQueryVO();
        scanPayRefundQueryVO.setTradeSuccess(false);
        try {
            Map<String, Object> response = BasePayClient.request(request, false);
            JSONObject res = handleResponse(response);
            scanPayRefundQueryVO.setOrgReqDate(res.getString("org_req_date"))
                    .setTransDate(res.getString("trans_date"))
                    .setOrdAmt(res.getString("ord_amt"))
                    .setTransStat(res.getString("trans_stat"))
                    .setOrgHfSeqId(res.getString("org_hf_seq_id"))
                    .setBankMessage(res.getString("bank_message"))
                    .setActualRefAmt(res.getString("actual_ref_amt"))
                    .setOrgReqSeqId(res.getString("org_req_seq_id"))
                    .setHuifuId(res.getString("huifu_id"))
                    .setAcctSplitBunch(res.getString("acct_split_bunch"))
                    .setTransFinishTime(res.getString("trans_finish_time"))
                    .setRespCode(res.getString("resp_code"))
                    .setRespDesc(res.getString("resp_desc"))
                    ;
            if (HuiFuTradeStateEnum.TRADE_SUCCESS.getType().equals(res.getString("trans_stat"))) {
                scanPayRefundQueryVO.setTradeSuccess(true);
            }
        } catch (BasePayException | IllegalAccessException | HuifuException e) {
            log.error("扫码支付退款查询请求异常，data:{}", dto);
        }
        return scanPayRefundQueryVO;
    }

    private static JSONObject handleResponse(Map<String, Object> response) {
        log.info("开始处理汇付天下响应， 响应数据: {}", response);

        JSONObject responseObj = JSON.parseObject(JSON.toJSONString(response));

        // 获取响应码和描述
        String respCode = responseObj.getString("resp_code");
        String respDesc = responseObj.getString("resp_desc");

        // 如果respCode 不等于00000000 也不等于00000100 就抛异常
        if (!respCode.equals("00000000") && !respCode.equals("00000100")) {
            log.error("汇付天下响应失败，错误码: {}, 错误描述: {}", respCode, respDesc);
            throw new HuifuException(String.format("错误码: %s, 错误描述: %s", respCode, respDesc));
        }

        return responseObj;
    }
}
