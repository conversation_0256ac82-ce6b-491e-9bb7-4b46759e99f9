package com.yts.yyt.common.pay.huifu.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 结算配置
 *
 */
@Data
@Schema(description = "结算配置")
public class SettleConfig {
	@Schema(description = "最小金额", example = "1.00")
	private String minAmt;

	@Schema(description = "外部结算标志(2-允许)", example = "2")
	private String outSettleFlag;

	@Schema(description = "外部结算汇付ID", example = "6666000105306730")
	private String outSettleHuifuId;

	@Schema(description = "剩余金额", example = "10.00")
	private String remainedAmt;

	@Schema(description = "结算摘要", example = "结算配置")
	private String settleAbstract;

	@Schema(description = "结算批次号", example = "0")
	private String settleBatchNo;

	@Schema(description = "结算周期(T1-次日结算)", example = "T1")
	private String settleCycle;

	@Schema(description = "结算状态(1-正常)", example = "1")
	private String settleStatus;
}
