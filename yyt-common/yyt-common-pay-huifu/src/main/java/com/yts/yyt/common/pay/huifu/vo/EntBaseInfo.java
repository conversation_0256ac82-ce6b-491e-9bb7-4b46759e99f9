package com.yts.yyt.common.pay.huifu.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 企业用户基本信息
 */
@Data
@Schema(description = "企业用户基本信息")
public class EntBaseInfo {
	@Schema(description = "企业用户名称", required = true, maxLength = 128,
			example = "上海汇付支付服务公司")
	private String regName;

	@Schema(description = "营业执照编号", required = true, maxLength = 20,
			example = "92650109MA79R8E308")
	private String licenseCode;

	@Schema(description = "证照有效期类型(1：长期有效；0：非长期有效)", required = true,
			allowableValues = {"0", "1"}, example = "1")
	private String licenseValidityType;

	@Schema(description = "证照有效期起始日期(yyyyMMdd)", required = true, maxLength = 8,
			example = "20220909")
	private String licenseBeginDate;

	@Schema(description = "证照有效期结束日期(yyyyMMdd)", maxLength = 8,
			example = "20420909")
	private String licenseEndDate;

	@Schema(description = "注册地址(省)编码", required = true, maxLength = 6,
			example = "310000")
	private String regProvId;

	@Schema(description = "注册地址(市)编码", required = true, maxLength = 6,
			example = "310100")
	private String regAreaId;

	@Schema(description = "注册地址(区)编码", required = true, maxLength = 6,
			example = "310101")
	private String regDistrictId;

	@Schema(description = "注册地址(详细信息)", required = true, maxLength = 256,
			example = "上海市徐汇区XX路XX号")
	private String regDetail;

	@Schema(description = "法人姓名", required = true, maxLength = 32,
			example = "张三")
	private String legalName;

	@Schema(description = "法人证件类型(参见《自然人证件类型》说明)", required = true, maxLength = 2,
			example = "00")
	private String legalCertType;

	@Schema(description = "法人证件号码", required = true, maxLength = 32,
			example = "3209026198312183829")
	private String legalCertNo;

	@Schema(description = "法人证件有效期类型(1：长期有效；0：非长期有效)", required = true,
			allowableValues = {"0", "1"}, example = "0")
	private String legalCertValidityType;

	@Schema(description = "法人证件有效期开始日期(yyyyMMdd)", required = true, maxLength = 8,
			example = "20220909")
	private String legalCertBeginDate;

	@Schema(description = "法人证件有效期截止日期(yyyyMMdd)", maxLength = 8,
			example = "20340909")
	private String legalCertEndDate;

	@Schema(description = "联系人姓名", required = true, maxLength = 32,
			example = "李四")
	private String contactName;

	@Schema(description = "联系人手机号(11位数字)", required = true, maxLength = 11,
			example = "18611111111")
	private String contactMobileNo;

	@Schema(description = "联系人电子邮箱", maxLength = 64,
			example = "<EMAIL>")
	private String contactEmail;

	@Schema(description = "管理员账号", maxLength = 32,
			example = "cwd10012423")
	private String loginName;
}
