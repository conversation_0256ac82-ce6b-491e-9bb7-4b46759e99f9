package com.yts.yyt.common.pay.huifu.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 个人基础信息
 */
@Data
@Schema(description = "个人基础信息")
public class IndvBaseInfo {
	@Schema(description = "证件有效期开始日期(YYYYMMDD)", example = "20200101")
	private String certBeginDate;

	@Schema(description = "证件有效期结束日期(YYYYMMDD)", example = "29991231")
	private String certEndDate;

	@Schema(description = "证件号码", example = "310112197806081453")
	private String certNo;

	@Schema(description = "证件类型(00-身份证)", example = "00")
	private String certType;

	@Schema(description = "证件有效期类型(1-长期有效)", example = "1")
	private String certValidityType;

	@Schema(description = "电子邮箱", example = "<EMAIL>")
	private String email;

	@Schema(description = "文件列表")
	private List<FileInfo> fileList;

	@Schema(description = "登录名", example = "Lg20220ig3070j5kf48200")
	private String loginName;

	@Schema(description = "手机号", example = "15556622369")
	private String mobileNo;

	@Schema(description = "姓名", example = "王家二")
	private String name;

	@Schema(description = "文件信息")
	public static class FileInfo {
		@Schema(description = "文件ID", example = "99e00421-dad7-3334-9538-4f2ad10612d5")
		private String fileId;

		@Schema(description = "文件名", example = "法人身份证正面.jpg")
		private String fileName;

		@Schema(description = "文件类型(F02-身份证正面)", example = "F02")
		private String fileType;
	}
}
