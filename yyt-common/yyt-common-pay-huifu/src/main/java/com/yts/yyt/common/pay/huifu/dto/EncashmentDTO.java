package com.yts.yyt.common.pay.huifu.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 取现DTO
 *
 * <AUTHOR>
 * @date 2025-01-06 20:45:45
 */
@Data
@Schema(description = "取现DTO")
public class EncashmentDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    public final static String DEFAULT_INTO_ACCDATE_TYPE  = "T1";

    @Schema(description = "取现金额", example = "1.00")
    private String cashAmt;

    @Schema(description = "取现方ID号", example = "6666000109812123")
    private String huifuId;

    @Schema(description = "账户号", example = "F00598600")
    private String acctId;

    @Schema(description = "到账日期类型", example = "D0")
    private String intoAcctDateType;

    @Schema(description = "取现卡序列号", example = "10004053462")
    private String tokenNo;

    @Schema(description = "取现渠道", example = "00")
    private String enchashmentChannel;

    @Schema(description = "备注", example = "备注")
    private String remark;

    @Schema(description = "异步通知地址", example = "http://service.example.com/to/path")
    private String notifyUrl;

	@Schema(description = "业务id")
	private String bizId;
}
