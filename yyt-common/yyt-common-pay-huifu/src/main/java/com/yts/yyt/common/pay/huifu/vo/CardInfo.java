package com.yts.yyt.common.pay.huifu.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 结算银行卡信息
 */
@Data
@Schema(description = "结算银行卡信息")
public class CardInfo {
	@Schema(description = "地区ID", example = "310100")
	private String areaId;

	@Schema(description = "银行代码", example = "********")
	private String bankCode;

	@Schema(description = "支行代码", example = "************")
	private String branchCode;

	@Schema(description = "支行名称", example = "中国建设银行股份有限公司上海五角场支行")
	private String branchName;

	@Schema(description = "持卡人姓名", example = "王家二")
	private String cardName;

	@Schema(description = "银行卡号", example = "6217007210064760000")
	private String cardNo;

	@Schema(description = "卡片类型(0-借记卡)", example = "0")
	private String cardType;

	@Schema(description = "证件有效期开始日期(YYYYMMDD)", example = "********")
	private String certBeginDate;

	@Schema(description = "证件有效期结束日期(YYYYMMDD)", example = "********")
	private String certEndDate;

	@Schema(description = "证件号码", example = "371202198806087518")
	private String certNo;

	@Schema(description = "证件类型(00-身份证)", example = "00")
	private String certType;

	@Schema(description = "证件有效期类型(0-长期有效)", example = "0")
	private String certValidityType;

	@Schema(description = "手机号", example = "13700000214")
	private String mobilePhone;

	@Schema(description = "省份ID", example = "310000")
	private String provId;

	@Schema(description = "令牌编号", example = "10000979883")
	private String tokenNo;
}
