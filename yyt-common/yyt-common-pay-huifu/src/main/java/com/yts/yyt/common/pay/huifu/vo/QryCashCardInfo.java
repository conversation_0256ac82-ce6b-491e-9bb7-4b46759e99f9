package com.yts.yyt.common.pay.huifu.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "取现银行卡信息")
public class QryCashCardInfo {
	@Schema(description = "卡类型(0：对公；1：对私)", allowableValues = {"0", "1"}, example = "1")
	private String cardType;

	@Schema(description = "银行卡户名", maxLength = 128, example = "上海汇付支付服务公司")
	private String cardName;

	@Schema(description = "银行卡号", maxLength = 32, example = "****************")
	private String cardNo;

	@Schema(description = "银行所在市编码", maxLength = 6, example = "310000")
	private String areaId;

	@Schema(description = "银行所在省编码", maxLength = 6, example = "310100")
	private String provId;

	@Schema(description = "银行号", maxLength = 8, example = "********")
	private String bankCode;

	@Schema(description = "银行名称", maxLength = 32, example = "中国工商银行")
	private String bankName;

	@Schema(description = "联行号", maxLength = 12, example = "************")
	private String branchCode;

	@Schema(description = "支行名称", maxLength = 64, example = "中国工商银行上海市中山北路支行")
	private String branchName;

	@Schema(description = "持卡人证件类型(参见《自然人证件类型》说明)", maxLength = 2, example = "00")
	private String certType;

	@Schema(description = "持卡人证件号码", maxLength = 32, example = "320926198412026034")
	private String certNo;

	@Schema(description = "持卡人证件有效期类型(1：长期有效；0：非长期有效)",
			allowableValues = {"0", "1"}, example = "1")
	private String certValidityType;

	@Schema(description = "持卡人证件有效期起始日期(yyyyMMdd)", maxLength = 8, example = "********")
	private String certBeginDate;

	@Schema(description = "持卡人证件有效期截止日期(yyyyMMdd)", maxLength = 8, example = "********")
	private String certEndDate;

	@Schema(description = "银行卡绑定手机号(11位数字)", maxLength = 11, example = "18611111111")
	private String mp;

	@Schema(description = "绑卡序列号", maxLength = 20, example = "10004053462")
	private String tokenNo;

	@Schema(description = "银行卡绑定状态(N:正常 C:关闭)", allowableValues = {"N", "C"}, example = "N")
	private String status;

	@Schema(description = "默认结算卡标志(Y:是 N:否)", allowableValues = {"Y", "N"}, example = "Y")
	private String isSettleDefault;
}
