package com.yts.yyt.common.pay.huifu.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "分账对象")
public class AcctSplitBunchDTO {

    @Schema(description = "百分比分账标志")
    private String percentageFlag;

    @Schema(description = "分账总金额（元）")
    private String totalDivAmt;

    @Schema(description = "分账明细")
    private List<AcctSplitInfoDTO> acctInfos;
}
