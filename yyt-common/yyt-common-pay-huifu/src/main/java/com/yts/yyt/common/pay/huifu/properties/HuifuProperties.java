package com.yts.yyt.common.pay.huifu.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;

import lombok.Data;

@Data
@ConfigurationProperties(prefix = "huifu")
public class HuifuProperties {

    private String rsaPrivateKey;

    private String rsaPublicKey;

    private String procutId;

    private String sysId;

    /**
     * 微信子商户应用ID
     * 子商户在微信申请的应用ID，全局唯一。走聚合正扫发货管理的商户，使用的微信公众号/小程序支付
     */
    private String wxSubAppid;



    /**
     * 交易异步通知地址
     */
    private String paymentNotifyUrl;

    /**
     * 平台汇付虚拟户id
     */
    private String platformHuiFuId;

    /**
     * 扫码支付退款通知地址
     */
    private String scanPayRefundNotifyUrl;
    
    /**
     * 取现回调通知地址
     */
    private String encashmentNotifyUrl;
    
}
