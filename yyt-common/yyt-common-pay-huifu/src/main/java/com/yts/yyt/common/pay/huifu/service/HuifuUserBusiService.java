package com.yts.yyt.common.pay.huifu.service;

import com.yts.yyt.common.pay.huifu.dto.*;
import com.yts.yyt.common.pay.huifu.vo.*;

/**
 * 汇付服务接口
 *
 * 用户业务
 */
public interface HuifuUserBusiService {

    /**
     * 用户业务入驻
     *
     * @param dto 业务入驻信息
     * @return 入驻结果
     */
    UserBusiOpenResultVO userBusiOpen(UserBusiOpenDTO dto);

    /**
     * 查询用户账户余额
     *
     * @param dto 查询参数
     * @return 账户余额信息
     */
    UserBalanceQueryResultVO queryUserBalance(UserBalanceQueryDTO dto);

    /**
     * 查询用户信息
     *
     * @param dto 查询参数
     * @return 用户信息
     */
    UserInfoQueryResultVO queryUserInfo(UserInfoQueryDTO dto);

	/**
	 * 查询用户信息列表
	 *
	 * @param dto 查询参数
	 * @return 用户信息列表
	 */
	UserInfoQueryListResultVO queryUserInfoList(UserInfoQueryListDTO dto);

    /**
     * 用户业务入驻修改
     *
     * @param dto 业务入驻修改信息
     * @return 修改结果
     */
    UserBusiModifyResultVO userBusiModify(UserBusiModifyDTO dto);
}
