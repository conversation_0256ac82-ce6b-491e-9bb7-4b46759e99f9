package com.yts.yyt.common.pay.huifu.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户业务入驻响应结果
 */
@Data
public class UserBusiOpenResultVO {
    /**
     * 响应码
     */
    private String respCode;
    
    /**
     * 响应描述
     */
    private String respDesc;
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 汇付ID
     */
    private String huifuId;

	/**
	 * 取现卡序列号
	 */
	@Schema(description="取现卡序列号；示例值：10004053462")
	private String tokenNo;

} 