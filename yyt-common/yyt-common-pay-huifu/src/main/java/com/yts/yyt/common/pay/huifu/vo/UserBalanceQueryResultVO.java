package com.yts.yyt.common.pay.huifu.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 用户账户余额查询结果VO
 */
@Data
@Schema(description = "用户账户余额查询结果VO")
public class UserBalanceQueryResultVO {

    /**
     * 响应码
     */
    @Schema(description = "响应码")
    private String respCode;

    /**
     * 响应描述
     */
    @Schema(description = "响应描述")
    private String respDesc;

    /**
     * 账户余额信息列表
     */
    @Schema(description = "账户余额信息列表")
    private List<BalanceInfo> balanceInfoList;

    /**
     * 账户余额信息
     */
    @Data
    @Schema(description = "账户余额信息")
    public static class BalanceInfo {
		/**
		 * 商户号
		 */
		@Schema(description = "商户号")
		private String huifuId;
        /**
         * 账户号
         */
        @Schema(description = "账户号")
        private String acctId;
        /**
         * 账户类型
         */
        @Schema(description = "账户类型 01:基本户 02:现金户 03：延时户 04:钱包户 05:充值户 09营销户；")
        private String acctType;

        /**
         * 账户余额
         */
        @Schema(description = "账户余额 balanceAmt=avlBal+frzBal；保留2位小数；示例值：2100.00")
        private String balanceAmt;

        /**
         * 可用余额
         */
        @Schema(description = "可用余额")
        private String avlVal;

        /**
         * 冻结余额
         */
        @Schema(description = "冻结余额")
        private String frzVal;

        /**
         * 昨日日终余额
         */
        @Schema(description = "昨日日终余额")
        private String lastAvlBal;

        /**
         * 状态
         */
        @Schema(description = "状态 N：“正常”; C：“关闭”; F：“冻结”; D：“销户”；")
        private String acctStat;

        /**
         * 管控金额
         */
        @Schema(description = "管控金额")
        private String controlBal;
    }
} 