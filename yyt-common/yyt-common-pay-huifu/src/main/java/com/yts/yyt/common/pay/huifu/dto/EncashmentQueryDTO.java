package com.yts.yyt.common.pay.huifu.dto;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 汇付取现查询请求对象
 */
@Data
@Accessors(chain = true)
public class EncashmentQueryDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 汇付客户Id
     */
    private String huifuId;
    
    /**
     * 原交易请求日期
     * 格式：yyyyMMdd
     */
    private String orgReqDate;
    
    /**
     * 原交易请求流水号
     */
    private String reqSeqId;
} 
