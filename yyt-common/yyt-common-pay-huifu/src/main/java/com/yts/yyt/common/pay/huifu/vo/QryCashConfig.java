package com.yts.yyt.common.pay.huifu.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 取现配置列表
 */
@Data
@Schema(description = "取现配置列表")
public class QryCashConfig {
	@Schema(description = "提现类型(D0-当日到账)", example = "D0")
	private String cashType;

	@Schema(description = "开关状态(1-开启)", example = "1")
	private String switchState;

	@Schema(description = "费率", example = "10.00")
	private String feeRate;

	@Schema(description = "固定金额", example = "0.01")
	private String fixAmt;

	@Schema(description = "D1工作日取现手续费率固定金额")
	private String weekdayFixAmt;

	@Schema(description = "D1工作日取现手续费率")
	private String weekdayFeeRate;

	@Schema(description = "外部提现标志(2-允许)", example = "2")
	private String outCashFlag;

	@Schema(description = "外部提现汇付ID", example = "")
	private String outCashHuifuId;

	@Schema(description = "取现手续费外扣子账户类型", example = "")
	private String outCashAcctType;
}
