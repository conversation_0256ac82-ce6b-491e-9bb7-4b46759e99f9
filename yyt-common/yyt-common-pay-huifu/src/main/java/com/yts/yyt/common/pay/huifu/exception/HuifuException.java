package com.yts.yyt.common.pay.huifu.exception;

import com.yts.yyt.common.core.exception.GlobalBizException;

/**
 * 汇付天下异常
 */
public class HuifuException extends GlobalBizException {

    public HuifuException() {
        super();
    }

    public HuifuException(String msg) {
        super(msg);

    }

    public HuifuException(String msg, Throwable e) {
        super(msg, e);
    }

    public HuifuException(String msg, int code) {
        super(msg, code);

    }

    public HuifuException(HuifuException codeEnum) {
        super(codeEnum.getMsg(), codeEnum.getCode());
    }

    public HuifuException(Integer code, String msg) {
        super(msg, code);

    }

    public HuifuException(String msg, int code, Throwable e) {
        super(msg, code, e);
    }

    public static HuifuException build(HuifuException codeEnum) {
        return new HuifuException(codeEnum.getCode(), codeEnum.getMsg());
    }

    public static void isTrue(boolean condition, HuifuException codeEnum){
        if(condition){
            throw build(codeEnum);
        }
    }

} 