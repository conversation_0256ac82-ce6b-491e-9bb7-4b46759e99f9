package com.yts.yyt.common.security.service;

import com.yts.yyt.admin.api.dto.UserInfo;
import com.yts.yyt.admin.api.feign.RemoteUserService;
import com.yts.yyt.common.core.constant.SecurityConstants;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.core.util.RetOps;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
public class YytMobileUserDetailServiceImpl implements YytUserDetailsService {

	private final UserDetailsService yytDefaultUserDetailsServiceImpl;

	private final RemoteUserService remoteUserService;

	@Override
	@SneakyThrows
	public UserDetails loadUserByUsername(String phone) {
		R<UserInfo> result = remoteUserService.social(phone);
		return getUserDetails(RetOps.of(result).getData());
	}

	@Override
	public UserDetails loadUserByUser(YytUser yytUser) {
		return yytDefaultUserDetailsServiceImpl.loadUserByUsername(yytUser.getUsername());
	}

	/**
	 * 支持所有的 mobile 类型
	 * @param clientId 目标客户端
	 * @param grantType 授权类型
	 * @return true/false
	 */
	@Override
	public boolean support(String clientId, String grantType) {
		return SecurityConstants.GRANT_MOBILE.equals(grantType);
	}

}
